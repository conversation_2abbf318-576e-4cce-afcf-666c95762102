# Database Update for Email Field

To make the email field optional in the users table, you need to run the SQL script provided in this directory.

## Option 1: Using phpMyAdmin

1. Open phpMyAdmin
2. Select your salon database (usually `salon_db`)
3. Click on the "SQL" tab
4. Copy and paste the contents of `update_email_field.sql` into the SQL query box
5. Click "Go" to execute the query

## Option 2: Using MySQL Command Line

1. Open a command prompt or terminal
2. Connect to your MySQL server:
   ```
   mysql -u your_username -p
   ```
3. Enter your password when prompted
4. Select your database:
   ```
   USE salon_db;
   ```
5. Run the SQL commands:
   ```
   ALTER TABLE users MODIFY email VARCHAR(100) NULL;
   ALTER TABLE users DROP INDEX email;
   CREATE UNIQUE INDEX email_unique ON users(email) WHERE email IS NOT NULL;
   ```

## What This Update Does

This update modifies the `email` column in the `users` table to allow NULL values, while still maintaining uniqueness for non-NULL values. This allows you to create customers without providing an email address.

## Alternative Solution

If you prefer not to modify the database structure, the system has been updated to use a permanent placeholder email for customers who don't provide an email address:

```
<EMAIL>
```

If this email is already in use, the system will automatically append a number to make it unique:

```
<EMAIL>
<EMAIL>
...
```

This approach uses a consistent, easy-to-recognize email format for customers without email addresses, making it easier to identify and manage these accounts.
