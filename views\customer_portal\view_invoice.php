<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-file-invoice"></i> Invoice #<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?>
        </h1>
        <div>
            <a href="<?= base_url('customer/invoices') ?>" class="btn btn-outline-primary me-2">
                <i class="fas fa-arrow-left"></i> Back to Invoices
            </a>
            <a href="<?= base_url('billing/print/' . $invoice['id']) ?>" target="_blank" class="btn btn-primary">
                <i class="fas fa-print"></i> Print Invoice
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Invoice Details -->
        <div class="col-md-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Invoice Details</h5>
                </div>
                <div class="card-body">
                    <!-- Invoice Header -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h4 class="mb-3">Invoice Information</h4>
                            <div class="mb-2">
                                <span class="text-muted">Invoice Number:</span>
                                <span class="fw-bold">#<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?></span>
                            </div>
                            <div class="mb-2">
                                <span class="text-muted">Date:</span>
                                <span class="fw-bold"><?= format_date($invoice['invoice_date']) ?></span>
                            </div>
                            <div class="mb-2">
                                <span class="text-muted">Status:</span>
                                <span class="badge <?= get_payment_status_class($invoice['payment_status']) ?>">
                                    <?= ucfirst($invoice['payment_status']) ?>
                                </span>
                            </div>
                            <div class="mb-2">
                                <span class="text-muted">Payment Method:</span>
                                <span class="fw-bold"><?= ucfirst($invoice['payment_method']) ?></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h4 class="mb-3">Customer Information</h4>
                            <div class="mb-2">
                                <span class="text-muted">Name:</span>
                                <span class="fw-bold"><?= $invoice['customer_name'] ?></span>
                            </div>
                            <div class="mb-2">
                                <span class="text-muted">Email:</span>
                                <span class="fw-bold"><?= $invoice['customer_email'] ?></span>
                            </div>
                            <div class="mb-2">
                                <span class="text-muted">Phone:</span>
                                <span class="fw-bold"><?= $invoice['customer_phone'] ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <h4 class="mb-3">Invoice Items</h4>
                    <div class="table-responsive mb-4">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Service</th>
                                    <th>Staff</th>
                                    <th class="text-end">Price</th>
                                    <th class="text-center">Qty</th>
                                    <th class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($invoice['services'] as $service): ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold"><?= $service['service_name'] ?></div>
                                            <?php if ($service['discount_percentage'] > 0): ?>
                                                <div class="small text-success">
                                                    <?= $service['discount_percentage'] ?>% discount applied
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= $service['staff_name'] ?></td>
                                        <td class="text-end"><?= format_currency($service['unit_price']) ?></td>
                                        <td class="text-center"><?= $service['quantity'] ?></td>
                                        <td class="text-end"><?= format_currency($service['total_price']) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                                
                                <?php if (isset($invoice['products']) && !empty($invoice['products'])): ?>
                                    <?php foreach ($invoice['products'] as $product): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?= $product['product_name'] ?> (Product)</div>
                                                <?php if ($product['discount_percentage'] > 0): ?>
                                                    <div class="small text-success">
                                                        <?= $product['discount_percentage'] ?>% discount applied
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>-</td>
                                            <td class="text-end"><?= format_currency($product['unit_price']) ?></td>
                                            <td class="text-center"><?= $product['quantity'] ?></td>
                                            <td class="text-end"><?= format_currency($product['total_price']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Invoice Summary -->
                    <div class="row">
                        <div class="col-md-6">
                            <?php if (!empty($invoice['notes'])): ?>
                                <div class="mb-4">
                                    <h5>Notes</h5>
                                    <div class="p-3 bg-light rounded">
                                        <?= nl2br($invoice['notes']) ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <div class="bg-light p-3 rounded">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Subtotal:</span>
                                    <span class="fw-bold"><?= format_currency($invoice['subtotal']) ?></span>
                                </div>
                                <?php if ($invoice['discount_amount'] > 0): ?>
                                    <div class="d-flex justify-content-between mb-2 text-success">
                                        <span>Discount:</span>
                                        <span class="fw-bold">-<?= format_currency($invoice['discount_amount']) ?></span>
                                    </div>
                                <?php endif; ?>
                                <?php if ($invoice['tax_amount'] > 0): ?>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Tax:</span>
                                        <span class="fw-bold"><?= format_currency($invoice['tax_amount']) ?></span>
                                    </div>
                                <?php endif; ?>
                                <hr>
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="fw-bold">Total:</span>
                                    <span class="fw-bold fs-5"><?= format_currency($invoice['total_amount']) ?></span>
                                </div>
                                <?php if ($invoice['payment_status'] == 'partial'): ?>
                                    <div class="d-flex justify-content-between mb-2 text-success">
                                        <span>Paid:</span>
                                        <span class="fw-bold"><?= format_currency($invoice['paid_amount']) ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2 text-danger">
                                        <span>Balance Due:</span>
                                        <span class="fw-bold"><?= format_currency($invoice['total_amount'] - $invoice['paid_amount']) ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-md-4 mb-4">
            <!-- Payment Status -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Payment Status</h5>
                </div>
                <div class="card-body text-center">
                    <?php if ($invoice['payment_status'] == 'paid'): ?>
                        <div class="mb-3">
                            <div class="icon-circle bg-success text-white mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                                <i class="fas fa-check"></i>
                            </div>
                            <h4 class="text-success">Paid</h4>
                            <p class="text-muted">Thank you for your payment!</p>
                        </div>
                        <div class="alert alert-success">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="text-start">
                                    <p class="mb-0">This invoice has been fully paid. You can print or download it for your records.</p>
                                </div>
                            </div>
                        </div>
                    <?php elseif ($invoice['payment_status'] == 'partial'): ?>
                        <div class="mb-3">
                            <div class="icon-circle bg-info text-white mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                                <i class="fas fa-hourglass-half"></i>
                            </div>
                            <h4 class="text-info">Partially Paid</h4>
                            <p class="text-muted">Balance due: <?= format_currency($invoice['total_amount'] - $invoice['paid_amount']) ?></p>
                        </div>
                        <div class="alert alert-info">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="text-start">
                                    <p class="mb-0">This invoice has been partially paid. Please contact the salon to complete the payment.</p>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="mb-3">
                            <div class="icon-circle bg-warning text-white mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                                <i class="fas fa-exclamation"></i>
                            </div>
                            <h4 class="text-warning">Unpaid</h4>
                            <p class="text-muted">Amount due: <?= format_currency($invoice['total_amount']) ?></p>
                        </div>
                        <div class="alert alert-warning">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="text-start">
                                    <p class="mb-0">This invoice is unpaid. Please contact the salon to make a payment.</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('billing/print/' . $invoice['id']) ?>" target="_blank" class="btn btn-primary">
                            <i class="fas fa-print"></i> Print Invoice
                        </a>
                        <a href="tel:+1234567890" class="btn btn-outline-primary">
                            <i class="fas fa-phone"></i> Contact Salon
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.icon-circle {
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}
</style>

<?php
// Helper function
function get_payment_status_class($status) {
    switch ($status) {
        case 'paid':
            return 'bg-success';
        case 'partial':
            return 'bg-info';
        case 'unpaid':
            return 'bg-warning';
        default:
            return 'bg-secondary';
    }
}
?>

