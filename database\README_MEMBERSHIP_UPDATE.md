# Database Update for Membership Table

To add the missing columns to the customer_memberships table, you need to run the SQL script provided in this directory.

## Option 1: Using phpMyAdmin

1. Open phpMyAdmin
2. Select your salon database (usually `salon_db`)
3. Click on the "SQL" tab
4. Copy and paste the contents of `update_membership_table.sql` into the SQL query box
5. Click "Go" to execute the query

## Option 2: Using MySQL Command Line

1. Open a command prompt or terminal
2. Connect to your MySQL server:
   ```
   mysql -u your_username -p
   ```
3. Enter your password when prompted
4. Select your database:
   ```
   USE salon_db;
   ```
5. Run the SQL script:
   ```
   source path/to/update_membership_table.sql
   ```

## What This Update Does

This update adds two columns to the `customer_memberships` table:
1. `payment_method` - An ENUM field to store the payment method (cash, card, upi, other)
2. `amount_paid` - A DECIMAL field to store the amount paid for the membership

These columns are needed for the membership functionality to work properly.

## Alternative Solution

If you prefer not to modify the database structure, the system has been updated to work without these columns. The code will automatically detect if the columns exist and adjust its behavior accordingly.

However, adding these columns is recommended for full functionality.
