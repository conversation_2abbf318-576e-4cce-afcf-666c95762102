<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-plus-circle"></i> Create Coupon</h1>
        <div>
            <a href="<?= base_url('coupons') ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Coupons
            </a>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('coupons/store') ?>" method="post">
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-12 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-info-circle me-2"></i>Basic Information</h5>
                    </div>

                    <!-- Coupon Code -->
                    <div class="col-md-6 mb-3">
                        <label for="code" class="form-label">Coupon Code <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="code" name="code" required>
                            <button class="btn btn-outline-secondary" type="button" id="generate-code">Generate</button>
                        </div>
                        <div class="form-text">Customers will enter this code to apply the discount</div>
                    </div>

                    <!-- Status -->
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>

                    <!-- Description -->
                    <div class="col-md-12 mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                        <div class="form-text">Internal description for reference</div>
                    </div>

                    <!-- Discount Details -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-tag me-2"></i>Discount Details</h5>
                    </div>

                    <!-- Discount Type -->
                    <div class="col-md-4 mb-3">
                        <label for="discount_type" class="form-label">Discount Type <span class="text-danger">*</span></label>
                        <select class="form-select" id="discount_type" name="discount_type" required>
                            <option value="percentage">Percentage</option>
                            <option value="fixed">Fixed Amount</option>
                        </select>
                    </div>

                    <!-- Discount Value -->
                    <div class="col-md-4 mb-3">
                        <label for="discount_value" class="form-label">Discount Value <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="discount_value" name="discount_value" step="0.01" min="0" required>
                            <span class="input-group-text" id="discount-symbol">%</span>
                        </div>
                    </div>

                    <!-- Max Discount (for percentage) -->
                    <div class="col-md-4 mb-3" id="max-discount-container">
                        <label for="max_discount" class="form-label">Maximum Discount</label>
                        <div class="input-group">
                            <span class="input-group-text"><?= html_entity_decode(CURRENCY_SYMBOL, ENT_HTML5, "UTF-8") ?></span>
                            <input type="number" class="form-control" id="max_discount" name="max_discount" step="0.01" min="0">
                        </div>
                        <div class="form-text">Leave empty for no limit</div>
                    </div>

                    <!-- Minimum Purchase -->
                    <div class="col-md-4 mb-3">
                        <label for="min_purchase" class="form-label">Minimum Purchase</label>
                        <div class="input-group">
                            <span class="input-group-text"><?= html_entity_decode(CURRENCY_SYMBOL, ENT_HTML5, "UTF-8") ?></span>
                            <input type="number" class="form-control" id="min_purchase" name="min_purchase" step="0.01" min="0">
                        </div>
                        <div class="form-text">Leave empty for no minimum</div>
                    </div>

                    <!-- Usage Limit -->
                    <div class="col-md-4 mb-3">
                        <label for="usage_limit" class="form-label">Usage Limit</label>
                        <input type="number" class="form-control" id="usage_limit" name="usage_limit" min="0">
                        <div class="form-text">Leave empty for unlimited usage</div>
                    </div>

                    <!-- Validity Period -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-calendar-alt me-2"></i>Validity Period</h5>
                    </div>

                    <!-- Valid From -->
                    <div class="col-md-6 mb-3">
                        <label for="valid_from" class="form-label">Valid From</label>
                        <input type="date" class="form-control" id="valid_from" name="valid_from" value="<?= date('Y-m-d') ?>">
                        <div class="form-text">Leave empty to start immediately</div>
                    </div>

                    <!-- Valid To -->
                    <div class="col-md-6 mb-3">
                        <label for="valid_to" class="form-label">Valid To</label>
                        <input type="date" class="form-control" id="valid_to" name="valid_to">
                        <div class="form-text">Leave empty for no expiry</div>
                    </div>

                    <!-- Applicability -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-filter me-2"></i>Applicability</h5>
                    </div>

                    <!-- Applies To -->
                    <div class="col-md-12 mb-3">
                        <label class="form-label">Applies To</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="applies_to" id="applies_to_all" value="all" checked>
                            <label class="form-check-label" for="applies_to_all">
                                All Products and Services
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="applies_to" id="applies_to_services" value="services">
                            <label class="form-check-label" for="applies_to_services">
                                Services Only
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="applies_to" id="applies_to_products" value="products">
                            <label class="form-check-label" for="applies_to_products">
                                Products Only
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="applies_to" id="applies_to_specific" value="specific">
                            <label class="form-check-label" for="applies_to_specific">
                                Specific Items
                            </label>
                        </div>
                    </div>

                    <!-- Specific Items -->
                    <div class="col-md-12 mb-3" id="specific-items-container" style="display: none;">
                        <div class="card">
                            <div class="card-body">
                                <ul class="nav nav-tabs" id="itemTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="services-tab" data-bs-toggle="tab" data-bs-target="#services-content" type="button" role="tab" aria-controls="services-content" aria-selected="true">Services</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="products-tab" data-bs-toggle="tab" data-bs-target="#products-content" type="button" role="tab" aria-controls="products-content" aria-selected="false">Products</button>
                                    </li>
                                </ul>
                                <div class="tab-content p-3" id="itemTabsContent">
                                    <div class="tab-pane fade show active" id="services-content" role="tabpanel" aria-labelledby="services-tab">
                                        <div class="row">
                                            <?php foreach ($services as $service): ?>
                                                <div class="col-md-4 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="service_<?= $service['id'] ?>" name="applicable_items[]" value="service_<?= $service['id'] ?>">
                                                        <label class="form-check-label" for="service_<?= $service['id'] ?>">
                                                            <?= $service['name'] ?> (<?= format_currency($service['price']) ?>)
                                                        </label>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="products-content" role="tabpanel" aria-labelledby="products-tab">
                                        <div class="row">
                                            <?php foreach ($products as $product): ?>
                                                <div class="col-md-4 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="product_<?= $product['id'] ?>" name="applicable_items[]" value="product_<?= $product['id'] ?>">
                                                        <label class="form-check-label" for="product_<?= $product['id'] ?>">
                                                            <?= $product['name'] ?> (<?= format_currency($product['price']) ?>)
                                                        </label>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-md-12 mt-4 text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-save me-2"></i> Create Coupon
                        </button>
                        <a href="<?= base_url('coupons') ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Generate random coupon code
    document.getElementById('generate-code').addEventListener('click', function() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let code = '';
        for (let i = 0; i < 8; i++) {
            code += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        document.getElementById('code').value = code;
    });

    // Toggle discount symbol based on discount type
    const discountType = document.getElementById('discount_type');
    const discountSymbol = document.getElementById('discount-symbol');
    const maxDiscountContainer = document.getElementById('max-discount-container');

    discountType.addEventListener('change', function() {
        if (this.value === 'percentage') {
            discountSymbol.textContent = '%';
            maxDiscountContainer.style.display = 'block';
        } else {
            discountSymbol.textContent = '<?= html_entity_decode(CURRENCY_SYMBOL, ENT_HTML5, "UTF-8") ?>';
            maxDiscountContainer.style.display = 'none';
        }
    });

    // Toggle specific items container based on applies_to
    const appliesTo = document.querySelectorAll('input[name="applies_to"]');
    const specificItemsContainer = document.getElementById('specific-items-container');

    appliesTo.forEach(function(radio) {
        radio.addEventListener('change', function() {
            if (this.value === 'specific') {
                specificItemsContainer.style.display = 'block';
            } else {
                specificItemsContainer.style.display = 'none';
            }
        });
    });
});
</script>
