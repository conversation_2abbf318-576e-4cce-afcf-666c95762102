/**
 * Main JavaScript file for Salon Management System
 */

// Document ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize Bootstrap popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide only success alerts after 5 seconds
    setTimeout(function() {
        var successAlerts = document.querySelectorAll('.alert-success');
        successAlerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
        // Error, warning, and info alerts will remain visible until dismissed
    }, 5000);

    // Service selection in booking
    var serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(function(card) {
        card.addEventListener('click', function() {
            // Remove selected class from all cards
            serviceCards.forEach(function(c) {
                c.classList.remove('selected');
            });

            // Add selected class to clicked card
            this.classList.add('selected');

            // Set hidden input value
            document.getElementById('selected_service').value = this.dataset.serviceId;
        });
    });

    // Staff selection in booking
    var staffCards = document.querySelectorAll('.staff-card');
    staffCards.forEach(function(card) {
        card.addEventListener('click', function() {
            // Remove selected class from all cards
            staffCards.forEach(function(c) {
                c.classList.remove('selected');
            });

            // Add selected class to clicked card
            this.classList.add('selected');

            // Set hidden input value
            document.getElementById('selected_staff').value = this.dataset.staffId;
        });
    });

    // Time slot selection in booking
    var timeSlots = document.querySelectorAll('.time-slot:not(.disabled)');
    timeSlots.forEach(function(slot) {
        slot.addEventListener('click', function() {
            // Remove selected class from all slots
            timeSlots.forEach(function(s) {
                s.classList.remove('selected');
            });

            // Add selected class to clicked slot
            this.classList.add('selected');

            // Set hidden input value
            document.getElementById('selected_time').value = this.dataset.time;
        });
    });

    // Coupon code application
    var applyCouponBtn = document.getElementById('apply_coupon');
    if (applyCouponBtn) {
        applyCouponBtn.addEventListener('click', function() {
            var couponCode = document.getElementById('coupon_code').value;
            var subtotal = parseFloat(document.getElementById('subtotal').value);

            if (couponCode.trim() === '') {
                alert('Please enter a coupon code');
                return;
            }

            // AJAX request to validate coupon
            fetch(getUrl('validate-coupon.php'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'coupon_code=' + encodeURIComponent(couponCode) + '&subtotal=' + subtotal
            })
            .then(response => response.json())
            .then(data => {
                if (data.valid) {
                    document.getElementById('discount_amount').value = data.discount_amount;
                    document.getElementById('discount_display').textContent = data.discount_amount;
                    document.getElementById('total_amount').value = data.total_amount;
                    document.getElementById('total_display').textContent = data.total_amount;

                    // Show success message
                    document.getElementById('coupon_message').innerHTML = '<div class="alert alert-success">Coupon applied successfully!</div>';
                } else {
                    // Show error message
                    document.getElementById('coupon_message').innerHTML = '<div class="alert alert-danger">' + data.message + '</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('coupon_message').innerHTML = '<div class="alert alert-danger">An error occurred. Please try again.</div>';
            });
        });
    }

    // Product quantity change in billing
    var quantityInputs = document.querySelectorAll('.product-quantity');
    quantityInputs.forEach(function(input) {
        input.addEventListener('change', function() {
            var productId = this.dataset.productId;
            var quantity = parseInt(this.value);
            var price = parseFloat(this.dataset.price);

            // Update total price
            var totalElement = document.getElementById('product_total_' + productId);
            var total = quantity * price;
            totalElement.textContent = total.toFixed(2);

            // Update hidden input
            document.getElementById('product_quantity_' + productId).value = quantity;

            // Recalculate subtotal
            calculateSubtotal();
        });
    });

    // Calculate subtotal function
    function calculateSubtotal() {
        var subtotal = 0;

        // Add service totals
        var serviceTotals = document.querySelectorAll('.service-total');
        serviceTotals.forEach(function(element) {
            subtotal += parseFloat(element.textContent);
        });

        // Add product totals
        var productTotals = document.querySelectorAll('.product-total');
        productTotals.forEach(function(element) {
            subtotal += parseFloat(element.textContent);
        });

        // Update subtotal display
        document.getElementById('subtotal_display').textContent = subtotal.toFixed(2);
        document.getElementById('subtotal').value = subtotal.toFixed(2);

        // Recalculate tax
        var taxRate = parseFloat(document.getElementById('tax_rate').value);
        var taxAmount = subtotal * (taxRate / 100);
        document.getElementById('tax_display').textContent = taxAmount.toFixed(2);
        document.getElementById('tax_amount').value = taxAmount.toFixed(2);

        // Get discount
        var discountAmount = parseFloat(document.getElementById('discount_amount').value || 0);

        // Calculate total
        var total = subtotal + taxAmount - discountAmount;
        document.getElementById('total_display').textContent = total.toFixed(2);
        document.getElementById('total_amount').value = total.toFixed(2);
    }

    // Date picker initialization
    var datePickers = document.querySelectorAll('.datepicker');
    datePickers.forEach(function(picker) {
        picker.addEventListener('change', function() {
            // If this is the appointment date picker, fetch available time slots
            if (this.id === 'appointment_date') {
                var selectedDate = this.value;
                var serviceId = document.getElementById('selected_service').value;
                var staffId = document.getElementById('selected_staff').value;

                if (selectedDate && serviceId && staffId) {
                    fetchAvailableTimeSlots(selectedDate, serviceId, staffId);
                }
            }
        });
    });

    // Fetch available time slots
    function fetchAvailableTimeSlots(date, serviceId, staffId) {
        var timeSlotsContainer = document.getElementById('time_slots_container');

        if (!timeSlotsContainer) {
            return;
        }

        // Show loading
        timeSlotsContainer.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';

        // AJAX request to get available time slots
        fetch(getUrl('get-time-slots.php'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'date=' + encodeURIComponent(date) + '&service_id=' + serviceId + '&staff_id=' + staffId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Render time slots
                var html = '<div class="row">';

                if (data.time_slots.length === 0) {
                    html = '<div class="alert alert-info">No available time slots for the selected date.</div>';
                } else {
                    data.time_slots.forEach(function(slot) {
                        var disabledClass = slot.available ? '' : 'disabled';
                        html += '<div class="col-md-3 col-6">';
                        html += '<div class="time-slot ' + disabledClass + '" data-time="' + slot.time + '">';
                        html += slot.formatted_time;
                        html += '</div>';
                        html += '</div>';
                    });
                    html += '</div>';
                }

                timeSlotsContainer.innerHTML = html;

                // Reinitialize time slot click events
                var timeSlots = document.querySelectorAll('.time-slot:not(.disabled)');
                timeSlots.forEach(function(slot) {
                    slot.addEventListener('click', function() {
                        // Remove selected class from all slots
                        timeSlots.forEach(function(s) {
                            s.classList.remove('selected');
                        });

                        // Add selected class to clicked slot
                        this.classList.add('selected');

                        // Set hidden input value
                        document.getElementById('selected_time').value = this.dataset.time;
                    });
                });
            } else {
                timeSlotsContainer.innerHTML = '<div class="alert alert-danger">' + data.message + '</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            timeSlotsContainer.innerHTML = '<div class="alert alert-danger">An error occurred. Please try again.</div>';
        });
    }

    // Print invoice
    var printInvoiceBtn = document.getElementById('print_invoice');
    if (printInvoiceBtn) {
        printInvoiceBtn.addEventListener('click', function() {
            window.print();
        });
    }
});
