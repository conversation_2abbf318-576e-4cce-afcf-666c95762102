<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-file-invoice-dollar"></i> Payroll Details</h1>
        <div>
            <a href="<?= base_url('payroll/print/' . $payroll['id']) ?>" class="btn btn-info" target="_blank">
                <i class="fas fa-print"></i> Print Payslip
            </a>
            <a href="<?= base_url('payroll/edit/' . $payroll['id']) ?>" class="btn btn-primary ms-2">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="<?= base_url('payroll') ?>" class="btn btn-outline-primary ms-2">
                <i class="fas fa-arrow-left"></i> Back to Payroll
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Payroll Summary -->
        <div class="col-md-4 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Payroll Summary</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="border-bottom pb-2">Staff Information</h6>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Name:</span>
                            <span class="fw-bold"><?= $payroll['staff_name'] ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Email:</span>
                            <span><?= $payroll['staff_email'] ?></span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="border-bottom pb-2">Pay Period</h6>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Start Date:</span>
                            <span><?= format_date($payroll['pay_period_start']) ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">End Date:</span>
                            <span><?= format_date($payroll['pay_period_end']) ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Payment Date:</span>
                            <span><?= format_date($payroll['payment_date']) ?></span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="border-bottom pb-2">Payment Details</h6>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Method:</span>
                            <span><?= ucfirst(str_replace('_', ' ', $payroll['payment_method'])) ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Status:</span>
                            <span>
                                <?php if ($payroll['status'] == 'paid'): ?>
                                    <span class="badge bg-success">Paid</span>
                                <?php elseif ($payroll['status'] == 'pending'): ?>
                                    <span class="badge bg-warning">Pending</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary"><?= ucfirst($payroll['status']) ?></span>
                                <?php endif; ?>
                            </span>
                        </div>
                    </div>
                    
                    <?php if (!empty($payroll['notes'])): ?>
                        <div class="mb-3">
                            <h6 class="border-bottom pb-2">Notes</h6>
                            <p class="mb-0"><?= $payroll['notes'] ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Salary Breakdown -->
        <div class="col-md-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Salary Breakdown</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Earnings</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Basic Salary</span>
                                        <span><?= format_currency($payroll['basic_salary']) ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Commission</span>
                                        <span><?= format_currency($payroll['commission_amount']) ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Bonus</span>
                                        <span><?= format_currency($payroll['bonus']) ?></span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between fw-bold">
                                        <span>Total Earnings</span>
                                        <span><?= format_currency($payroll['basic_salary'] + $payroll['commission_amount'] + $payroll['bonus']) ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card border mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Deductions</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Deductions</span>
                                        <span><?= format_currency($payroll['deductions']) ?></span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between fw-bold">
                                        <span>Total Deductions</span>
                                        <span><?= format_currency($payroll['deductions']) ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-12">
                            <div class="card border bg-light">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">Net Salary</h5>
                                        <h4 class="mb-0 text-primary"><?= format_currency($payroll['net_salary']) ?></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Commission Details -->
            <?php if (!empty($commissions)): ?>
                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Commission Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Service</th>
                                        <th>Service Amount</th>
                                        <th>Commission Rate</th>
                                        <th>Commission Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($commissions as $commission): ?>
                                        <tr>
                                            <td><?= $commission['service_name'] ?></td>
                                            <td><?= format_currency($commission['service_amount']) ?></td>
                                            <td><?= $commission['commission_rate'] ?>%</td>
                                            <td><?= format_currency($commission['commission_amount']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-light">
                                        <th colspan="3" class="text-end">Total Commission:</th>
                                        <th><?= format_currency($payroll['commission_amount']) ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
