-- Wallet System Tables
-- This file creates the necessary tables for the customer wallet system

-- Customer Wallets Table
CREATE TABLE IF NOT EXISTS customer_wallets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL UNIQUE,
    balance DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    INDEX idx_customer_id (customer_id)
);

-- Wallet Transactions Table
CREATE TABLE IF NOT EXISTS wallet_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    transaction_type ENUM('credit', 'debit') NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    balance_before DECIMAL(10, 2) NOT NULL,
    balance_after DECIMAL(10, 2) NOT NULL,
    reference_type ENUM('manual_add', 'invoice_payment', 'refund', 'adjustment') NOT NULL,
    reference_id INT NULL COMMENT 'Invoice ID for payments, NULL for manual additions',
    description TEXT,
    created_by INT NOT NULL COMMENT 'User ID who performed the transaction',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_reference_type (reference_type),
    INDEX idx_created_at (created_at)
);

-- Add wallet_amount column to invoices table to track wallet payments
ALTER TABLE invoices 
ADD COLUMN wallet_amount DECIMAL(10, 2) NOT NULL DEFAULT 0.00 AFTER discount_amount;

-- Update the payment_method enum to include wallet option
ALTER TABLE invoices 
MODIFY COLUMN payment_method ENUM('cash', 'card', 'upi', 'wallet', 'mixed', 'other') NOT NULL DEFAULT 'cash';
