<?php
/**
 * Wallet Controller
 * Handles customer wallet management
 */
class WalletController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            flash('error', 'Please login to access this page');
            header('Location: ' . base_url('login'));
            exit;
        }

        // Check if user has admin, manager, or staff role
        if (!has_role(['admin', 'manager', 'staff'])) {
            flash('error', 'You do not have permission to access this page');
            header('Location: ' . base_url());
            exit;
        }
    }

    /**
     * Display wallet overview
     */
    public function index() {
        // Get filter parameters
        $search = input('search');
        $min_balance = input('min_balance');
        $max_balance = input('max_balance');

        // Get wallet data
        $wallet_model = new WalletModel();
        $wallets = $wallet_model->getAllWithCustomerDetails($search, $min_balance, $max_balance);

        // Get wallet statistics
        $stats = $wallet_model->getStatistics();

        // Render view
        $this->render('wallet/index', [
            'wallets' => $wallets,
            'stats' => $stats,
            'search' => $search,
            'min_balance' => $min_balance,
            'max_balance' => $max_balance
        ]);
    }

    /**
     * Display add money form
     */
    public function addMoney($customer_id = null) {
        // Get customers for dropdown
        $customer_model = new CustomerModel();
        $customers = $customer_model->getAllWithUsers();

        // If customer_id is provided, get customer details
        $selected_customer = null;
        if ($customer_id) {
            $selected_customer = $customer_model->getWithUser($customer_id);
            if (!$selected_customer) {
                flash('error', 'Customer not found');
                $this->redirect(base_url('wallet'));
            }
        }

        // Render view
        $this->render('wallet/add_money', [
            'customers' => $customers,
            'selected_customer' => $selected_customer
        ]);
    }

    /**
     * Process add money request
     */
    public function storeAddMoney() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('wallet'));
        }

        // Get form data
        $customer_id = input('customer_id');
        $amount = input('amount');
        $description = input('description');

        // Validate data
        if (empty($customer_id)) {
            flash('error', 'Please select a customer');
            $this->redirect(base_url('wallet/add-money'));
        }

        if (empty($amount) || !is_numeric($amount) || $amount <= 0) {
            flash('error', 'Please enter a valid amount');
            $this->redirect(base_url('wallet/add-money/' . $customer_id));
        }

        // Verify customer exists
        $customer_model = new CustomerModel();
        $customer = $customer_model->find($customer_id);
        if (!$customer) {
            flash('error', 'Customer not found');
            $this->redirect(base_url('wallet'));
        }

        // Add money to wallet
        $wallet_model = new WalletModel();
        $result = $wallet_model->addMoney($customer_id, $amount, $description, $_SESSION['user_id']);

        if (!$result) {
            flash('error', 'Failed to add money to wallet');
            $this->redirect(base_url('wallet/add-money/' . $customer_id));
        }

        flash('success', 'Money added to wallet successfully');
        $this->redirect(base_url('wallet/view/' . $customer_id));
    }

    /**
     * View customer wallet details
     */
    public function view($customer_id) {
        // Get customer details
        $customer_model = new CustomerModel();
        $customer = $customer_model->getWithUser($customer_id);

        if (!$customer) {
            flash('error', 'Customer not found');
            $this->redirect(base_url('wallet'));
        }

        // Get wallet details
        $wallet_model = new WalletModel();
        $wallet = $wallet_model->getByCustomerId($customer_id);
        
        // Get transaction history
        $transactions = $wallet_model->getTransactionHistory($customer_id);

        // Render view
        $this->render('wallet/view', [
            'customer' => $customer,
            'wallet' => $wallet,
            'transactions' => $transactions
        ]);
    }

    /**
     * Adjust wallet balance (for corrections)
     */
    public function adjustBalance($customer_id) {
        // Get customer details
        $customer_model = new CustomerModel();
        $customer = $customer_model->getWithUser($customer_id);

        if (!$customer) {
            flash('error', 'Customer not found');
            $this->redirect(base_url('wallet'));
        }

        // Get current wallet balance
        $wallet_model = new WalletModel();
        $wallet = $wallet_model->getByCustomerId($customer_id);

        // Render view
        $this->render('wallet/adjust_balance', [
            'customer' => $customer,
            'wallet' => $wallet
        ]);
    }

    /**
     * Process balance adjustment
     */
    public function storeAdjustment($customer_id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('wallet/view/' . $customer_id));
        }

        // Get form data
        $adjustment_type = input('adjustment_type'); // 'add' or 'subtract'
        $amount = input('amount');
        $reason = input('reason');

        // Validate data
        if (empty($adjustment_type) || !in_array($adjustment_type, ['add', 'subtract'])) {
            flash('error', 'Please select a valid adjustment type');
            $this->redirect(base_url('wallet/adjust-balance/' . $customer_id));
        }

        if (empty($amount) || !is_numeric($amount) || $amount <= 0) {
            flash('error', 'Please enter a valid amount');
            $this->redirect(base_url('wallet/adjust-balance/' . $customer_id));
        }

        if (empty($reason)) {
            flash('error', 'Please provide a reason for the adjustment');
            $this->redirect(base_url('wallet/adjust-balance/' . $customer_id));
        }

        // Verify customer exists
        $customer_model = new CustomerModel();
        $customer = $customer_model->find($customer_id);
        if (!$customer) {
            flash('error', 'Customer not found');
            $this->redirect(base_url('wallet'));
        }

        // Process adjustment
        $wallet_model = new WalletModel();
        
        if ($adjustment_type == 'add') {
            $result = $wallet_model->addMoney($customer_id, $amount, 'Adjustment: ' . $reason, $_SESSION['user_id'], 'adjustment');
        } else {
            $result = $wallet_model->deductMoney($customer_id, $amount, 'Adjustment: ' . $reason, $_SESSION['user_id'], 'adjustment');
        }

        if (!$result) {
            flash('error', 'Failed to adjust wallet balance');
            $this->redirect(base_url('wallet/adjust-balance/' . $customer_id));
        }

        flash('success', 'Wallet balance adjusted successfully');
        $this->redirect(base_url('wallet/view/' . $customer_id));
    }

    /**
     * Get wallet balance for AJAX requests
     */
    public function getBalance($customer_id) {
        // Set JSON header
        header('Content-Type: application/json');

        // Verify customer exists
        $customer_model = new CustomerModel();
        $customer = $customer_model->find($customer_id);
        if (!$customer) {
            echo json_encode(['error' => 'Customer not found']);
            exit;
        }

        // Get wallet balance
        $wallet_model = new WalletModel();
        $wallet = $wallet_model->getByCustomerId($customer_id);

        echo json_encode([
            'success' => true,
            'balance' => $wallet ? $wallet['balance'] : 0,
            'formatted_balance' => format_currency($wallet ? $wallet['balance'] : 0)
        ]);
        exit;
    }

    /**
     * Transaction history for a specific customer
     */
    public function transactions($customer_id) {
        // Get customer details
        $customer_model = new CustomerModel();
        $customer = $customer_model->getWithUser($customer_id);

        if (!$customer) {
            flash('error', 'Customer not found');
            $this->redirect(base_url('wallet'));
        }

        // Get filter parameters
        $date_from = input('date_from');
        $date_to = input('date_to');
        $transaction_type = input('transaction_type');

        // Get transaction history
        $wallet_model = new WalletModel();
        $transactions = $wallet_model->getTransactionHistory($customer_id, $date_from, $date_to, $transaction_type);

        // Get wallet details
        $wallet = $wallet_model->getByCustomerId($customer_id);

        // Render view
        $this->render('wallet/transactions', [
            'customer' => $customer,
            'wallet' => $wallet,
            'transactions' => $transactions,
            'date_from' => $date_from,
            'date_to' => $date_to,
            'transaction_type' => $transaction_type
        ]);
    }
}
