<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-history"></i> <?= $customer['name'] ?>'s Transaction History</h1>
        <div>
            <a href="<?= base_url('wallet/view/' . $customer['id']) ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Wallet
            </a>
        </div>
    </div>

    <!-- Customer Info & Current Balance -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-user"></i> Customer Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Name:</strong><br>
                            <span class="text-muted"><?= $customer['name'] ?></span>
                        </div>
                        <div class="col-md-3">
                            <strong>Email:</strong><br>
                            <span class="text-muted"><?= $customer['email'] ?></span>
                        </div>
                        <div class="col-md-3">
                            <strong>Phone:</strong><br>
                            <span class="text-muted"><?= $customer['phone'] ?></span>
                        </div>
                        <div class="col-md-3">
                            <strong>Customer ID:</strong><br>
                            <span class="text-muted">#<?= $customer['id'] ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-wallet"></i> Current Balance</h5>
                </div>
                <div class="card-body text-center">
                    <h2 class="text-<?= $wallet && $wallet['balance'] > 0 ? 'success' : 'muted' ?>">
                        <?= format_currency($wallet ? $wallet['balance'] : 0) ?>
                    </h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-filter"></i> Filter Transactions</h5>
        </div>
        <div class="card-body">
            <form method="GET" action="<?= base_url('wallet/transactions/' . $customer['id']) ?>">
                <div class="row">
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="<?= $date_from ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="<?= $date_to ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="transaction_type" class="form-label">Transaction Type</label>
                        <select class="form-select" id="transaction_type" name="transaction_type">
                            <option value="">All Types</option>
                            <option value="credit" <?= $transaction_type == 'credit' ? 'selected' : '' ?>>Credit (Money Added)</option>
                            <option value="debit" <?= $transaction_type == 'debit' ? 'selected' : '' ?>>Debit (Money Deducted)</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="<?= base_url('wallet/transactions/' . $customer['id']) ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Transaction History -->
    <div class="card shadow-sm">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-list"></i> Transaction History</h5>
            <span class="badge bg-primary"><?= count($transactions) ?> transactions</span>
        </div>
        <div class="card-body">
            <?php if (empty($transactions)): ?>
                <div class="text-center py-5">
                    <img src="<?= base_url('assets/images/empty-transactions.svg') ?>" alt="No Transactions" style="width: 120px; opacity: 0.5;" class="mb-3">
                    <h5>No Transactions Found</h5>
                    <p class="text-muted">No transactions match your filter criteria.</p>
                    <a href="<?= base_url('wallet/add-money/' . $customer['id']) ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Money to Wallet
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th>Date & Time</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Balance Before</th>
                                <th>Balance After</th>
                                <th>Description</th>
                                <th>Reference</th>
                                <th>By</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($transactions as $index => $transaction): ?>
                                <tr>
                                    <td><?= $index + 1 ?></td>
                                    <td>
                                        <div class="fw-bold"><?= format_date($transaction['created_at']) ?></div>
                                        <div class="small text-muted"><?= format_time($transaction['created_at']) ?></div>
                                    </td>
                                    <td>
                                        <?php if ($transaction['transaction_type'] == 'credit'): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-plus"></i> Credit
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">
                                                <i class="fas fa-minus"></i> Debit
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-<?= $transaction['transaction_type'] == 'credit' ? 'success' : 'danger' ?>">
                                            <?= $transaction['transaction_type'] == 'credit' ? '+' : '-' ?><?= format_currency($transaction['amount']) ?>
                                        </span>
                                    </td>
                                    <td><?= format_currency($transaction['balance_before']) ?></td>
                                    <td>
                                        <span class="fw-bold text-<?= $transaction['balance_after'] > 0 ? 'success' : 'muted' ?>">
                                            <?= format_currency($transaction['balance_after']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-muted"><?= $transaction['description'] ?: 'No description' ?></span>
                                    </td>
                                    <td>
                                        <?php if ($transaction['reference_type'] == 'invoice_payment' && $transaction['reference_id']): ?>
                                            <a href="<?= base_url('billing/view/' . $transaction['reference_id']) ?>" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-receipt"></i> Invoice #<?= $transaction['reference_id'] ?>
                                            </a>
                                        <?php elseif ($transaction['reference_type'] == 'manual_add'): ?>
                                            <span class="badge bg-info">Manual Add</span>
                                        <?php elseif ($transaction['reference_type'] == 'adjustment'): ?>
                                            <span class="badge bg-warning">Adjustment</span>
                                        <?php elseif ($transaction['reference_type'] == 'refund'): ?>
                                            <span class="badge bg-secondary">Refund</span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?= $transaction['created_by_name'] ?: 'System' ?></small>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Transaction Summary -->
                <?php
                $total_credits = 0;
                $total_debits = 0;
                foreach ($transactions as $transaction) {
                    if ($transaction['transaction_type'] == 'credit') {
                        $total_credits += $transaction['amount'];
                    } else {
                        $total_debits += $transaction['amount'];
                    }
                }
                ?>
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h6>Total Credits</h6>
                                <h4><?= format_currency($total_credits) ?></h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h6>Total Debits</h6>
                                <h4><?= format_currency($total_debits) ?></h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h6>Net Change</h6>
                                <h4><?= format_currency($total_credits - $total_debits) ?></h4>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

.badge {
    font-size: 0.75rem;
}

.btn-sm {
    font-size: 0.75rem;
}
</style>
