/* Modern Style for Salon Management System */

:root {
    /* Modern Color Palette */
    --primary: #616161;       /* Indigo */
    --primary-dark: #434242;  /* Darker Indigo */
    --primary-light: #a5b4fc; /* Light Indigo */

    --secondary: #ec4899;     /* Pink */
    --secondary-dark: #db2777;/* Darker Pink */
    --secondary-light: #fbcfe8;/* Light Pink */

    --success: #10b981;       /* Emerald */
    --success-dark: #059669;  /* Darker Emerald */
    --success-light: #a7f3d0; /* Light Emerald */

    --warning: #f59e0b;       /* Amber */
    --warning-dark: #d97706;  /* Darker Amber */
    --warning-light: #fcd34d; /* Light Amber */

    --danger: #ef4444;        /* Red */
    --danger-dark: #dc2626;   /* Darker Red */
    --danger-light: #fca5a5;  /* Light Red */

    --info: #4c109a;          /* Blue */
    --info-dark: #454545;     /* Darker Blue */
    --info-light: #dddad0;    /* Light Blue */

    --dark: #1f2937;          /* Gray 800 */
    --gray: #6b7280;          /* Gray 500 */
    --light: #f3f4f6;         /* Gray 100 */
    --white: #ffffff;

    /* Typography */
    --font-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-serif: 'Playfair Display', Georgia, Cambria, 'Times New Roman', Times, serif;
    --font-mono: 'JetBrains Mono', SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
}

/* Base Styles */
body {
    font-family: var(--font-sans);
    background-color: #f9fafb;
    color: var(--dark);
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-serif);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

/* Navbar Styles */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 0.75rem 1rem;
    margin: 0 !important;
    border-radius: 0 !important;
    position: relative;
    z-index: 1000;
}

.navbar-dark {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
}

/* Fix for container inside navbar */
.navbar .container-fluid {
    padding: 0 1rem !important;
    margin: 0 !important;
    max-width: 100% !important;
}

.navbar-brand {
    font-family: var(--font-serif);
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

.navbar-dark .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border-radius: var(--radius-md);
    padding: 0.5rem;
}

.dropdown-item {
    border-radius: var(--radius-sm);
    padding: 0.5rem 1rem;
}

.dropdown-item:hover {
    background-color: var(--light);
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.card-header {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: white;
    font-weight: 600;
    padding: 1rem 1.5rem;
    border-bottom: none;
}

.card-header.bg-success {
    background: linear-gradient(135deg, var(--success), var(--success-dark)) !important;
}

.card-header.bg-warning {
    background: linear-gradient(135deg, var(--warning), var(--warning-dark)) !important;
}

.card-header.bg-danger {
    background: linear-gradient(135deg, var(--danger), var(--danger-dark)) !important;
}

.card-header.bg-info {
    background: linear-gradient(135deg, var(--info), var(--info-dark)) !important;
}

.card-header.bg-secondary {
    background: linear-gradient(135deg, var(--secondary), var(--secondary-dark)) !important;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: rgba(0, 0, 0, 0.02);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.5rem;
}

/* Button Styles */
.btn {
    font-weight: 500;
    padding: 0.5rem 1.25rem;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary));
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
}

.btn-success {
    background: linear-gradient(135deg, var(--success), var(--success-dark));
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning), var(--warning-dark));
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger), var(--danger-dark));
}

.btn-info {
    background: linear-gradient(135deg, var(--info), var(--info-dark));
}

.btn-outline-primary {
    color: var(--primary);
    border: 1px solid var(--primary);
}

.btn-outline-primary:hover {
    background-color: var(--primary);
    color: white;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: var(--radius-md);
    padding: 0.75rem 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--gray);
}

/* Badge Styles */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: var(--radius-full);
}

.bg-primary {
    background-color: var(--primary) !important;
}

.bg-secondary {
    background-color: var(--secondary) !important;
}

.bg-success {
    background-color: var(--success) !important;
}

.bg-warning {
    background-color: var(--warning) !important;
}

.bg-danger {
    background-color: var(--danger) !important;
}

.bg-info {
    background-color: var(--info) !important;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: var(--radius-md);
    padding: 1rem 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.alert-success {
    background-color: var(--success-light);
    color: var(--success-dark);
}

.alert-warning {
    background-color: var(--warning-light);
    color: var(--warning-dark);
}

.alert-danger {
    background-color: var(--danger-light);
    color: var(--danger-dark);
}

.alert-info {
    background-color: var(--info-light);
    color: var(--info-dark);
}

/* Table Styles */
.table {
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.table th {
    background-color: var(--light);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Service Card Styles */
.service-card {
    overflow: hidden;
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.service-card .card-img-top {
    height: 200px;
    object-fit: cover;
    transition: all 0.5s ease;
}

.service-card:hover .card-img-top {
    transform: scale(1.1);
}

.service-card.selected {
    border: 2px solid var(--primary);
    box-shadow: 0 0 0 4px var(--primary-light);
}

/* Staff Card Styles */
.staff-card {
    overflow: hidden;
}

.staff-card .staff-img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    margin: 0 auto;
    border: 4px solid var(--primary-light);
    transition: all 0.3s ease;
}

.staff-card:hover .staff-img {
    transform: scale(1.1);
    border-color: var(--primary);
}

.staff-card.selected {
    border: 2px solid var(--primary);
    box-shadow: 0 0 0 4px var(--primary-light);
}

/* Date and Time Selection Styles */
.date-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: var(--radius-md);
    padding: 1rem;
    text-align: center;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.date-card:hover {
    background-color: var(--light);
}

.date-card.selected {
    background-color: var(--primary);
    color: white;
}

.time-slot {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: var(--radius-md);
    padding: 0.75rem;
    text-align: center;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 0.5rem;
}

.time-slot:hover:not(.disabled) {
    background-color: var(--light);
}

.time-slot.selected {
    background-color: var(--primary);
    color: white;
}

.time-slot.disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
    opacity: 0.7;
}

/* Footer Styles */
footer {
    background: linear-gradient(135deg, var(--dark), #111827);
    color: white;
    padding: 3rem 0;
}

footer a {
    color: var(--light);
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: white;
    text-decoration: underline;
}

footer h5 {
    color: white;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

/* Dashboard Stats Cards */
.stat-card {
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card .icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* Admin Layout with Sidebar */
.admin-layout {
    display: flex;
    min-height: 100vh;
    width: 100%;
}

/* Sidebar Styles */
.sidebar {
    width: 260px;
    background: linear-gradient(135deg, var(--dark), #111827);
    color: var(--light);
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 1000;
    overflow-y: auto;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    gap: 10px;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    color: white;
    text-decoration: none;
    text-align: center;
    font-weight: 600;
    font-size: 1.25rem;
}

.sidebar-brand i {
        font-size: 1.5rem;
}

.sidebar-toggle {
    background: transparent;
    border: none;
    color: var(--light);
    cursor: pointer;
    font-size: 1.25rem;
}

.sidebar-user {
    padding: 1rem;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-user .avatar-circle {
    width: 40px;
    height: 40px;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.sidebar-user-info {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-left: 20px;
}

.sidebar-user-name {
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-user-role {
    font-size: 0.75rem;
    white-space: nowrap;
}

.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
    flex-grow: 1;
}

.sidebar-header {
    padding: 0.75rem 1rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: rgba(255, 255, 255, 0.5);
    margin-top: 1rem;
    gap: 10px;
}

.sidebar-item {
    margin: 0.25rem 0;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border-left-color: var(--primary);
}

.sidebar-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border-left-color: var(--primary);
}

.sidebar-link i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-around;
}

.sidebar-footer-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.sidebar-footer-link:hover {
    color: white;
}

.sidebar-footer-link i {
    margin-right: 0.5rem;
}

/* Main Content Styles */
.main-content {
    flex-grow: 1;
    margin-left: 260px;
    transition: all 0.3s ease;
    width: calc(100% - 260px);
}

.admin-layout.sidebar-collapsed .main-content {
    margin-left: 70px;
    width: calc(100% - 70px);
}

/* Top Navbar Styles */
.top-navbar {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 0.75rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 999;
}

.top-navbar-left {
    display: flex;
    align-items: center;
}

.mobile-sidebar-toggle {
    display: none;
    background: transparent;
    border: none;
    font-size: 1.25rem;
    margin-right: 1rem;
    cursor: pointer;
    color: var(--dark);
}

.top-navbar-right {
    display: flex;
    align-items: center;
}

.top-navbar .avatar-circle {
    width: 36px;
    height: 36px;
    font-size: 1rem;
}

.top-navbar .dropdown-toggle::after {
    display: none;
}

/* Content Wrapper Styles */
.content-wrapper {
    padding: 1.5rem;
    background-color: #f9fafb;
    min-height: calc(100vh - 60px);
}

/* Alert Styles */
.alert {
    border-left-width: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.alert-success {
    border-left-color: var(--success);
}

.alert-danger {
    border-left-color: var(--danger);
}

.alert-warning {
    border-left-color: var(--warning);
}

.alert-info {
    border-left-color: var(--info);
}

.alert .btn-close {
    opacity: 1;
    font-size: 0.8rem;
    padding: 22px;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
        box-shadow: none;
    }

    .sidebar.show {
        transform: translateX(0);
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }

    .mobile-sidebar-toggle {
        display: block;
    }

    .admin-layout.sidebar-collapsed .main-content {
        margin-left: 0;
        width: 100%;
    }
}

@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .navbar-brand {
        font-size: 1.25rem;
    }

    h1 {
        font-size: 1.75rem;
    }

    .display-4 {
        font-size: 2rem;
    }

    .content-wrapper {
        padding: 1rem;
    }

    .top-navbar {
        padding: 0.5rem 1rem;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    body {
        background-color: white;
    }
}
