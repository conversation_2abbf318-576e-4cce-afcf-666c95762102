<?php
/**
 * Stock Model
 */
class StockModel extends Model {
    protected $table = 'stock_history';

    /**
     * Get stock history by product ID
     *
     * @param int $product_id Product ID
     * @return array Stock history
     */
    public function getByProductId($product_id) {
        $query = "SELECT sh.*, u.name as user_name,
                 CASE
                    WHEN sh.quantity_change > 0 THEN 'add'
                    WHEN sh.quantity_change < 0 THEN 'remove'
                    ELSE 'set'
                 END as adjustment_type
                 FROM " . $this->table . " sh
                 JOIN users u ON sh.user_id = u.id
                 WHERE sh.product_id = :product_id
                 ORDER BY sh.created_at DESC";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
