<?php
/**
 * Base Controller class
 */
class Controller {
    /**
     * Render a view
     *
     * @param string $view View file path
     * @param array $data Data to pass to the view
     */
    protected function render($view, $data = []) {
        // Extract data to make variables available in view
        extract($data);

        // Start output buffering
        ob_start();

        // Include view file
        include BASE_PATH . '/views/' . $view . '.php';

        // Get content and clean buffer
        $content = ob_get_clean();

        // Include layout if not an AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] != 'XMLHttpRequest') {
            include BASE_PATH . '/views/layouts/main.php';
        } else {
            echo $content;
        }
    }

    /**
     * Redirect to another URL
     *
     * @param string $url URL to redirect to
     */
    protected function redirect($url) {
        header('Location: ' . $url);
        exit;
    }

    /**
     * Return JSON response
     *
     * @param array $data Data to encode as JSON
     * @param int $status HTTP status code
     */
    protected function json($data, $status = 200) {
        header('Content-Type: application/json');
        http_response_code($status);
        echo json_encode($data);
        exit;
    }

    /**
     * Render a view without the main layout
     *
     * @param string $view View file path
     * @param array $data Data to pass to the view
     */
    protected function renderStandalone($view, $data = []) {
        // Extract data to make variables available in view
        extract($data);

        // Include view file
        include BASE_PATH . '/views/' . $view . '.php';
    }
}

