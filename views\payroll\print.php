<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payslip - <?= $payroll['staff_name'] ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            background-color: #f8f9fa;
        }
        .payslip-container {
            max-width: 800px;
            margin: 20px auto;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .payslip-header {
            padding: 20px;
            border-bottom: 1px solid #ddd;
        }
        .payslip-body {
            padding: 20px;
        }
        .payslip-footer {
            padding: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
        }
        .company-logo {
            max-height: 80px;
        }
        .payslip-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .payslip-subtitle {
            font-size: 16px;
            color: #666;
        }
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #ddd;
        }
        .table-sm {
            font-size: 14px;
        }
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 50px;
            padding-top: 5px;
            width: 200px;
            text-align: center;
        }
        @media print {
            body {
                background-color: #fff;
            }
            .payslip-container {
                box-shadow: none;
                margin: 0;
                max-width: 100%;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row no-print mb-3">
            <div class="col-12 text-end">
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print me-1"></i> Print
                </button>
                <a href="<?= base_url('payroll/view/' . $payroll['id']) ?>" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-arrow-left me-1"></i> Back
                </a>
            </div>
        </div>
        
        <div class="payslip-container">
            <!-- Payslip Header -->
            <div class="payslip-header">
                <div class="row align-items-center">
                    <div class="col-6">
                        <?php if (!empty($settings['logo'])): ?>
                            <img src="<?= base_url('uploads/' . $settings['logo']) ?>" alt="<?= $settings['salon_name'] ?>" class="company-logo">
                        <?php else: ?>
                            <h3><?= $settings['salon_name'] ?></h3>
                        <?php endif; ?>
                    </div>
                    <div class="col-6 text-end">
                        <div class="payslip-title">PAYSLIP</div>
                        <div class="payslip-subtitle">
                            Pay Period: <?= format_date($payroll['pay_period_start']) ?> to <?= format_date($payroll['pay_period_end']) ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Payslip Body -->
            <div class="payslip-body">
                <!-- Employee & Payment Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="section-title">Employee Information</div>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td width="40%"><strong>Name:</strong></td>
                                <td><?= $payroll['staff_name'] ?></td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td><?= $payroll['staff_email'] ?></td>
                            </tr>
                            <tr>
                                <td><strong>Employee ID:</strong></td>
                                <td><?= $payroll['staff_id'] ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <div class="section-title">Payment Information</div>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td width="40%"><strong>Payment Date:</strong></td>
                                <td><?= format_date($payroll['payment_date']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Payment Method:</strong></td>
                                <td><?= ucfirst(str_replace('_', ' ', $payroll['payment_method'])) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <?php if ($payroll['status'] == 'paid'): ?>
                                        <span class="badge bg-success">Paid</span>
                                    <?php elseif ($payroll['status'] == 'pending'): ?>
                                        <span class="badge bg-warning">Pending</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary"><?= ucfirst($payroll['status']) ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Earnings & Deductions -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="section-title">Earnings</div>
                        <table class="table table-sm">
                            <tr>
                                <td>Basic Salary</td>
                                <td class="text-end"><?= format_currency($payroll['basic_salary']) ?></td>
                            </tr>
                            <tr>
                                <td>Commission</td>
                                <td class="text-end"><?= format_currency($payroll['commission_amount']) ?></td>
                            </tr>
                            <tr>
                                <td>Bonus</td>
                                <td class="text-end"><?= format_currency($payroll['bonus']) ?></td>
                            </tr>
                            <tr class="table-light">
                                <th>Total Earnings</th>
                                <th class="text-end"><?= format_currency($payroll['basic_salary'] + $payroll['commission_amount'] + $payroll['bonus']) ?></th>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <div class="section-title">Deductions</div>
                        <table class="table table-sm">
                            <tr>
                                <td>Deductions</td>
                                <td class="text-end"><?= format_currency($payroll['deductions']) ?></td>
                            </tr>
                            <tr class="table-light">
                                <th>Total Deductions</th>
                                <th class="text-end"><?= format_currency($payroll['deductions']) ?></th>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Net Salary -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Net Salary</h5>
                                    <h4 class="mb-0"><?= format_currency($payroll['net_salary']) ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Commission Details -->
                <?php if (!empty($commissions)): ?>
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="section-title">Commission Details</div>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Service</th>
                                            <th class="text-end">Service Amount</th>
                                            <th class="text-end">Commission Rate</th>
                                            <th class="text-end">Commission Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($commissions as $commission): ?>
                                            <tr>
                                                <td><?= $commission['service_name'] ?></td>
                                                <td class="text-end"><?= format_currency($commission['service_amount']) ?></td>
                                                <td class="text-end"><?= $commission['commission_rate'] ?>%</td>
                                                <td class="text-end"><?= format_currency($commission['commission_amount']) ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-light">
                                            <th colspan="3" class="text-end">Total Commission:</th>
                                            <th class="text-end"><?= format_currency($payroll['commission_amount']) ?></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Notes -->
                <?php if (!empty($payroll['notes'])): ?>
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="section-title">Notes</div>
                            <p><?= $payroll['notes'] ?></p>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Signatures -->
                <div class="row mt-5">
                    <div class="col-md-4">
                        <div class="signature-line">
                            Employee Signature
                        </div>
                    </div>
                    <div class="col-md-4 offset-md-4">
                        <div class="signature-line">
                            Authorized Signature
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Payslip Footer -->
            <div class="payslip-footer text-center">
                <p class="mb-0">This is a computer-generated document. No signature is required.</p>
                <p class="mb-0">
                    <?= $settings['salon_name'] ?> | 
                    <?= $settings['address'] ?> | 
                    <?= $settings['phone'] ?> | 
                    <?= $settings['email'] ?>
                </p>
                <p class="mb-0">Generated on <?= format_datetime(date('Y-m-d H:i:s')) ?></p>
            </div>
        </div>
    </div>
</body>
</html>
