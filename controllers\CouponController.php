<?php
/**
 * Coupon Controller
 */
class CouponController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            $this->redirect(base_url('login'));
        }

        // Check if user has permission - temporarily disabled until permission system is implemented
        // if (!has_permission('coupons')) {
        //     flash('error', 'You do not have permission to access coupons');
        //     $this->redirect(base_url('dashboard'));
        // }
    }

    /**
     * Display all coupons
     */
    public function index() {
        // Get all coupons
        $coupon_model = new CouponModel();
        $coupons = $coupon_model->all();

        // Get usage count for each coupon
        foreach ($coupons as &$coupon) {
            $coupon['usage_count'] = $coupon_model->getUsageCount($coupon['id']);
        }

        // Render view
        $this->render('coupons/index', [
            'coupons' => $coupons
        ]);
    }

    /**
     * Display create coupon form
     */
    public function create() {
        // Get services and products for discount options
        $service_model = new ServiceModel();
        $services = $service_model->all();

        $product_model = new ProductModel();
        $products = $product_model->getAllWithCategories();

        // Render view
        $this->render('coupons/create', [
            'services' => $services,
            'products' => $products
        ]);
    }

    /**
     * Store new coupon
     */
    public function store() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('coupons'));
        }

        // Get form data
        $code = input('code');
        $description = input('description');
        $discount_type = input('discount_type');
        $discount_value = input('discount_value');
        $min_purchase = input('min_purchase');
        $max_discount = input('max_discount');
        $valid_from = input('valid_from');
        $valid_to = input('valid_to');
        $usage_limit = input('usage_limit');
        $applies_to = input('applies_to');
        $applicable_items = input('applicable_items');
        $status = input('status');

        // Validate data
        if (empty($code)) {
            flash('error', 'Coupon code is required');
            $this->redirect(base_url('coupons/create'));
        }

        if (empty($discount_type) || empty($discount_value) || !is_numeric($discount_value) || $discount_value <= 0) {
            flash('error', 'Please enter a valid discount value');
            $this->redirect(base_url('coupons/create'));
        }

        if ($discount_type == 'percentage' && $discount_value > 100) {
            flash('error', 'Percentage discount cannot exceed 100%');
            $this->redirect(base_url('coupons/create'));
        }

        // Check if coupon code already exists
        $coupon_model = new CouponModel();
        if ($coupon_model->codeExists($code)) {
            flash('error', 'Coupon code already exists');
            $this->redirect(base_url('coupons/create'));
        }

        // Create coupon
        $coupon_data = [
            'code' => strtoupper($code),
            'description' => $description,
            'discount_type' => $discount_type,
            'discount_value' => $discount_value,
            'min_purchase' => $min_purchase,
            'max_discount' => $max_discount,
            'valid_from' => $valid_from,
            'valid_to' => $valid_to,
            'usage_limit' => $usage_limit,
            'applies_to' => $applies_to,
            'applicable_items' => $applicable_items ? json_encode($applicable_items) : null,
            'status' => $status
        ];

        $result = $coupon_model->create($coupon_data);

        if (!$result) {
            flash('error', 'Failed to create coupon');
            $this->redirect(base_url('coupons/create'));
        }

        flash('success', 'Coupon created successfully');
        $this->redirect(base_url('coupons'));
    }

    /**
     * Display edit coupon form
     */
    public function edit($id) {
        // Get coupon
        $coupon_model = new CouponModel();
        $coupon = $coupon_model->find($id);

        if (!$coupon) {
            flash('error', 'Coupon not found');
            $this->redirect(base_url('coupons'));
        }

        // Parse applicable items
        if (!empty($coupon['applicable_items'])) {
            $coupon['applicable_items'] = json_decode($coupon['applicable_items'], true);
        } else {
            $coupon['applicable_items'] = [];
        }

        // Get services and products for discount options
        $service_model = new ServiceModel();
        $services = $service_model->all();

        $product_model = new ProductModel();
        $products = $product_model->getAllWithCategories();

        // Render view
        $this->render('coupons/edit', [
            'coupon' => $coupon,
            'services' => $services,
            'products' => $products
        ]);
    }

    /**
     * Update coupon
     */
    public function update($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('coupons'));
        }

        // Get coupon
        $coupon_model = new CouponModel();
        $coupon = $coupon_model->find($id);

        if (!$coupon) {
            flash('error', 'Coupon not found');
            $this->redirect(base_url('coupons'));
        }

        // Get form data
        $code = input('code');
        $description = input('description');
        $discount_type = input('discount_type');
        $discount_value = input('discount_value');
        $min_purchase = input('min_purchase');
        $max_discount = input('max_discount');
        $valid_from = input('valid_from');
        $valid_to = input('valid_to');
        $usage_limit = input('usage_limit');
        $applies_to = input('applies_to');
        $applicable_items = input('applicable_items');
        $status = input('status');

        // Validate data
        if (empty($code)) {
            flash('error', 'Coupon code is required');
            $this->redirect(base_url('coupons/edit/' . $id));
        }

        if (empty($discount_type) || empty($discount_value) || !is_numeric($discount_value) || $discount_value <= 0) {
            flash('error', 'Please enter a valid discount value');
            $this->redirect(base_url('coupons/edit/' . $id));
        }

        if ($discount_type == 'percentage' && $discount_value > 100) {
            flash('error', 'Percentage discount cannot exceed 100%');
            $this->redirect(base_url('coupons/edit/' . $id));
        }

        // Check if coupon code already exists (excluding current coupon)
        if (strtoupper($code) != $coupon['code'] && $coupon_model->codeExists($code)) {
            flash('error', 'Coupon code already exists');
            $this->redirect(base_url('coupons/edit/' . $id));
        }

        // Update coupon
        $coupon_data = [
            'code' => strtoupper($code),
            'description' => $description,
            'discount_type' => $discount_type,
            'discount_value' => $discount_value,
            'min_purchase' => $min_purchase,
            'max_discount' => $max_discount,
            'valid_from' => $valid_from,
            'valid_to' => $valid_to,
            'usage_limit' => $usage_limit,
            'applies_to' => $applies_to,
            'applicable_items' => $applicable_items ? json_encode($applicable_items) : null,
            'status' => $status
        ];

        $result = $coupon_model->update($id, $coupon_data);

        if (!$result) {
            flash('error', 'Failed to update coupon');
            $this->redirect(base_url('coupons/edit/' . $id));
        }

        flash('success', 'Coupon updated successfully');
        $this->redirect(base_url('coupons'));
    }

    /**
     * Delete coupon
     */
    public function delete($id) {
        // Get coupon
        $coupon_model = new CouponModel();
        $coupon = $coupon_model->find($id);

        if (!$coupon) {
            flash('error', 'Coupon not found');
            $this->redirect(base_url('coupons'));
        }

        // Delete coupon
        $result = $coupon_model->delete($id);

        if (!$result) {
            flash('error', 'Failed to delete coupon');
            $this->redirect(base_url('coupons'));
        }

        flash('success', 'Coupon deleted successfully');
        $this->redirect(base_url('coupons'));
    }

    /**
     * Validate coupon
     */
    public function validate() {
        // Check if request is AJAX
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            $this->redirect(base_url('coupons'));
        }

        // Get coupon code
        $code = input('code');
        $amount = input('amount');

        if (empty($code)) {
            echo json_encode(['valid' => false, 'message' => 'Please enter a coupon code']);
            exit;
        }

        // Validate coupon
        $coupon_model = new CouponModel();
        $result = $coupon_model->validateCoupon($code, $amount);

        echo json_encode($result);
        exit;
    }
}
