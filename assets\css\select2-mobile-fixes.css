/**
 * Select2 Mobile Fixes
 * 
 * This file contains CSS fixes for Select2 dropdowns on mobile devices
 */

/* Fix Select2 dropdown width on mobile */
@media (max-width: 767.98px) {
    .select2-container {
        width: 100% !important;
    }
    
    /* Fix Select2 dropdown positioning */
    .select2-container--open .select2-dropdown {
        width: 100% !important;
        left: 0 !important;
    }
    
    /* Fix Select2 search box */
    .select2-search__field {
        width: 100% !important;
    }
    
    /* Fix Select2 dropdown height */
    .select2-results {
        max-height: 200px;
    }
    
    /* Fix Select2 selection display */
    .select2-selection__rendered {
        white-space: normal !important;
        word-break: break-word !important;
    }
    
    /* Fix Select2 in input groups */
    .input-group .select2-container {
        flex: 1 1 auto;
        width: 1% !important;
    }
    
    /* Fix Select2 in invoice form */
    #invoice-form .select2-container {
        width: 100% !important;
    }
    
    /* Fix Select2 in service rows */
    .service-row .select2-container,
    .product-row .select2-container {
        width: 100% !important;
    }
    
    /* Fix Select2 dropdown z-index */
    .select2-container--open {
        z-index: 9999;
    }
    
    /* Fix Select2 dropdown width when in modal */
    .modal .select2-container {
        width: 100% !important;
    }
    
    /* Fix Select2 dropdown in input groups */
    .input-group > .select2-container--bootstrap-5 {
        position: relative;
        flex: 1 1 auto;
        width: 1% !important;
        min-width: 0;
    }
    
    /* Fix Select2 dropdown in input groups with buttons */
    .input-group > .select2-container--bootstrap-5:not(:last-child) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
    
    /* Fix Select2 dropdown in input groups with prepend */
    .input-group > .select2-container--bootstrap-5:not(:first-child) {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
    
    /* Fix Select2 dropdown in input groups with prepend and append */
    .input-group > .select2-container--bootstrap-5:not(:first-child):not(:last-child) {
        border-radius: 0;
    }
    
    /* Fix Select2 dropdown in input groups with prepend and append */
    .input-group > .select2-container--bootstrap-5 .select2-selection {
        border-radius: inherit;
    }
}

/* Fix Select2 dropdown on very small screens */
@media (max-width: 480px) {
    .select2-container--open .select2-dropdown {
        min-width: 100% !important;
        position: fixed !important;
        left: 0 !important;
        right: 0 !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        max-height: 80vh !important;
        margin: 0 auto !important;
        width: 95% !important;
        border-radius: 8px !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    }
    
    .select2-container--open .select2-dropdown--below {
        border-top: 1px solid #aaa !important;
    }
    
    .select2-container--open .select2-dropdown--above {
        border-bottom: 1px solid #aaa !important;
    }
}
