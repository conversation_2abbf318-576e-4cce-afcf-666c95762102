<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-id-card"></i> My Membership</h1>
        <div>
            <a href="<?= base_url('customer') ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Current Membership -->
    <div class="row">
        <div class="col-md-8 mb-4">
            <?php if (!$membership): ?>
                <div class="card shadow-sm">
                    <div class="card-body text-center p-5">
                        <img src="<?= base_url('assets/images/membership.svg') ?>" alt="No Membership" style="width: 150px; opacity: 0.5;" class="mb-3">
                        <h4>No Active Membership</h4>
                        <p class="text-muted">You don't have an active membership plan.</p>
                        <p>Consider joining one of our membership plans to enjoy exclusive benefits and discounts.</p>
                    </div>
                </div>
            <?php else: ?>
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-crown"></i> Active Membership</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h3 class="mb-3"><?= $membership['membership_name'] ?></h3>
                                
                                <div class="mb-4">
                                    <?php if (!empty($membership['description'])): ?>
                                        <p><?= $membership['description'] ?></p>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mb-4">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="icon-circle bg-light text-primary me-3">
                                                    <i class="fas fa-calendar-alt"></i>
                                                </div>
                                                <div>
                                                    <div class="text-muted small">Start Date</div>
                                                    <div class="fw-bold"><?= format_date($membership['start_date']) ?></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="icon-circle bg-light text-primary me-3">
                                                    <i class="fas fa-calendar-check"></i>
                                                </div>
                                                <div>
                                                    <div class="text-muted small">End Date</div>
                                                    <div class="fw-bold"><?= format_date($membership['end_date']) ?></div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php if (isset($membership['amount_paid'])): ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="icon-circle bg-light text-primary me-3">
                                                    <i class="fas fa-money-bill-wave"></i>
                                                </div>
                                                <div>
                                                    <div class="text-muted small">Amount Paid</div>
                                                    <div class="fw-bold"><?= format_currency($membership['amount_paid']) ?></div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                        <?php if (isset($membership['payment_method'])): ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="icon-circle bg-light text-primary me-3">
                                                    <i class="fas fa-credit-card"></i>
                                                </div>
                                                <div>
                                                    <div class="text-muted small">Payment Method</div>
                                                    <div class="fw-bold"><?= ucfirst($membership['payment_method']) ?></div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <h5>Membership Benefits</h5>
                                    <ul class="list-group list-group-flush">
                                        <?php if (isset($membership['service_discount']) && $membership['service_discount'] > 0): ?>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <span><?= $membership['service_discount'] ?>% discount on all services</span>
                                            </li>
                                        <?php endif; ?>
                                        <?php if (isset($membership['product_discount']) && $membership['product_discount'] > 0): ?>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2"></i>
                                                <span><?= $membership['product_discount'] ?>% discount on all products</span>
                                            </li>
                                        <?php endif; ?>
                                        <li class="list-group-item d-flex align-items-center">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            <span>Priority booking</span>
                                        </li>
                                        <li class="list-group-item d-flex align-items-center">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            <span>Special member-only promotions</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light mb-4">
                                    <div class="card-body text-center">
                                        <div class="display-4 mb-3">
                                            <?php
                                            $today = new DateTime();
                                            $end_date = new DateTime($membership['end_date']);
                                            $days_left = $today->diff($end_date)->days;
                                            
                                            if ($today > $end_date) {
                                                echo '<span class="text-danger">Expired</span>';
                                            } else {
                                                echo '<span class="text-primary">' . $days_left . '</span>';
                                            }
                                            ?>
                                        </div>
                                        <p class="mb-0">
                                            <?php if ($today > $end_date): ?>
                                                <span class="text-danger">Your membership has expired</span>
                                            <?php else: ?>
                                                <span class="text-primary">Days remaining</span>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h5 class="mb-3">Membership Status</h5>
                                        <?php if ($membership['status'] == 'active' && $today <= $end_date): ?>
                                            <div class="badge bg-success p-2 mb-3" style="font-size: 1rem;">
                                                <i class="fas fa-check-circle me-1"></i> Active
                                            </div>
                                            <p class="mb-0">Your membership is active and all benefits are available.</p>
                                        <?php elseif ($membership['status'] == 'active' && $today > $end_date): ?>
                                            <div class="badge bg-danger p-2 mb-3" style="font-size: 1rem;">
                                                <i class="fas fa-times-circle me-1"></i> Expired
                                            </div>
                                            <p class="mb-0">Your membership has expired. Please renew to continue enjoying the benefits.</p>
                                        <?php else: ?>
                                            <div class="badge bg-secondary p-2 mb-3" style="font-size: 1rem;">
                                                <i class="fas fa-ban me-1"></i> Cancelled
                                            </div>
                                            <p class="mb-0">Your membership has been cancelled.</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Membership History -->
        <div class="col-md-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-history"></i> Membership History</h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($membership_history)): ?>
                        <div class="p-4 text-center">
                            <p class="text-muted mb-0">No membership history found.</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($membership_history as $history): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-1"><?= $history['membership_name'] ?></h6>
                                        <span class="badge <?= ($history['status'] == 'active' && strtotime($history['end_date']) >= strtotime('today')) ? 'bg-success' : (($history['status'] == 'cancelled') ? 'bg-danger' : 'bg-secondary') ?>">
                                            <?= (strtotime($history['end_date']) < strtotime('today') && $history['status'] != 'cancelled') ? 'Expired' : ucfirst($history['status']) ?>
                                        </span>
                                    </div>
                                    <div class="small text-muted">
                                        <?= format_date($history['start_date']) ?> - <?= format_date($history['end_date']) ?>
                                    </div>
                                    <?php if (isset($history['amount_paid']) && $history['amount_paid'] > 0): ?>
                                        <div class="small text-muted mt-1">
                                            Amount: <?= format_currency($history['amount_paid']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
</style>
