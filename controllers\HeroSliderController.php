<?php
/**
 * Hero Slider Controller
 *
 * Handles operations related to hero sliders on the homepage
 */
class HeroSliderController extends Controller {

    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in and has admin or manager role
        if (!is_logged_in() || !has_role(['admin', 'manager'])) {
            $this->redirect(base_url('login'));
        }
    }

    /**
     * Display all sliders
     */
    public function index() {
        $slider_model = new HeroSliderModel();
        $sliders = $slider_model->getAll();

        $this->render('settings/hero_sliders', [
            'page_title' => 'Hero Sliders',
            'sliders' => $sliders
        ]);
    }

    /**
     * Add a new slider
     */
    public function add() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Get form data
            $title = input('title');
            $subtitle = input('subtitle');
            $button_text = input('button_text');
            $button_link = input('button_link');
            $status = input('status');

            // Handle image upload
            $image_path = '';
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                $upload_dir = 'uploads/sliders/';

                // Create directory if it doesn't exist
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                $file_name = time() . '_' . $_FILES['image']['name'];
                $upload_path = $upload_dir . $file_name;

                if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    $image_path = $upload_path;
                } else {
                    flash('error', 'Failed to upload image');
                    $this->redirect(base_url('settings/appearance'));
                }
            } else {
                flash('error', 'Please select an image');
                $this->redirect(base_url('settings/appearance'));
            }

            // Prepare data for insertion
            $data = [
                'title' => $title,
                'subtitle' => $subtitle,
                'image_path' => $image_path,
                'button_text' => $button_text,
                'button_link' => $button_link,
                'status' => $status
            ];

            // Add slider
            $slider_model = new HeroSliderModel();
            $result = $slider_model->add($data);

            if ($result) {
                flash('success', 'Slider added successfully');
            } else {
                flash('error', 'Failed to add slider');
            }

            $this->redirect(base_url('settings/appearance'));
        } else {
            $this->redirect(base_url('settings/appearance'));
        }
    }

    /**
     * Edit a slider
     */
    public function edit($id) {
        $slider_model = new HeroSliderModel();
        $slider = $slider_model->find($id);

        if (!$slider) {
            flash('error', 'Slider not found');
            $this->redirect(base_url('settings/appearance'));
        }

        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Get form data
            $title = input('title');
            $subtitle = input('subtitle');
            $button_text = input('button_text');
            $button_link = input('button_link');
            $status = input('status');

            // Handle image upload
            $image_path = $slider['image_path'];
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                $upload_dir = 'uploads/sliders/';

                // Create directory if it doesn't exist
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }

                $file_name = time() . '_' . $_FILES['image']['name'];
                $upload_path = $upload_dir . $file_name;

                if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    // Delete old image if it exists and is not a default image
                    if (!empty($slider['image_path']) && file_exists($slider['image_path']) &&
                        !strpos($slider['image_path'], 'default-slider')) {
                        unlink($slider['image_path']);
                    }

                    $image_path = $upload_path;
                } else {
                    flash('error', 'Failed to upload image');
                    $this->redirect(base_url('settings/appearance'));
                }
            }

            // Prepare data for update
            $data = [
                'title' => $title,
                'subtitle' => $subtitle,
                'image_path' => $image_path,
                'button_text' => $button_text,
                'button_link' => $button_link,
                'status' => $status
            ];

            // Update slider
            $result = $slider_model->update($id, $data);

            if ($result) {
                flash('success', 'Slider updated successfully');
            } else {
                flash('error', 'Failed to update slider');
            }

            $this->redirect(base_url('settings/appearance'));
        } else {
            $this->render('settings/edit_slider', [
                'page_title' => 'Edit Slider',
                'slider' => $slider
            ]);
        }
    }

    /**
     * Delete a slider
     */
    public function delete($id) {
        $slider_model = new HeroSliderModel();
        $result = $slider_model->delete($id);

        if ($result['success']) {
            // Delete the image file if it exists and is not a default image
            if (!empty($result['image_path']) && file_exists($result['image_path']) &&
                !strpos($result['image_path'], 'default-slider')) {
                unlink($result['image_path']);
            }

            flash('success', 'Slider deleted successfully');
        } else {
            flash('error', 'Failed to delete slider');
        }

        $this->redirect(base_url('settings/appearance'));
    }

    /**
     * Update slider order (AJAX)
     */
    public function updateOrder() {
        // Check if request is AJAX
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] != 'XMLHttpRequest') {
            echo json_encode(['success' => false, 'message' => 'Invalid request']);
            exit;
        }

        // Get order data
        $order_data = $_POST['order'] ?? [];

        if (empty($order_data)) {
            echo json_encode(['success' => false, 'message' => 'No order data provided']);
            exit;
        }

        // Update order
        $slider_model = new HeroSliderModel();
        $result = $slider_model->updateOrder($order_data);

        echo json_encode(['success' => $result]);
        exit;
    }
}
