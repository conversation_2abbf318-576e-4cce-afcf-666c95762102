<div class="container">
    <div class="row mb-5">
        <div class="col-md-12 text-center">
            <h1 class="display-4">Choose a Staff Member</h1>
            <p class="lead">Select the staff member you prefer for your service</p>
        </div>
    </div>
    
    <!-- Selected Service Info -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Selected Service</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h4><?= $service['name'] ?></h4>
                            <p><?= $service['description'] ?></p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="mb-2">
                                <span class="badge bg-secondary">
                                    <i class="fas fa-clock"></i> <?= $service['duration'] ?> mins
                                </span>
                            </div>
                            <h4 class="text-primary"><?= format_currency($service['price']) ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Staff List -->
    <div class="row">
        <?php if (empty($staff)): ?>
            <div class="col-md-12">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No staff members available for this service. Please select another service.
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($staff as $member): ?>
                <div class="col-md-4 mb-4">
                    <div class="card staff-card h-100 shadow" data-staff-id="<?= $member['id'] ?>">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><?= $member['name'] ?></h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-user-circle fa-5x text-primary"></i>
                            </div>
                            <p class="card-text">
                                <strong>Position:</strong> <?= $member['position'] ?>
                            </p>
                            <p class="card-text">
                                <?= $member['experience'] ?? 'Professional stylist with years of experience' ?>
                            </p>
                        </div>
                        <div class="card-footer">
                            <a href="<?= base_url('booking/datetime?service_id=' . $service['id'] . '&staff_id=' . $member['id']) ?>" class="btn btn-primary w-100">
                                <i class="fas fa-arrow-right"></i> Select
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <!-- Navigation Buttons -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between">
                <a href="<?= base_url('booking/services') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Services
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for staff selection -->
<form id="staff-form" action="<?= base_url('booking/datetime') ?>" method="get" class="d-none">
    <input type="hidden" name="service_id" value="<?= $service['id'] ?>">
    <input type="hidden" id="selected_staff" name="staff_id" value="">
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Staff card selection
    var staffCards = document.querySelectorAll('.staff-card');
    staffCards.forEach(function(card) {
        card.addEventListener('click', function() {
            // Get staff ID
            var staffId = this.dataset.staffId;
            
            // Set hidden input value
            document.getElementById('selected_staff').value = staffId;
            
            // Submit form
            document.getElementById('staff-form').submit();
        });
    });
});
</script>
