/**
 * Currency Symbol Fix
 * 
 * This script finds and replaces the incorrect currency symbol code (262145)
 * with the actual rupee symbol (₹) on the page.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Function to replace text in all text nodes
    function replaceTextInNode(node) {
        if (node.nodeType === 3) { // Text node
            if (node.nodeValue.includes('262145')) {
                node.nodeValue = node.nodeValue.replace(/262145/g, '₹');
            }
        } else if (node.nodeType === 1) { // Element node
            // Skip script and style tags
            if (node.tagName !== 'SCRIPT' && node.tagName !== 'STYLE') {
                // Process child nodes
                for (let i = 0; i < node.childNodes.length; i++) {
                    replaceTextInNode(node.childNodes[i]);
                }
            }
        }
    }

    // Replace text in the entire document
    replaceTextInNode(document.body);

    // Also handle dynamic content by observing DOM changes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    replaceTextInNode(node);
                });
            } else if (mutation.type === 'characterData') {
                replaceTextInNode(mutation.target);
            }
        });
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true
    });
});
