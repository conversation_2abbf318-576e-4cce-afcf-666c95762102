<?php
/**
 * Service Model
 */
class ServiceModel extends Model {
    protected $table = 'services';

    /**
     * Get all active services
     *
     * @return array Services
     */
    public function getAllActive() {
        $query = "SELECT s.*, c.name as category_name
                 FROM " . $this->table . " s
                 JOIN service_categories c ON s.category_id = c.id
                 WHERE s.status = 'active'
                 ORDER BY s.name";
        $stmt = $this->db->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get services by category ID
     *
     * @param int $category_id Category ID
     * @return array Services
     */
    public function getByCategoryId($category_id) {
        $query = "SELECT s.*, c.name as category_name
                 FROM " . $this->table . " s
                 JOIN service_categories c ON s.category_id = c.id
                 WHERE s.category_id = :category_id AND s.status = 'active'
                 ORDER BY s.name";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':category_id', $category_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get service with category
     *
     * @param int $id Service ID
     * @return array|false Service data or false if not found
     */
    public function getWithCategory($id) {
        $query = "SELECT s.*, c.name as category_name
                 FROM " . $this->table . " s
                 JOIN service_categories c ON s.category_id = c.id
                 WHERE s.id = :id
                 LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get all services with categories
     *
     * @return array Services
     */
    public function getAllWithCategories() {
        $query = "SELECT s.*, c.name as category_name
                 FROM " . $this->table . " s
                 JOIN service_categories c ON s.category_id = c.id
                 ORDER BY s.name";
        $stmt = $this->db->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Find service by name
     *
     * @param string $name Service name
     * @return array|false Service data or false if not found
     */
    public function findByName($name) {
        $query = "SELECT * FROM " . $this->table . " WHERE name = :name LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':name', $name);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Find service by name and category
     *
     * @param string $name Service name
     * @param int $category_id Category ID
     * @return array|false Service data or false if not found
     */
    public function findByNameAndCategory($name, $category_id) {
        $query = "SELECT * FROM " . $this->table . " WHERE name = :name AND category_id = :category_id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':category_id', $category_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get top most used services
     *
     * @param int $limit Number of services to return
     * @return array Services
     */
    public function getTopUsedServices($limit = 10) {
        try {
            // First try to get services with usage count
            $query = "SELECT s.*, c.name as category_name, COUNT(is2.service_id) as usage_count
                     FROM " . $this->table . " s
                     JOIN service_categories c ON s.category_id = c.id
                     LEFT JOIN invoice_services is2 ON s.id = is2.service_id
                     WHERE s.status = 'active'
                     GROUP BY s.id
                     ORDER BY usage_count DESC, s.name ASC
                     LIMIT :limit";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();

            $services = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // If no services found or fewer than limit, get the most recent services
            if (count($services) < $limit) {
                $remaining = $limit - count($services);
                $used_ids = array_column($services, 'id');

                $exclude_clause = empty($used_ids) ? "" : "AND s.id NOT IN (" . implode(',', $used_ids) . ")";

                $query = "SELECT s.*, c.name as category_name, 0 as usage_count
                         FROM " . $this->table . " s
                         JOIN service_categories c ON s.category_id = c.id
                         WHERE s.status = 'active' $exclude_clause
                         ORDER BY s.created_at DESC, s.name ASC
                         LIMIT :limit";
                $stmt = $this->db->prepare($query);
                $stmt->bindParam(':limit', $remaining, PDO::PARAM_INT);
                $stmt->execute();

                $recent_services = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $services = array_merge($services, $recent_services);
            }

            return $services;
        } catch (PDOException $e) {
            // If there's an error (like GROUP BY issues), fall back to a simpler query
            $query = "SELECT s.*, c.name as category_name, 0 as usage_count
                     FROM " . $this->table . " s
                     JOIN service_categories c ON s.category_id = c.id
                     WHERE s.status = 'active'
                     ORDER BY s.name ASC
                     LIMIT :limit";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }

    /**
     * Search services by name or category
     *
     * @param string $search Search term
     * @return array Services
     */
    public function searchServices($search) {
        try {
            $search = '%' . $search . '%';
            $query = "SELECT s.*, c.name as category_name
                     FROM " . $this->table . " s
                     JOIN service_categories c ON s.category_id = c.id
                     WHERE s.status = 'active' AND (s.name LIKE :search OR c.name LIKE :search)
                     ORDER BY s.name
                     LIMIT 20"; // Limit results to prevent overwhelming the dropdown
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':search', $search);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            // Log error and return empty array
            error_log('Error searching services: ' . $e->getMessage());
            return [];
        }
    }
}

