<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-edit"></i> Edit Payroll</h1>
        <div>
            <a href="<?= base_url('payroll/view/' . $payroll['id']) ?>" class="btn btn-outline-primary">
                <i class="fas fa-eye"></i> View Details
            </a>
            <a href="<?= base_url('payroll') ?>" class="btn btn-outline-primary ms-2">
                <i class="fas fa-arrow-left"></i> Back to Payroll
            </a>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('payroll/update/' . $payroll['id']) ?>" method="post" id="payrollForm">
                <div class="row">
                    <!-- Staff Selection -->
                    <div class="col-md-12 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-user me-2"></i>Staff Information</h5>
                    </div>

                    <!-- Staff -->
                    <div class="col-md-6 mb-3">
                        <label for="staff_id" class="form-label">Select Staff <span class="text-danger">*</span></label>
                        <select class="form-select" id="staff_id" name="staff_id" required>
                            <option value="">Select Staff Member</option>
                            <?php foreach ($staff as $member): ?>
                                <option value="<?= $member['id'] ?>" data-salary="<?= $member['salary'] ?>" data-commission="<?= $member['commission_rate'] ?>" <?= $member['id'] == $payroll['staff_id'] ? 'selected' : '' ?>>
                                    <?= $member['name'] ?> (<?= $member['position'] ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Pay Period -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-calendar-alt me-2"></i>Pay Period</h5>
                    </div>

                    <!-- Pay Period Start -->
                    <div class="col-md-4 mb-3">
                        <label for="pay_period_start" class="form-label">Start Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="pay_period_start" name="pay_period_start" value="<?= $payroll['pay_period_start'] ?>" required>
                    </div>

                    <!-- Pay Period End -->
                    <div class="col-md-4 mb-3">
                        <label for="pay_period_end" class="form-label">End Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="pay_period_end" name="pay_period_end" value="<?= $payroll['pay_period_end'] ?>" required>
                    </div>

                    <!-- Payment Date -->
                    <div class="col-md-4 mb-3">
                        <label for="payment_date" class="form-label">Payment Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?= $payroll['payment_date'] ?>" required>
                    </div>

                    <!-- Commission Details -->
                    <?php if (!empty($commissions)): ?>
                    <div class="col-md-12 mb-3" id="commissionsContainer">
                        <div class="card border">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Commission Details</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm" id="commissionsTable">
                                        <thead>
                                            <tr>
                                                <th>Service</th>
                                                <th>Service Amount</th>
                                                <th>Commission Rate</th>
                                                <th>Commission Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($commissions as $commission): ?>
                                            <tr>
                                                <td><?= $commission['service_name'] ?></td>
                                                <td><?= format_currency($commission['service_amount']) ?></td>
                                                <td><?= $commission['commission_rate'] ?>%</td>
                                                <td><?= format_currency($commission['commission_amount']) ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr class="table-light">
                                                <th colspan="3" class="text-end">Total Commission:</th>
                                                <th><?= format_currency($payroll['commission_amount']) ?></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Salary Details -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-money-bill-wave me-2"></i>Salary Details</h5>
                    </div>

                    <!-- Basic Salary -->
                    <div class="col-md-3 mb-3">
                        <label for="basic_salary" class="form-label">Basic Salary</label>
                        <div class="input-group">
                            <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                            <input type="number" class="form-control" id="basic_salary" name="basic_salary" step="0.01" min="0" value="<?= $payroll['basic_salary'] ?>">
                        </div>
                    </div>

                    <!-- Commission Amount -->
                    <div class="col-md-3 mb-3">
                        <label for="commission_amount" class="form-label">Commission</label>
                        <div class="input-group">
                            <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                            <input type="number" class="form-control" id="commission_amount" name="commission_amount" step="0.01" min="0" value="<?= $payroll['commission_amount'] ?>">
                        </div>
                    </div>

                    <!-- Bonus -->
                    <div class="col-md-3 mb-3">
                        <label for="bonus" class="form-label">Bonus</label>
                        <div class="input-group">
                            <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                            <input type="number" class="form-control" id="bonus" name="bonus" step="0.01" min="0" value="<?= $payroll['bonus'] ?>">
                        </div>
                    </div>

                    <!-- Deductions -->
                    <div class="col-md-3 mb-3">
                        <label for="deductions" class="form-label">Deductions</label>
                        <div class="input-group">
                            <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                            <input type="number" class="form-control" id="deductions" name="deductions" step="0.01" min="0" value="<?= $payroll['deductions'] ?>">
                        </div>
                    </div>

                    <!-- Net Salary -->
                    <div class="col-md-6 mb-3">
                        <label for="net_salary" class="form-label">Net Salary</label>
                        <div class="input-group">
                            <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                            <input type="number" class="form-control" id="net_salary" name="net_salary" step="0.01" min="0" value="<?= $payroll['net_salary'] ?>">
                        </div>
                        <div class="form-text">Basic Salary + Commission + Bonus - Deductions</div>
                    </div>

                    <!-- Payment Method -->
                    <div class="col-md-6 mb-3">
                        <label for="payment_method" class="form-label">Payment Method</label>
                        <select class="form-select" id="payment_method" name="payment_method">
                            <option value="cash" <?= $payroll['payment_method'] == 'cash' ? 'selected' : '' ?>>Cash</option>
                            <option value="bank_transfer" <?= $payroll['payment_method'] == 'bank_transfer' ? 'selected' : '' ?>>Bank Transfer</option>
                            <option value="check" <?= $payroll['payment_method'] == 'check' ? 'selected' : '' ?>>Check</option>
                            <option value="other" <?= $payroll['payment_method'] == 'other' ? 'selected' : '' ?>>Other</option>
                        </select>
                    </div>

                    <!-- Status -->
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="pending" <?= $payroll['status'] == 'pending' ? 'selected' : '' ?>>Pending</option>
                            <option value="paid" <?= $payroll['status'] == 'paid' ? 'selected' : '' ?>>Paid</option>
                        </select>
                    </div>

                    <!-- Notes -->
                    <div class="col-md-6 mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"><?= $payroll['notes'] ?></textarea>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-md-12 mt-4 text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-save me-2"></i> Update Payroll
                        </button>
                        <a href="<?= base_url('payroll') ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate net salary when any amount changes
    document.getElementById('basic_salary').addEventListener('input', calculateNetSalary);
    document.getElementById('commission_amount').addEventListener('input', calculateNetSalary);
    document.getElementById('bonus').addEventListener('input', calculateNetSalary);
    document.getElementById('deductions').addEventListener('input', calculateNetSalary);
    
    // Form submission
    document.getElementById('payrollForm').addEventListener('submit', function(e) {
        // Validate form
        const staffId = document.getElementById('staff_id').value;
        const startDate = document.getElementById('pay_period_start').value;
        const endDate = document.getElementById('pay_period_end').value;
        
        if (!staffId) {
            e.preventDefault();
            alert('Please select a staff member');
            return;
        }
        
        if (!startDate || !endDate) {
            e.preventDefault();
            alert('Please select pay period start and end dates');
            return;
        }
        
        if (new Date(startDate) > new Date(endDate)) {
            e.preventDefault();
            alert('Start date cannot be after end date');
            return;
        }
    });
    
    // Function to calculate net salary
    function calculateNetSalary() {
        const basicSalary = parseFloat(document.getElementById('basic_salary').value) || 0;
        const commission = parseFloat(document.getElementById('commission_amount').value) || 0;
        const bonus = parseFloat(document.getElementById('bonus').value) || 0;
        const deductions = parseFloat(document.getElementById('deductions').value) || 0;
        
        const netSalary = basicSalary + commission + bonus - deductions;
        document.getElementById('net_salary').value = netSalary.toFixed(2);
    }
});
</script>
