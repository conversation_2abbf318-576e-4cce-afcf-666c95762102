<?php
/**
 * Invoice Product Model
 */
class InvoiceProductModel extends Model {
    protected $table = 'invoice_products';
    
    /**
     * Delete by invoice ID
     * 
     * @param int $invoice_id Invoice ID
     * @return bool Success or failure
     */
    public function deleteByInvoiceId($invoice_id) {
        $query = "DELETE FROM " . $this->table . " WHERE invoice_id = :invoice_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':invoice_id', $invoice_id);
        
        return $stmt->execute();
    }
    
    /**
     * Find by product ID
     * 
     * @param int $product_id Product ID
     * @return array Invoice products
     */
    public function findByProductId($product_id) {
        $query = "SELECT * FROM " . $this->table . " WHERE product_id = :product_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':product_id', $product_id);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
