<?php
/**
 * Membership Model
 */
class MembershipModel extends Model {
    protected $table = 'membership_plans';

    /**
     * Get all memberships
     *
     * @return array Memberships
     */
    public function all() {
        $query = "SELECT * FROM " . $this->table . " ORDER BY name";
        $stmt = $this->db->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Find membership by ID
     *
     * @param int $id Membership ID
     * @return array|false Membership data or false if not found
     */
    public function find($id) {
        $query = "SELECT * FROM " . $this->table . " WHERE id = :id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Count active members for a membership
     *
     * @param int $membership_id Membership ID
     * @return int Number of active members
     */
    public function countActiveMembers($membership_id) {
        $query = "SELECT COUNT(*) FROM customer_memberships
                 WHERE plan_id = :membership_id
                 AND status = 'active'
                 AND end_date >= CURDATE()";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':membership_id', $membership_id);
        $stmt->execute();

        return $stmt->fetchColumn();
    }

    /**
     * Get members for a membership
     *
     * @param int $membership_id Membership ID
     * @return array Members
     */
    public function getMembers($membership_id) {
        try {
            $query = "SELECT cm.*, u.name as customer_name, u.email as customer_email, c.phone as customer_phone
                     FROM customer_memberships cm
                     JOIN customers c ON cm.customer_id = c.id
                     JOIN users u ON c.user_id = u.id
                     WHERE cm.plan_id = :membership_id
                     ORDER BY cm.start_date DESC";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':membership_id', $membership_id);
            $stmt->execute();

            $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
            error_log("Found " . count($members) . " members for membership ID: " . $membership_id);
            return $members;
        } catch (PDOException $e) {
            error_log("Error in getMembers: " . $e->getMessage());

            // Try alternative query if the first one fails
            try {
                // This is a fallback query that uses minimal columns
                $query = "SELECT cm.id, cm.customer_id, cm.start_date, cm.end_date, cm.status,
                         u.name as customer_name, u.email as customer_email, c.phone as customer_phone
                         FROM customer_memberships cm
                         JOIN customers c ON cm.customer_id = c.id
                         JOIN users u ON c.user_id = u.id
                         WHERE cm.plan_id = :membership_id
                         ORDER BY cm.start_date DESC";
                $stmt = $this->db->prepare($query);
                $stmt->bindParam(':membership_id', $membership_id);
                $stmt->execute();

                return $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e2) {
                error_log("Error in fallback getMembers query: " . $e2->getMessage());
                return [];
            }
        }
    }

    /**
     * Get active membership by customer ID
     *
     * @param int $customer_id Customer ID
     * @return array|false Membership data or false if not found
     */
    public function getActiveMembershipByCustomerId($customer_id) {
        try {
            $query = "SELECT cm.*, m.name as membership_name, m.service_discount, m.product_discount
                     FROM customer_memberships cm
                     JOIN " . $this->table . " m ON cm.plan_id = m.id
                     WHERE cm.customer_id = :customer_id
                     AND cm.status = 'active'
                     AND cm.end_date >= CURDATE()
                     LIMIT 1";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            error_log("Active membership for customer ID " . $customer_id . ": " . ($result ? "Found" : "Not found"));
            return $result;
        } catch (PDOException $e) {
            error_log("Error in getActiveMembershipByCustomerId: " . $e->getMessage());

            // Try a more basic query as fallback
            try {
                $query = "SELECT cm.id, cm.customer_id, cm.plan_id, cm.start_date, cm.end_date, cm.status,
                         m.name as membership_name, m.service_discount, m.product_discount
                         FROM customer_memberships cm
                         JOIN " . $this->table . " m ON cm.plan_id = m.id
                         WHERE cm.customer_id = :customer_id
                         AND cm.status = 'active'
                         AND cm.end_date >= CURDATE()
                         LIMIT 1";
                $stmt = $this->db->prepare($query);
                $stmt->bindParam(':customer_id', $customer_id);
                $stmt->execute();

                return $stmt->fetch(PDO::FETCH_ASSOC);
            } catch (PDOException $e2) {
                error_log("Error in fallback getActiveMembershipByCustomerId query: " . $e2->getMessage());
                return false;
            }
        }
    }

    /**
     * Get customer membership (legacy method)
     *
     * @param int $customer_id Customer ID
     * @return array|false Membership data or false if not found
     */
    public function getCustomerMembership($customer_id) {
        return $this->getActiveMembershipByCustomerId($customer_id);
    }

    /**
     * Get customer membership history
     *
     * @param int $customer_id Customer ID
     * @return array Membership history
     */
    public function getCustomerMembershipHistory($customer_id) {
        try {
            $query = "SELECT cm.*, m.name as membership_name, m.description, m.price,
                     m.service_discount, m.product_discount
                     FROM customer_memberships cm
                     JOIN " . $this->table . " m ON cm.plan_id = m.id
                     WHERE cm.customer_id = :customer_id
                     ORDER BY cm.start_date DESC";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error in getCustomerMembershipHistory: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Add member to membership
     *
     * @param array $data Member data
     * @return int|false Member ID or false on failure
     */
    public function addMember($data) {
        try {
            // Check which columns exist in the table
            $query = "SHOW COLUMNS FROM customer_memberships";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Debug the columns
            error_log("Available columns in customer_memberships: " . json_encode($columns));

            // Build column list and values list for the query
            $columnsList = ['customer_id', 'plan_id', 'start_date', 'end_date', 'status'];
            $valuesList = [':customer_id', ':plan_id', ':start_date', ':end_date', ':status'];

            // Add optional columns if they exist
            if (in_array('payment_method', $columns)) {
                $columnsList[] = 'payment_method';
                $valuesList[] = ':payment_method';
            }

            if (in_array('amount_paid', $columns)) {
                $columnsList[] = 'amount_paid';
                $valuesList[] = ':amount_paid';
            }

            if (in_array('notes', $columns)) {
                $columnsList[] = 'notes';
                $valuesList[] = ':notes';
            }

            // Build the query
            $query = "INSERT INTO customer_memberships (" . implode(', ', $columnsList) . ")
                     VALUES (" . implode(', ', $valuesList) . ")";

            error_log("Insert query: " . $query);

            // Prepare and bind parameters
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':customer_id', $data['customer_id']);
            $stmt->bindParam(':plan_id', $data['plan_id']);
            $stmt->bindParam(':start_date', $data['start_date']);
            $stmt->bindParam(':end_date', $data['end_date']);
            $stmt->bindParam(':status', $data['status']);

            // Bind optional parameters if they exist in the query
            if (in_array('payment_method', $columns) && isset($data['payment_method'])) {
                $stmt->bindParam(':payment_method', $data['payment_method']);
            }

            if (in_array('amount_paid', $columns) && isset($data['amount_paid'])) {
                $stmt->bindParam(':amount_paid', $data['amount_paid']);
            }

            if (in_array('notes', $columns) && isset($data['notes'])) {
                $stmt->bindParam(':notes', $data['notes']);
            }

            // Execute the query
            if ($stmt->execute()) {
                $lastId = $this->db->lastInsertId();
                error_log("Successfully added member with ID: " . $lastId);
                return $lastId;
            } else {
                $errorInfo = $stmt->errorInfo();
                error_log("SQL Error: " . json_encode($errorInfo));
                return false;
            }

        } catch (PDOException $e) {
            error_log("PDO Exception in addMember: " . $e->getMessage());

            // Try a simplified query as a last resort
            try {
                $query = "INSERT INTO customer_memberships (customer_id, plan_id, start_date, end_date, status)
                         VALUES (:customer_id, :plan_id, :start_date, :end_date, :status)";

                $stmt = $this->db->prepare($query);
                $stmt->bindParam(':customer_id', $data['customer_id']);
                $stmt->bindParam(':plan_id', $data['plan_id']);
                $stmt->bindParam(':start_date', $data['start_date']);
                $stmt->bindParam(':end_date', $data['end_date']);
                $stmt->bindParam(':status', $data['status']);

                if ($stmt->execute()) {
                    $lastId = $this->db->lastInsertId();
                    error_log("Successfully added member with simplified query. ID: " . $lastId);
                    return $lastId;
                } else {
                    $errorInfo = $stmt->errorInfo();
                    error_log("SQL Error in simplified query: " . json_encode($errorInfo));
                }
            } catch (PDOException $e2) {
                error_log("Error in fallback query: " . $e2->getMessage());
            }

            return false;
        }
    }

    /**
     * Get membership record by ID
     *
     * @param int $id Membership record ID
     * @return array|false Membership record data or false if not found
     */
    public function getMembershipRecord($id) {
        $query = "SELECT * FROM customer_memberships WHERE id = :id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Update membership record
     *
     * @param int $id Membership record ID
     * @param array $data Membership record data
     * @return bool Success or failure
     */
    public function updateMembershipRecord($id, $data) {
        $query = "UPDATE customer_memberships SET ";
        $params = [];

        foreach ($data as $key => $value) {
            $params[] = "$key = :$key";
        }

        $query .= implode(', ', $params);
        $query .= " WHERE id = :id";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);

        foreach ($data as $key => $value) {
            $stmt->bindParam(":$key", $data[$key]);
        }

        return $stmt->execute();
    }
}
