<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-calendar-plus"></i> New Appointment</h1>
        <a href="<?= base_url('appointments') ?>" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Appointments
        </a>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <form action="<?= base_url('appointments/store') ?>" method="post">
                <div class="row">
                    <!-- Customer Selection -->
                    <div class="col-md-6 mb-3">
                        <label for="customer_id" class="form-label">Customer <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <select class="form-select" id="customer_id" name="customer_id" required>
                                <option value="">Select Customer</option>
                                <?php foreach ($customers as $customer): ?>
                                    <option value="<?= $customer['id'] ?>"><?= $customer['name'] ?> (<?= $customer['phone'] ?>)</option>
                                <?php endforeach; ?>
                            </select>
                            <a href="<?= base_url('customers/create') ?>" class="btn btn-outline-secondary" target="_blank">
                                <i class="fas fa-plus"></i> New
                            </a>
                        </div>
                    </div>

                    <!-- Service Selection -->
                    <div class="col-md-6 mb-3">
                        <label for="service_id" class="form-label">Service <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-cut"></i></span>
                            <select class="form-select" id="service_id" name="service_id" required>
                                <option value="">Select Service</option>
                                <?php foreach ($services as $service): ?>
                                    <option value="<?= $service['id'] ?>" data-duration="<?= $service['duration'] ?>" data-price="<?= $service['price'] ?>">
                                        <?= $service['name'] ?> (<?= $service['duration'] ?> mins - <?= format_currency($service['price']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Staff Selection -->
                    <div class="col-md-6 mb-3">
                        <label for="staff_id" class="form-label">Staff <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user-tie"></i></span>
                            <select class="form-select" id="staff_id" name="staff_id" required>
                                <option value="">Select Staff</option>
                                <?php foreach ($staff as $staff_member): ?>
                                    <option value="<?= $staff_member['id'] ?>"><?= $staff_member['name'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Date Selection -->
                    <div class="col-md-6 mb-3">
                        <label for="appointment_date" class="form-label">Date <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            <input type="date" class="form-control" id="appointment_date" name="appointment_date" min="<?= date('Y-m-d') ?>" required>
                        </div>
                    </div>

                    <!-- Time Selection -->
                    <div class="col-md-6 mb-3">
                        <label for="start_time" class="form-label">Time <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-clock"></i></span>
                            <input type="time" class="form-control" id="start_time" name="start_time" required>
                        </div>
                        <div class="form-text">
                            Duration: <span id="service_duration">0</span> minutes
                        </div>
                    </div>

                    <!-- Status Selection -->
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-tag"></i></span>
                            <select class="form-select" id="status" name="status" required>
                                <option value="pending">Pending</option>
                                <option value="confirmed">Confirmed</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="col-md-12 mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>

                    <!-- Available Time Slots -->
                    <div class="col-md-12 mb-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">Available Time Slots</h5>
                                <p class="card-text text-muted">Select a staff member and date to see available time slots.</p>
                                <div id="time_slots_container" class="mt-3">
                                    <!-- Time slots will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Create Appointment
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update service duration when service is selected
    const serviceSelect = document.getElementById('service_id');
    const serviceDurationSpan = document.getElementById('service_duration');
    
    serviceSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const duration = selectedOption.getAttribute('data-duration') || '0';
        serviceDurationSpan.textContent = duration;
    });
    
    // Load available time slots when staff and date are selected
    const staffSelect = document.getElementById('staff_id');
    const dateInput = document.getElementById('appointment_date');
    const timeSlotsContainer = document.getElementById('time_slots_container');
    
    function loadTimeSlots() {
        const staffId = staffSelect.value;
        const date = dateInput.value;
        const serviceId = serviceSelect.value;
        
        if (!staffId || !date || !serviceId) {
            return;
        }
        
        timeSlotsContainer.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
        
        // In a real application, this would be an AJAX call to the server
        // For now, we'll simulate it with a timeout
        setTimeout(function() {
            // Generate time slots from 9 AM to 6 PM with 30-minute intervals
            let html = '<div class="d-flex flex-wrap gap-2">';
            
            for (let hour = 9; hour < 18; hour++) {
                for (let minute = 0; minute < 60; minute += 30) {
                    const timeHour = hour;
                    const ampm = hour >= 12 ? 'PM' : 'AM';
                    const displayHour = hour > 12 ? hour - 12 : hour;
                    
                    const timeString = `${displayHour}:${minute === 0 ? '00' : minute} ${ampm}`;
                    const timeValue = `${hour < 10 ? '0' + hour : hour}:${minute === 0 ? '00' : minute}:00`;
                    
                    // Randomly determine if slot is available (in a real app, this would come from the server)
                    const isAvailable = Math.random() > 0.3;
                    const slotClass = isAvailable ? 'btn-outline-primary' : 'btn-outline-secondary disabled';
                    
                    html += `<button type="button" class="btn btn-sm ${slotClass} time-slot" data-time="${timeValue}" ${!isAvailable ? 'disabled' : ''}>
                                ${timeString}
                             </button>`;
                }
            }
            
            html += '</div>';
            timeSlotsContainer.innerHTML = html;
            
            // Add click event to time slots
            document.querySelectorAll('.time-slot:not(.disabled)').forEach(function(slot) {
                slot.addEventListener('click', function() {
                    // Remove selected class from all slots
                    document.querySelectorAll('.time-slot').forEach(function(s) {
                        s.classList.remove('active');
                    });
                    
                    // Add selected class to clicked slot
                    this.classList.add('active');
                    
                    // Set the time input value
                    document.getElementById('start_time').value = this.getAttribute('data-time');
                });
            });
        }, 500);
    }
    
    staffSelect.addEventListener('change', loadTimeSlots);
    dateInput.addEventListener('change', loadTimeSlots);
    serviceSelect.addEventListener('change', loadTimeSlots);
});
</script>
