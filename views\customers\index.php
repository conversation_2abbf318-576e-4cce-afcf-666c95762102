<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-users"></i> Customers</h1>
        <div>
            <button type="button" class="btn btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#importModal">
                <i class="fas fa-file-import"></i> Import
            </button>
            <a href="<?= base_url('customers/export') ?>" class="btn btn-outline-primary me-2">
                <i class="fas fa-file-export"></i> Export
            </a>
            <a href="<?= base_url('customers/create') ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> New Customer
            </a>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('customers') ?>" method="get" class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" name="search" placeholder="Search by name, email, or phone" value="<?= $search ?>">
                        <button type="submit" class="btn btn-primary">Search</button>
                        <?php if ($search): ?>
                            <a href="<?= base_url('customers') ?>" class="btn btn-outline-secondary">Clear</a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex justify-content-end">
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-filter"></i> Filter
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="filterDropdown">
                                <li><a class="dropdown-item" href="<?= base_url('customers') ?>">All Customers</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('customers?status=active') ?>">Active Customers</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('customers?status=inactive') ?>">Inactive Customers</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= base_url('customers?sort=newest') ?>">Newest First</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('customers?sort=oldest') ?>">Oldest First</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('customers?sort=name') ?>">Sort by Name</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Customers List -->
    <div class="card shadow-sm">
        <div class="card-body p-0">
            <?php if (empty($customers)): ?>
                <div class="p-4 text-center">
                    <img src="<?= base_url('assets/images/customers.svg') ?>" alt="No Customers" style="width: 120px; opacity: 0.5;" class="mb-3">
                    <p class="text-muted mb-0">No customers found matching your criteria.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Customer</th>
                                <th>Contact</th>
                                <th>Gender</th>
                                <th>Joined</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($customers as $customer): ?>
                                <tr>
                                    <td>#<?= $customer['id'] ?></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-primary text-white me-2">
                                                <?= strtoupper(substr($customer['name'], 0, 1)) ?>
                                            </div>
                                            <div>
                                                <div class="fw-bold">
                                                    <?= $customer['name'] ?>
                                                    <?php
                                                    // Check if customer has an active membership
                                                    $membership_model = new MembershipModel();
                                                    $membership = $membership_model->getActiveMembershipByCustomerId($customer['id']);
                                                    if ($membership): ?>
                                                        <i class="fas fa-crown text-warning" title="<?= $membership['membership_name'] ?> Member"></i>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="small text-muted"><?= $customer['address'] ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div><i class="fas fa-envelope text-muted me-1"></i> <?= $customer['email'] ?></div>
                                        <div><i class="fas fa-phone text-muted me-1"></i> <?= $customer['phone'] ?></div>
                                    </td>
                                    <td><?= ucfirst($customer['gender']) ?></td>
                                    <td><?= format_date($customer['created_at']) ?></td>
                                    <td>
                                        <?php if ($customer['status'] == 'active'): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            <a href="<?= base_url('customers/view/' . $customer['id']) ?>" class="btn btn-sm btn-outline-primary me-1" data-bs-toggle="tooltip" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('customers/edit/' . $customer['id']) ?>" class="btn btn-sm btn-outline-secondary me-1" data-bs-toggle="tooltip" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?= base_url('customers/delete/' . $customer['id']) ?>" class="btn btn-sm btn-outline-danger" data-bs-toggle="tooltip" title="Delete" onclick="return confirm('Are you sure you want to delete this customer?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">Import Customers</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="<?= base_url('customers/import') ?>" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="csv_file" class="form-label">CSV File</label>
                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                        <div class="form-text">
                            The CSV file should have the following columns: Name, Email, Phone, Address, Gender, Date of Birth
                        </div>
                        <div class="alert alert-info mt-2 small">
                            <i class="fas fa-info-circle"></i> Customers with the same email address will be updated instead of creating duplicates.
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="has_header" name="has_header" checked>
                            <label class="form-check-label" for="has_header">
                                File has header row
                            </label>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-file-import"></i> Import
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>


