<?php
/**
 * Booking Controller for frontend booking
 */
class BookingController extends Controller {
    /**
     * Display booking homepage
     */
    public function index() {
        // Get service categories
        $category_model = new ServiceCategoryModel();
        $categories = $category_model->all();

        // Get hero sliders
        $slider_model = new HeroSliderModel();
        $sliders = $slider_model->getAllActive();

        // Render booking homepage for all users
        $this->render('booking/index', [
            'categories' => $categories,
            'sliders' => $sliders,
            'page_title' => 'Welcome to ' . get_salon_name(),
            'page_description' => 'Welcome to ' . get_salon_name() . ' - Your premier destination for professional beauty services including hair styling, skin care, nail treatments, makeup, and relaxing spa services. Book your appointment online today!'
        ]);
    }

    /**
     * Display services for booking
     */
    public function services() {
        // Get category ID from query string
        $category_id = input('category_id');

        // Get services
        $service_model = new ServiceModel();
        if ($category_id) {
            $services = $service_model->getByCategoryId($category_id);
        } else {
            $services = $service_model->getAllActive();
        }

        // Get categories for filter (only categories with services)
        $category_model = new ServiceCategoryModel();
        $categories = $category_model->getCategoriesWithServices();

        // Render services page
        $this->render('booking/services', [
            'services' => $services,
            'categories' => $categories,
            'selected_category' => $category_id
        ]);
    }

    /**
     * Display staff selection for booking
     */
    public function staff() {
        // Get service ID from query string
        $service_id = input('service_id');

        if (!$service_id) {
            flash('error', 'Please select a service first');
            $this->redirect(base_url('booking/services'));
        }

        // Get service details
        $service_model = new ServiceModel();
        $service = $service_model->find($service_id);

        if (!$service) {
            flash('error', 'Service not found');
            $this->redirect(base_url('booking/services'));
        }

        // Get staff who can perform this service
        $staff_model = new StaffModel();
        $staff = $staff_model->getByServiceId($service_id);

        // Render staff selection page
        $this->render('booking/staff', [
            'service' => $service,
            'staff' => $staff
        ]);
    }

    /**
     * Display date and time selection for booking
     */
    public function datetime() {
        // Get service and staff IDs from query string
        $service_id = input('service_id');
        $staff_id = input('staff_id');

        if (!$service_id || !$staff_id) {
            flash('error', 'Please select a service and staff');
            $this->redirect(base_url('booking/services'));
        }

        // Get service details
        $service_model = new ServiceModel();
        $service = $service_model->find($service_id);

        if (!$service) {
            flash('error', 'Service not found');
            $this->redirect(base_url('booking/services'));
        }

        // Get staff details
        $staff_model = new StaffModel();
        $staff = $staff_model->find($staff_id);

        if (!$staff) {
            flash('error', 'Staff not found');
            $this->redirect(base_url('booking/staff?service_id=' . $service_id));
        }

        // Get available dates (next 14 days)
        $dates = [];
        $start_date = date('Y-m-d');
        for ($i = 0; $i < 14; $i++) {
            $date = date('Y-m-d', strtotime($start_date . ' +' . $i . ' days'));
            $day_name = date('l', strtotime($date));
            $dates[] = [
                'date' => $date,
                'day_name' => $day_name,
                'formatted_date' => date('d M Y', strtotime($date))
            ];
        }

        // Render date and time selection page
        $this->render('booking/datetime', [
            'service' => $service,
            'staff' => $staff,
            'dates' => $dates
        ]);
    }

    /**
     * Process booking confirmation
     */
    public function confirm() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('booking'));
        }

        // Get form data
        $service_id = input('service_id');
        $staff_id = input('staff_id');
        $date = input('date');
        $time = input('time');
        $name = input('name');
        $email = input('email');
        $phone = input('phone');
        $notes = input('notes');

        // Validate form data
        if (empty($service_id) || empty($staff_id) || empty($date) || empty($time) || empty($name) || empty($email) || empty($phone)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('booking/datetime?service_id=' . $service_id . '&staff_id=' . $staff_id));
        }

        // Check if user is logged in
        $customer_id = null;
        if (is_logged_in()) {
            // Get customer ID
            $customer_model = new CustomerModel();
            $customer = $customer_model->findByUserId(current_user_id());

            if ($customer) {
                $customer_id = $customer['id'];
            }
        } else {
            // Check if email exists
            $user_model = new UserModel();
            $user = $user_model->findByEmail($email);

            if ($user) {
                // Get customer ID
                $customer_model = new CustomerModel();
                $customer = $customer_model->findByUserId($user['id']);

                if ($customer) {
                    $customer_id = $customer['id'];
                }
            } else {
                // Create new user
                $user_data = [
                    'name' => $name,
                    'email' => $email,
                    'password' => password_hash(random_string(8), PASSWORD_DEFAULT),
                    'role' => 'customer',
                    'status' => 'active'
                ];

                $user_id = $user_model->create($user_data);

                if ($user_id) {
                    // Create customer profile
                    $customer_data = [
                        'user_id' => $user_id,
                        'phone' => $phone,
                        'address' => '',
                        'gender' => 'other',
                        'date_of_birth' => null
                    ];

                    $customer_id = $customer_model->create($customer_data);
                }
            }
        }

        if (!$customer_id) {
            flash('error', 'Failed to create customer profile');
            $this->redirect(base_url('booking/datetime?service_id=' . $service_id . '&staff_id=' . $staff_id));
        }

        // Get service details
        $service_model = new ServiceModel();
        $service = $service_model->find($service_id);

        // Calculate end time
        $start_time = $time;
        $end_time = date('H:i:s', strtotime($start_time . ' +' . $service['duration'] . ' minutes'));

        // Create appointment
        $appointment_model = new AppointmentModel();
        $appointment_data = [
            'customer_id' => $customer_id,
            'service_id' => $service_id,
            'staff_id' => $staff_id,
            'appointment_date' => $date,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'status' => 'pending',
            'notes' => $notes
        ];

        $appointment_id = $appointment_model->create($appointment_data);

        if (!$appointment_id) {
            flash('error', 'Failed to create appointment');
            $this->redirect(base_url('booking/datetime?service_id=' . $service_id . '&staff_id=' . $staff_id));
        }

        // Redirect to success page
        flash('success', 'Appointment booked successfully');
        $this->redirect(base_url('booking/success?id=' . $appointment_id));
    }

    /**
     * Display booking success page
     */
    public function success() {
        // Get appointment ID from query string
        $appointment_id = input('id');

        if (!$appointment_id) {
            $this->redirect(base_url('booking'));
        }

        // Get appointment details
        $appointment_model = new AppointmentModel();
        $appointment = $appointment_model->getWithDetails($appointment_id);

        if (!$appointment) {
            flash('error', 'Appointment not found');
            $this->redirect(base_url('booking'));
        }

        // Render success page
        $this->render('booking/success', [
            'appointment' => $appointment
        ]);
    }
}
