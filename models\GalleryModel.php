<?php
/**
 * Gallery Model
 *
 * Handles database operations for gallery images
 */
class GalleryModel extends Model {

    protected $table = 'gallery_images';

    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Get all gallery images
     *
     * @param string $status Filter by status (active, inactive, all)
     * @param string $category Filter by category
     * @return array Gallery images
     */
    public function getAll($status = 'all', $category = null) {
        $query = "SELECT gi.*, gc.name as category_name, u.name as created_by_name
                 FROM {$this->table} gi
                 LEFT JOIN gallery_categories gc ON gi.category = gc.id
                 LEFT JOIN users u ON gi.created_by = u.id";

        $conditions = [];
        $params = [];

        if ($status != 'all') {
            $conditions[] = "gi.status = :status";
            $params[':status'] = $status;
        }

        if ($category) {
            $conditions[] = "gi.category = :category";
            $params[':category'] = $category;
        }

        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }

        $query .= " ORDER BY gi.created_at DESC";

        $stmt = $this->db->prepare($query);

        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get active gallery images for frontend
     *
     * @param string $category Filter by category
     * @param int $limit Limit number of results
     * @return array Gallery images
     */
    public function getActive($category = null, $limit = null) {
        $query = "SELECT gi.*, gc.name as category_name
                 FROM {$this->table} gi
                 LEFT JOIN gallery_categories gc ON gi.category = gc.id
                 WHERE gi.status = 'active'";

        if ($category) {
            $query .= " AND gi.category = :category";
        }

        $query .= " ORDER BY gi.created_at DESC";

        if ($limit) {
            $query .= " LIMIT :limit";
        }

        $stmt = $this->db->prepare($query);

        if ($category) {
            $stmt->bindValue(':category', $category, PDO::PARAM_INT);
        }

        if ($limit) {
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Add a new gallery image
     *
     * @param array $data Gallery image data
     * @return int|bool ID of the inserted image or false on failure
     */
    public function add($data) {
        return $this->create($data);
    }

    /**
     * Update a gallery image
     *
     * @param int $id Gallery image ID
     * @param array $data Gallery image data
     * @return bool Success or failure
     */
    public function updateImage($id, $data) {
        return $this->update($id, $data);
    }

    /**
     * Delete a gallery image
     *
     * @param int $id Gallery image ID
     * @return array Result with success status and image path
     */
    public function deleteImage($id) {
        // Get image path before deleting
        $image = $this->find($id);
        $image_path = $image['image_path'] ?? '';

        // Delete the record
        $result = $this->delete($id);

        return [
            'success' => $result,
            'image_path' => $image_path
        ];
    }
}
