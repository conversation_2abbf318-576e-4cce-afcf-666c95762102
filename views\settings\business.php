<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-cogs"></i> Settings</h1>
    </div>

    <div class="row">
        <!-- Settings Navigation -->
        <div class="col-md-3 mb-4">
            <?php include 'partials/settings_nav.php'; ?>
        </div>
        
        <!-- Settings Content -->
        <div class="col-md-9">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Business Settings</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('settings/update-business') ?>" method="post">
                        <div class="row">
                            <!-- Appointment Settings -->
                            <div class="col-md-12 mb-3">
                                <h6 class="border-bottom pb-2">Appointment Settings</h6>
                            </div>
                            
                            <!-- Appointment Interval -->
                            <div class="col-md-4 mb-3">
                                <label for="appointment_interval" class="form-label">Appointment Interval (minutes)</label>
                                <select class="form-select" id="appointment_interval" name="appointment_interval">
                                    <option value="15" <?= ($settings['appointment_interval'] ?? '30') == '15' ? 'selected' : '' ?>>15 minutes</option>
                                    <option value="30" <?= ($settings['appointment_interval'] ?? '30') == '30' ? 'selected' : '' ?>>30 minutes</option>
                                    <option value="45" <?= ($settings['appointment_interval'] ?? '30') == '45' ? 'selected' : '' ?>>45 minutes</option>
                                    <option value="60" <?= ($settings['appointment_interval'] ?? '30') == '60' ? 'selected' : '' ?>>60 minutes</option>
                                </select>
                            </div>
                            
                            <!-- Business Hours Start -->
                            <div class="col-md-4 mb-3">
                                <label for="business_hours_start" class="form-label">Business Hours Start</label>
                                <input type="time" class="form-control" id="business_hours_start" name="business_hours_start" value="<?= $settings['business_hours_start'] ?? '09:00' ?>">
                            </div>
                            
                            <!-- Business Hours End -->
                            <div class="col-md-4 mb-3">
                                <label for="business_hours_end" class="form-label">Business Hours End</label>
                                <input type="time" class="form-control" id="business_hours_end" name="business_hours_end" value="<?= $settings['business_hours_end'] ?? '18:00' ?>">
                            </div>
                            
                            <!-- Weekend Days -->
                            <div class="col-md-12 mb-4">
                                <label class="form-label">Weekend Days</label>
                                <div class="d-flex flex-wrap">
                                    <?php 
                                    $weekend_days = explode(',', $settings['weekend_days'] ?? '0,6');
                                    $days = [
                                        0 => 'Sunday',
                                        1 => 'Monday',
                                        2 => 'Tuesday',
                                        3 => 'Wednesday',
                                        4 => 'Thursday',
                                        5 => 'Friday',
                                        6 => 'Saturday'
                                    ];
                                    
                                    foreach ($days as $value => $day): 
                                    ?>
                                        <div class="form-check me-3">
                                            <input class="form-check-input" type="checkbox" id="weekend_day_<?= $value ?>" name="weekend_days[]" value="<?= $value ?>" <?= in_array($value, $weekend_days) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="weekend_day_<?= $value ?>"><?= $day ?></label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            
                            <!-- Invoice Settings -->
                            <div class="col-md-12 mb-3">
                                <h6 class="border-bottom pb-2">Invoice Settings</h6>
                            </div>
                            
                            <!-- Invoice Prefix -->
                            <div class="col-md-6 mb-3">
                                <label for="invoice_prefix" class="form-label">Invoice Prefix</label>
                                <input type="text" class="form-control" id="invoice_prefix" name="invoice_prefix" value="<?= $settings['invoice_prefix'] ?? 'INV-' ?>">
                            </div>
                            
                            <!-- Invoice Footer -->
                            <div class="col-md-12 mb-3">
                                <label for="invoice_footer" class="form-label">Invoice Footer</label>
                                <textarea class="form-control" id="invoice_footer" name="invoice_footer" rows="3"><?= $settings['invoice_footer'] ?? 'Thank you for your business!' ?></textarea>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="col-md-12 mt-3 text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> Save Changes
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
