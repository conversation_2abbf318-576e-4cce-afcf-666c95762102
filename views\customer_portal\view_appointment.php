<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-calendar-check"></i> Appointment Details</h1>
        <div>
            <a href="<?= base_url('customer/appointments') ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Appointments
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Appointment Details -->
        <div class="col-md-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Appointment Information</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Service</label>
                                <div class="fw-bold fs-5"><?= $appointment['service_name'] ?></div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Date</label>
                                <div class="fw-bold"><?= format_date($appointment['appointment_date']) ?></div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Time</label>
                                <div class="fw-bold"><?= format_time($appointment['start_time']) ?> - <?= format_time($appointment['end_time']) ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Staff</label>
                                <div class="fw-bold"><?= $appointment['staff_name'] ?></div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Duration</label>
                                <div class="fw-bold"><?= $appointment['service_duration'] ?> minutes</div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label text-muted">Price</label>
                                <div class="fw-bold"><?= format_currency($appointment['service_price']) ?></div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label text-muted">Status</label>
                        <div>
                            <?php
                            $status_class = '';
                            $status_text = ucfirst($appointment['status']);
                            
                            switch ($appointment['status']) {
                                case 'pending':
                                    $status_class = 'bg-warning';
                                    break;
                                case 'confirmed':
                                    $status_class = 'bg-info';
                                    break;
                                case 'completed':
                                    $status_class = 'bg-success';
                                    break;
                                case 'cancelled':
                                    $status_class = 'bg-danger';
                                    break;
                                default:
                                    $status_class = 'bg-secondary';
                            }
                            
                            // Check if appointment date is in the past but status is still pending/confirmed
                            if (($appointment['status'] == 'pending' || $appointment['status'] == 'confirmed') && 
                                strtotime($appointment['appointment_date']) < strtotime('today')) {
                                $status_class = 'bg-secondary';
                                $status_text = 'Missed';
                            }
                            ?>
                            <span class="badge <?= $status_class ?> p-2" style="font-size: 1rem;">
                                <?= $status_text ?>
                            </span>
                        </div>
                    </div>

                    <?php if (!empty($appointment['notes'])): ?>
                    <div class="mb-4">
                        <label class="form-label text-muted">Notes</label>
                        <div class="p-3 bg-light rounded">
                            <?= nl2br($appointment['notes']) ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="alert alert-info">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-info-circle fa-2x"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading">Appointment Policy</h5>
                                <p class="mb-0">If you need to cancel or reschedule your appointment, please contact us at least 24 hours in advance.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-md-4 mb-4">
            <!-- Service Details -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Service Details</h5>
                </div>
                <div class="card-body">
                    <h5 class="mb-3"><?= $appointment['service_name'] ?></h5>
                    <div class="mb-3">
                        <div class="d-flex mb-2">
                            <div class="icon-circle bg-light text-primary me-3">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <div class="text-muted small">Duration</div>
                                <div class="fw-bold"><?= $appointment['service_duration'] ?> minutes</div>
                            </div>
                        </div>
                        <div class="d-flex mb-2">
                            <div class="icon-circle bg-light text-primary me-3">
                                <i class="fas fa-money-bill"></i>
                            </div>
                            <div>
                                <div class="text-muted small">Price</div>
                                <div class="fw-bold"><?= format_currency($appointment['service_price']) ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <?php
                    $today = date('Y-m-d');
                    $appointment_date = $appointment['appointment_date'];
                    $is_future_appointment = strtotime($appointment_date) > strtotime($today);
                    $is_active = $appointment['status'] == 'pending' || $appointment['status'] == 'confirmed';
                    ?>

                    <?php if ($is_future_appointment && $is_active): ?>
                        <div class="d-grid gap-2">
                            <a href="<?= base_url('booking') ?>" class="btn btn-primary">
                                <i class="fas fa-calendar-alt"></i> Book Another Appointment
                            </a>
                            <a href="tel:+1234567890" class="btn btn-outline-primary">
                                <i class="fas fa-phone"></i> Contact Salon
                            </a>
                        </div>
                        <hr>
                        <div class="alert alert-warning">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div>
                                    <p class="mb-0">Need to cancel or reschedule? Please contact the salon directly.</p>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="d-grid gap-2">
                            <a href="<?= base_url('booking') ?>" class="btn btn-primary">
                                <i class="fas fa-calendar-plus"></i> Book New Appointment
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}
</style>
