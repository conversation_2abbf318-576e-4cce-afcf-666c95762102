<?php
/**
 * Membership Controller
 */
class MembershipController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            $this->redirect(base_url('login'));
        }

        // Check if user has permission - temporarily disabled until permission system is implemented
        // if (!has_permission('memberships')) {
        //     flash('error', 'You do not have permission to access memberships');
        //     $this->redirect(base_url('dashboard'));
        // }
    }

    /**
     * Display all memberships
     */
    public function index() {
        // Get all memberships
        $membership_model = new MembershipModel();
        $memberships = $membership_model->all();

        // Get active members count for each membership
        foreach ($memberships as &$membership) {
            $membership['active_members'] = $membership_model->countActiveMembers($membership['id']);
        }

        // Render view
        $this->render('memberships/index', [
            'memberships' => $memberships
        ]);
    }

    /**
     * Display create membership form
     */
    public function create() {
        // Get services for benefits
        $service_model = new ServiceModel();
        $services = $service_model->all();

        // Render view
        $this->render('memberships/create', [
            'services' => $services
        ]);
    }

    /**
     * Store new membership
     */
    public function store() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('memberships'));
        }

        // Get form data
        $name = input('name');
        $description = input('description');
        $price = input('price');
        $duration = input('duration');
        $duration_unit = input('duration_unit');
        $benefits = input('benefits');
        $discount_percentage = input('discount_percentage');
        $status = input('status');

        // Validate data
        if (empty($name)) {
            flash('error', 'Membership name is required');
            $this->redirect(base_url('memberships/create'));
        }

        if (empty($price) || !is_numeric($price) || $price < 0) {
            flash('error', 'Please enter a valid price');
            $this->redirect(base_url('memberships/create'));
        }

        if (empty($duration) || !is_numeric($duration) || $duration <= 0) {
            flash('error', 'Please enter a valid duration');
            $this->redirect(base_url('memberships/create'));
        }

        // Create membership
        $membership_model = new MembershipModel();

        // Convert duration to days based on the selected unit
        $days = $duration;
        if ($duration_unit == 'months') {
            $days = $duration * 30; // Approximate
        } else if ($duration_unit == 'years') {
            $days = $duration * 365; // Approximate
        }

        $membership_data = [
            'name' => $name,
            'description' => $description,
            'price' => $price,
            'duration' => $days,
            'service_discount' => $discount_percentage,
            'product_discount' => $discount_percentage,
            'status' => $status
        ];

        $result = $membership_model->create($membership_data);

        if (!$result) {
            flash('error', 'Failed to create membership');
            $this->redirect(base_url('memberships/create'));
        }

        flash('success', 'Membership created successfully');
        $this->redirect(base_url('memberships'));
    }

    /**
     * Display edit membership form
     */
    public function edit($id) {
        // Get membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->find($id);

        if (!$membership) {
            flash('error', 'Membership not found');
            $this->redirect(base_url('memberships'));
        }

        // Parse benefits
        if (!empty($membership['benefits'])) {
            $membership['benefits'] = json_decode($membership['benefits'], true);
        } else {
            $membership['benefits'] = [];
        }

        // Get services for benefits
        $service_model = new ServiceModel();
        $services = $service_model->all();

        // Render view
        $this->render('memberships/edit', [
            'membership' => $membership,
            'services' => $services
        ]);
    }

    /**
     * Update membership
     */
    public function update($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('memberships'));
        }

        // Get membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->find($id);

        if (!$membership) {
            flash('error', 'Membership not found');
            $this->redirect(base_url('memberships'));
        }

        // Get form data
        $name = input('name');
        $description = input('description');
        $price = input('price');
        $duration = input('duration');
        $duration_unit = input('duration_unit');
        $benefits = input('benefits');
        $discount_percentage = input('discount_percentage');
        $status = input('status');

        // Validate data
        if (empty($name)) {
            flash('error', 'Membership name is required');
            $this->redirect(base_url('memberships/edit/' . $id));
        }

        if (empty($price) || !is_numeric($price) || $price < 0) {
            flash('error', 'Please enter a valid price');
            $this->redirect(base_url('memberships/edit/' . $id));
        }

        if (empty($duration) || !is_numeric($duration) || $duration <= 0) {
            flash('error', 'Please enter a valid duration');
            $this->redirect(base_url('memberships/edit/' . $id));
        }

        // Update membership

        // Convert duration to days based on the selected unit
        $days = $duration;
        if ($duration_unit == 'months') {
            $days = $duration * 30; // Approximate
        } else if ($duration_unit == 'years') {
            $days = $duration * 365; // Approximate
        }

        $membership_data = [
            'name' => $name,
            'description' => $description,
            'price' => $price,
            'duration' => $days,
            'service_discount' => $discount_percentage,
            'product_discount' => $discount_percentage,
            'status' => $status
        ];

        $result = $membership_model->update($id, $membership_data);

        if (!$result) {
            flash('error', 'Failed to update membership');
            $this->redirect(base_url('memberships/edit/' . $id));
        }

        flash('success', 'Membership updated successfully');
        $this->redirect(base_url('memberships'));
    }

    /**
     * Delete membership
     */
    public function delete($id) {
        // Get membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->find($id);

        if (!$membership) {
            flash('error', 'Membership not found');
            $this->redirect(base_url('memberships'));
        }

        // Check if membership has active members
        $active_members = $membership_model->countActiveMembers($id);

        if ($active_members > 0) {
            flash('error', 'Cannot delete membership with active members');
            $this->redirect(base_url('memberships'));
        }

        // Delete membership
        $result = $membership_model->delete($id);

        if (!$result) {
            flash('error', 'Failed to delete membership');
            $this->redirect(base_url('memberships'));
        }

        flash('success', 'Membership deleted successfully');
        $this->redirect(base_url('memberships'));
    }

    /**
     * Display membership members
     */
    public function members($id) {
        // Get membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->find($id);

        if (!$membership) {
            flash('error', 'Membership not found');
            $this->redirect(base_url('memberships'));
        }

        // Get members
        $members = $membership_model->getMembers($id);

        // Render view
        $this->render('memberships/members', [
            'membership' => $membership,
            'members' => $members
        ]);
    }

    /**
     * Display add member form
     */
    public function addMember($id) {
        // Get membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->find($id);

        if (!$membership) {
            flash('error', 'Membership not found');
            $this->redirect(base_url('memberships'));
        }

        // Get customers with user data (including names)
        $customer_model = new CustomerModel();
        $customers = $customer_model->getAllWithUsers();

        // Render view
        $this->render('memberships/add_member', [
            'membership' => $membership,
            'customers' => $customers
        ]);
    }

    /**
     * Store new member
     */
    public function storeMember($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('memberships/members/' . $id));
        }

        // Get membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->find($id);

        if (!$membership) {
            flash('error', 'Membership not found');
            $this->redirect(base_url('memberships'));
        }

        // Get form data
        $customer_id = input('customer_id');
        $start_date = input('start_date');
        $end_date = input('end_date');
        $payment_method = input('payment_method');
        $notes = input('notes');

        // Validate data
        if (empty($customer_id)) {
            flash('error', 'Please select a customer');
            $this->redirect(base_url('memberships/add-member/' . $id));
        }

        if (empty($start_date)) {
            $start_date = date('Y-m-d');
        }

        if (empty($end_date)) {
            flash('error', 'Please provide an end date');
            $this->redirect(base_url('memberships/add-member/' . $id));
        }

        // Validate that end date is after start date
        if (strtotime($end_date) <= strtotime($start_date)) {
            flash('error', 'End date must be after start date');
            $this->redirect(base_url('memberships/add-member/' . $id));
        }

        // Check if customer already has an active membership
        $existing_membership = $membership_model->getActiveMembershipByCustomerId($customer_id);

        if ($existing_membership) {
            flash('error', 'Customer already has an active membership');
            $this->redirect(base_url('memberships/add-member/' . $id));
        }

        // Create member with required fields
        $member_data = [
            'customer_id' => $customer_id,
            'plan_id' => $id,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'status' => 'active'
        ];

        // Add optional fields if they have values
        if (!empty($payment_method)) {
            $member_data['payment_method'] = $payment_method;
        }

        if (isset($membership['price']) && $membership['price'] > 0) {
            $member_data['amount_paid'] = $membership['price'];
        }

        if (!empty($notes)) {
            $member_data['notes'] = $notes;
        }

        // Debug the membership data
        error_log("Membership data: " . json_encode($membership));

        // Debug data
        error_log("Adding member with data: " . json_encode($member_data));

        try {
            $result = $membership_model->addMember($member_data);

            if (!$result) {
                error_log("Failed to add member: No result returned");
                flash('error', 'Failed to add member');
                $this->redirect(base_url('memberships/add-member/' . $id));
            }
        } catch (Exception $e) {
            error_log("Exception adding member: " . $e->getMessage());
            flash('error', 'Error adding member: ' . $e->getMessage());
            $this->redirect(base_url('memberships/add-member/' . $id));
        }

        flash('success', 'Member added successfully');
        $this->redirect(base_url('memberships/members/' . $id));
    }

    /**
     * Cancel membership
     */
    public function cancelMembership($id) {
        // Get membership record
        $membership_model = new MembershipModel();
        $membership_record = $membership_model->getMembershipRecord($id);

        if (!$membership_record) {
            flash('error', 'Membership record not found');
            $this->redirect(base_url('memberships'));
        }

        // Update membership record
        $result = $membership_model->updateMembershipRecord($id, [
            'status' => 'cancelled',
            'end_date' => date('Y-m-d')
        ]);

        if (!$result) {
            flash('error', 'Failed to cancel membership');
            // Use plan_id instead of membership_id
            $plan_id = isset($membership_record['plan_id']) ? $membership_record['plan_id'] : $membership_record['membership_id'];
            $this->redirect(base_url('memberships/members/' . $plan_id));
        }

        flash('success', 'Membership cancelled successfully');
        // Use plan_id instead of membership_id
        $plan_id = isset($membership_record['plan_id']) ? $membership_record['plan_id'] : $membership_record['membership_id'];
        $this->redirect(base_url('memberships/members/' . $plan_id));
    }

    /**
     * Renew membership
     */
    public function renewMembership($id) {
        // Get membership record
        $membership_model = new MembershipModel();
        $membership_record = $membership_model->getMembershipRecord($id);

        if (!$membership_record) {
            flash('error', 'Membership record not found');
            $this->redirect(base_url('memberships'));
        }

        // Get membership - handle both plan_id and membership_id
        $plan_id = isset($membership_record['plan_id']) ? $membership_record['plan_id'] : $membership_record['membership_id'];
        $membership = $membership_model->find($plan_id);

        if (!$membership) {
            flash('error', 'Membership not found');
            $this->redirect(base_url('memberships'));
        }

        // Calculate new end date
        $start_date = date('Y-m-d');
        $end_date = date('Y-m-d', strtotime($start_date . ' + ' . $membership['duration'] . ' days'));

        // Update membership record
        $result = $membership_model->updateMembershipRecord($id, [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'status' => 'active',
            'amount_paid' => $membership['price']
        ]);

        if (!$result) {
            flash('error', 'Failed to renew membership');
            // Use plan_id instead of membership_id
            $plan_id = isset($membership_record['plan_id']) ? $membership_record['plan_id'] : $membership_record['membership_id'];
            $this->redirect(base_url('memberships/members/' . $plan_id));
        }

        flash('success', 'Membership renewed successfully');
        // Use plan_id instead of membership_id
        $plan_id = isset($membership_record['plan_id']) ? $membership_record['plan_id'] : $membership_record['membership_id'];
        $this->redirect(base_url('memberships/members/' . $plan_id));
    }
}
