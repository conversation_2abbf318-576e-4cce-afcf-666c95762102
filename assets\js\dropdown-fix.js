/**
 * Dropdown Fix
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all dropdowns
    var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
    var dropdownList = dropdownElementList.map(function(dropdownToggleEl) {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });

    // Fix for user dropdown
    var userDropdown = document.getElementById('userMenuDropdown');
    if (userDropdown) {
        // Create a new dropdown instance
        var userDropdownInstance = new bootstrap.Dropdown(userDropdown);
    }
});
