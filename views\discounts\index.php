<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-percentage"></i> Discount Plans</h1>
        <a href="<?= base_url('discounts/create') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Discount Plan
        </a>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <?php if (empty($memberships)): ?>
                <div class="text-center py-5">
                    <img src="<?= base_url('assets/images/empty-membership.svg') ?>" alt="No Memberships" style="width: 120px; opacity: 0.5;" class="mb-3">
                    <h5>No Memberships Found</h5>
                    <p class="text-muted">You haven't created any membership plans yet.</p>
                    <a href="<?= base_url('memberships/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create First Membership
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th>Name</th>
                                <th>Duration</th>
                                <th>Price</th>
                                <th>Discount</th>
                                <th>Members</th>
                                <th>Status</th>
                                <th width="15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($memberships as $index => $membership): ?>
                                <tr>
                                    <td><?= $index + 1 ?></td>
                                    <td>
                                        <div class="fw-bold"><?= $membership['name'] ?></div>
                                        <div class="small text-muted"><?= $membership['description'] ? substr($membership['description'], 0, 50) . (strlen($membership['description']) > 50 ? '...' : '') : 'No description' ?></div>
                                    </td>
                                    <td><?= $membership['duration'] ?> days</td>
                                    <td><?= format_currency($membership['price']) ?></td>
                                    <td><?= $membership['service_discount'] ? $membership['service_discount'] . '%' : 'None' ?></td>
                                    <td>
                                        <a href="<?= base_url('discounts/members/' . $membership['id']) ?>" class="badge bg-primary">
                                            <?= $membership['active_members'] ?> active
                                        </a>
                                    </td>
                                    <td>
                                        <?php if ($membership['status'] == 'active'): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= base_url('discounts/members/' . $membership['id']) ?>" class="btn btn-outline-primary" title="View Members">
                                                <i class="fas fa-users"></i>
                                            </a>
                                            <a href="<?= base_url('discounts/edit/' . $membership['id']) ?>" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if ($membership['active_members'] == 0): ?>
                                                <a href="<?= base_url('discounts/delete/' . $membership['id']) ?>" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this membership?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
