<?php
/**
 * Gallery Controller
 *
 * Handles operations related to the gallery
 */
class GalleryController extends Controller {

    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in and has admin or manager role for admin actions
        if (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/gallery/admin') !== false) {
            if (!is_logged_in() || !has_role(['admin', 'manager'])) {
                $this->redirect(base_url('login'));
            }
        }

        // Ensure the gallery upload directory exists
        $this->ensureUploadDirectoryExists();
    }

    /**
     * Ensure the gallery upload directory exists
     */
    private function ensureUploadDirectoryExists() {
        $upload_dir = 'uploads/gallery/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
    }

    /**
     * Display gallery for frontend
     */
    public function index() {
        $category_id = isset($_GET['category']) ? (int)$_GET['category'] : null;

        $gallery_model = new GalleryModel();
        $category_model = new GalleryCategoryModel();

        $images = $gallery_model->getActive($category_id);
        $categories = $category_model->getActive();

        $this->render('gallery/index', [
            'page_title' => 'Gallery',
            'images' => $images,
            'categories' => $categories,
            'active_category' => $category_id
        ]);
    }

    /**
     * Display admin gallery page
     */
    public function admin() {
        $gallery_model = new GalleryModel();
        $category_model = new GalleryCategoryModel();

        $images = $gallery_model->getAll();
        $categories = $category_model->getAll();

        $this->render('gallery/admin', [
            'page_title' => 'Manage Gallery',
            'images' => $images,
            'categories' => $categories
        ]);
    }

    /**
     * Display form to add a new gallery image
     */
    public function add() {
        $category_model = new GalleryCategoryModel();
        $categories = $category_model->getAll('active');

        $this->render('gallery/add', [
            'page_title' => 'Add Gallery Image',
            'categories' => $categories
        ]);
    }

    /**
     * Process form to add a new gallery image
     */
    public function create() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('gallery/admin'));
        }

        // Get form data
        $title = input('title');
        $description = input('description');
        $category = input('category');
        $status = input('status');

        // Validate form data
        if (empty($title)) {
            flash('error', 'Title is required');
            $this->redirect(base_url('gallery/add'));
        }

        // Handle image upload
        $image_path = '';
        if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
            $upload_dir = 'uploads/gallery/';

            // Create directory if it doesn't exist
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            $file_name = time() . '_' . $_FILES['image']['name'];
            $upload_path = $upload_dir . $file_name;

            if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                $image_path = $upload_path;
            } else {
                flash('error', 'Failed to upload image');
                $this->redirect(base_url('gallery/add'));
            }
        } else {
            flash('error', 'Please select an image');
            $this->redirect(base_url('gallery/add'));
        }

        // Prepare data for insertion
        $data = [
            'title' => $title,
            'description' => $description,
            'category' => $category,
            'image_path' => $image_path,
            'status' => $status,
            'created_by' => $_SESSION['user_id'] ?? null
        ];

        // Add gallery image
        $gallery_model = new GalleryModel();
        $result = $gallery_model->add($data);

        if ($result) {
            flash('success', 'Gallery image added successfully');
        } else {
            flash('error', 'Failed to add gallery image');
        }

        $this->redirect(base_url('gallery/admin'));
    }

    /**
     * Display form to edit a gallery image
     */
    public function edit($id) {
        $gallery_model = new GalleryModel();
        $category_model = new GalleryCategoryModel();

        $image = $gallery_model->find($id);

        if (!$image) {
            flash('error', 'Gallery image not found');
            $this->redirect(base_url('gallery/admin'));
        }

        $categories = $category_model->getAll('active');

        $this->render('gallery/edit', [
            'page_title' => 'Edit Gallery Image',
            'image' => $image,
            'categories' => $categories
        ]);
    }

    /**
     * Process form to update a gallery image
     */
    public function update($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('gallery/admin'));
        }

        // Get gallery image
        $gallery_model = new GalleryModel();
        $image = $gallery_model->find($id);

        if (!$image) {
            flash('error', 'Gallery image not found');
            $this->redirect(base_url('gallery/admin'));
        }

        // Get form data
        $title = input('title');
        $description = input('description');
        $category = input('category');
        $status = input('status');

        // Validate form data
        if (empty($title)) {
            flash('error', 'Title is required');
            $this->redirect(base_url('gallery/edit/' . $id));
        }

        // Handle image upload
        $image_path = $image['image_path'];
        if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
            $upload_dir = 'uploads/gallery/';

            // Create directory if it doesn't exist
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            $file_name = time() . '_' . $_FILES['image']['name'];
            $upload_path = $upload_dir . $file_name;

            if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                // Delete old image if it exists
                if (!empty($image['image_path']) && file_exists($image['image_path'])) {
                    unlink($image['image_path']);
                }

                $image_path = $upload_path;
            } else {
                flash('error', 'Failed to upload image');
                $this->redirect(base_url('gallery/edit/' . $id));
            }
        }

        // Prepare data for update
        $data = [
            'title' => $title,
            'description' => $description,
            'category' => $category,
            'image_path' => $image_path,
            'status' => $status
        ];

        // Update gallery image
        $result = $gallery_model->updateImage($id, $data);

        if ($result) {
            flash('success', 'Gallery image updated successfully');
        } else {
            flash('error', 'Failed to update gallery image');
        }

        $this->redirect(base_url('gallery/admin'));
    }

    /**
     * Delete a gallery image
     */
    public function delete($id) {
        $gallery_model = new GalleryModel();
        $result = $gallery_model->deleteImage($id);

        if ($result['success']) {
            // Delete the image file if it exists
            if (!empty($result['image_path']) && file_exists($result['image_path'])) {
                unlink($result['image_path']);
            }

            flash('success', 'Gallery image deleted successfully');
        } else {
            flash('error', 'Failed to delete gallery image');
        }

        $this->redirect(base_url('gallery/admin'));
    }

    /**
     * Manage gallery categories
     */
    public function categories() {
        $category_model = new GalleryCategoryModel();
        $categories = $category_model->getAll();

        $this->render('gallery/categories', [
            'page_title' => 'Manage Gallery Categories',
            'categories' => $categories
        ]);
    }

    /**
     * Add a new gallery category
     */
    public function addCategory() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('gallery/categories'));
        }

        // Get form data
        $name = input('name');
        $description = input('description');
        $status = input('status');

        // Validate form data
        if (empty($name)) {
            flash('error', 'Name is required');
            $this->redirect(base_url('gallery/categories'));
        }

        // Prepare data for insertion
        $data = [
            'name' => $name,
            'description' => $description,
            'status' => $status
        ];

        // Add gallery category
        $category_model = new GalleryCategoryModel();
        $result = $category_model->add($data);

        if ($result) {
            flash('success', 'Gallery category added successfully');
        } else {
            flash('error', 'Failed to add gallery category');
        }

        $this->redirect(base_url('gallery/categories'));
    }

    /**
     * Update a gallery category
     */
    public function updateCategory() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('gallery/categories'));
        }

        // Get form data
        $id = input('id');
        $name = input('name');
        $description = input('description');
        $status = input('status');

        // Validate form data
        if (empty($id) || empty($name)) {
            flash('error', 'ID and name are required');
            $this->redirect(base_url('gallery/categories'));
        }

        // Prepare data for update
        $data = [
            'name' => $name,
            'description' => $description,
            'status' => $status
        ];

        // Update gallery category
        $category_model = new GalleryCategoryModel();
        $result = $category_model->updateCategory($id, $data);

        if ($result) {
            flash('success', 'Gallery category updated successfully');
        } else {
            flash('error', 'Failed to update gallery category');
        }

        $this->redirect(base_url('gallery/categories'));
    }

    /**
     * Delete a gallery category
     */
    public function deleteCategory($id) {
        $category_model = new GalleryCategoryModel();

        // Check if category has images
        if ($category_model->hasImages($id)) {
            flash('error', 'Cannot delete category with images. Please delete or move the images first.');
            $this->redirect(base_url('gallery/categories'));
        }

        $result = $category_model->deleteCategory($id);

        if ($result) {
            flash('success', 'Gallery category deleted successfully');
        } else {
            flash('error', 'Failed to delete gallery category');
        }

        $this->redirect(base_url('gallery/categories'));
    }
}
