<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-user-edit"></i> Edit Staff Member</h1>
        <a href="<?= base_url('staff') ?>" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Staff
        </a>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('staff/update/' . $staff['id']) ?>" method="post">
                <div class="row">
                    <!-- User Account Information -->
                    <div class="col-md-12 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-user-circle me-2"></i>Account Information</h5>
                    </div>

                    <!-- Name -->
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" value="<?= $staff['name'] ?>" required>
                    </div>

                    <!-- Email -->
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="email" name="email" value="<?= $staff['email'] ?>" required>
                    </div>

                    <!-- Password -->
                    <div class="col-md-6 mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password">
                        <div class="form-text">Leave blank to keep current password</div>
                    </div>

                    <!-- Confirm Password -->
                    <div class="col-md-6 mb-3">
                        <label for="confirm_password" class="form-label">Confirm Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                    </div>

                    <!-- Role -->
                    <div class="col-md-6 mb-3">
                        <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="staff" <?= isset($staff['role']) && $staff['role'] == 'staff' ? 'selected' : '' ?>>Staff</option>
                            <option value="manager" <?= isset($staff['role']) && $staff['role'] == 'manager' ? 'selected' : '' ?>>Manager</option>
                            <?php if (has_role('admin')): ?>
                                <option value="admin" <?= isset($staff['role']) && $staff['role'] == 'admin' ? 'selected' : '' ?>>Admin</option>
                            <?php endif; ?>
                        </select>
                    </div>

                    <!-- Status -->
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="active" <?= $staff['status'] == 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= $staff['status'] == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                    </div>

                    <!-- Personal Information -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-address-card me-2"></i>Personal Information</h5>
                    </div>

                    <!-- Phone -->
                    <div class="col-md-6 mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="text" class="form-control" id="phone" name="phone" value="<?= $staff['phone'] ?>">
                    </div>

                    <!-- Position -->
                    <div class="col-md-6 mb-3">
                        <label for="position" class="form-label">Position</label>
                        <input type="text" class="form-control" id="position" name="position" value="<?= $staff['position'] ?>">
                    </div>

                    <!-- Address -->
                    <div class="col-md-12 mb-3">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control" id="address" name="address" rows="3"><?= $staff['address'] ?></textarea>
                    </div>

                    <!-- Employment Details -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-briefcase me-2"></i>Employment Details</h5>
                    </div>

                    <!-- Joining Date -->
                    <div class="col-md-4 mb-3">
                        <label for="joining_date" class="form-label">Joining Date</label>
                        <input type="date" class="form-control" id="joining_date" name="joining_date" value="<?= $staff['joining_date'] ?>">
                    </div>

                    <!-- Salary -->
                    <div class="col-md-4 mb-3">
                        <label for="salary" class="form-label">Monthly Salary</label>
                        <div class="input-group">
                            <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                            <input type="number" class="form-control" id="salary" name="salary" step="0.01" min="0" value="<?= $staff['salary'] ?>">
                        </div>
                    </div>

                    <!-- Commission Rate -->
                    <div class="col-md-4 mb-3">
                        <label for="commission_rate" class="form-label">Commission Rate (%)</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="commission_rate" name="commission_rate" step="0.01" min="0" max="100" value="<?= $staff['commission_rate'] ?>">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>

                    <!-- Services -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-concierge-bell me-2"></i>Services</h5>
                        <div class="form-text mb-3">Select the services this staff member can provide</div>
                    </div>

                    <!-- Service List -->
                    <div class="col-md-12 mb-3">
                        <div class="row">
                            <?php foreach ($services as $service): ?>
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="service_<?= $service['id'] ?>" name="services[]" value="<?= $service['id'] ?>" <?= in_array($service['id'], $staff_service_ids) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="service_<?= $service['id'] ?>">
                                            <?= $service['name'] ?> (<?= format_currency($service['price']) ?>)
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-md-12 mt-4 text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-save me-2"></i> Update Staff Member
                        </button>
                        <a href="<?= base_url('staff') ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
