<?php
/**
 * Format Helper Functions
 */

/**
 * Format currency
 *
 * @param float $amount Amount
 * @param string $currency Currency code
 * @return string Formatted currency
 */
function format_currency($amount, $currency = null) {
    // Convert HTML entity to actual character for display
    $symbol = CURRENCY_SYMBOL;
    // Ensure amount is numeric and not null
    $amount = is_numeric($amount) ? (float)$amount : 0;
    return $symbol . number_format($amount, 2);
}

/**
 * Format date
 *
 * @param string $date Date string
 * @param string $format Date format
 * @return string Formatted date
 */
function format_date($date, $format = 'M d, Y') {
    return date($format, strtotime($date));
}

/**
 * Format time
 *
 * @param string $time Time string
 * @param string $format Time format
 * @return string Formatted time
 */
function format_time($time, $format = 'h:i A') {
    return date($format, strtotime($time));
}

/**
 * Format date and time
 *
 * @param string $datetime Date and time string
 * @param string $format Date and time format
 * @return string Formatted date and time
 */
function format_datetime($datetime, $format = 'M d, Y h:i A') {
    return date($format, strtotime($datetime));
}

/**
 * Get time elapsed string
 *
 * @param string $datetime Date and time string
 * @param bool $full Show full format
 * @return string Time elapsed string
 */
function time_elapsed_string($datetime, $full = false) {
    $now = new DateTime;
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);

    $diff->w = floor($diff->d / 7);
    $diff->d -= $diff->w * 7;

    $string = array(
        'y' => 'year',
        'm' => 'month',
        'w' => 'week',
        'd' => 'day',
        'h' => 'hour',
        'i' => 'minute',
        's' => 'second',
    );

    foreach ($string as $k => &$v) {
        if ($diff->$k) {
            $v = $diff->$k . ' ' . $v . ($diff->$k > 1 ? 's' : '');
        } else {
            unset($string[$k]);
        }
    }

    if (!$full) $string = array_slice($string, 0, 1);
    return $string ? implode(', ', $string) . ' ago' : 'just now';
}
