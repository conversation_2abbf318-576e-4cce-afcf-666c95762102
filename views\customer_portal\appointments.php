<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-calendar-alt"></i> My Appointments</h1>
        <div>
            <a href="<?= base_url('customer') ?>" class="btn btn-outline-primary me-2">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
            <a href="<?= base_url('booking') ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Book New Appointment
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-header bg-white">
            <ul class="nav nav-tabs card-header-tabs" id="appointmentTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="upcoming-tab" data-bs-toggle="tab" data-bs-target="#upcoming" type="button" role="tab" aria-controls="upcoming" aria-selected="true">
                        <i class="fas fa-calendar-day"></i> Upcoming
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="past-tab" data-bs-toggle="tab" data-bs-target="#past" type="button" role="tab" aria-controls="past" aria-selected="false">
                        <i class="fas fa-history"></i> Past
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="cancelled-tab" data-bs-toggle="tab" data-bs-target="#cancelled" type="button" role="tab" aria-controls="cancelled" aria-selected="false">
                        <i class="fas fa-ban"></i> Cancelled
                    </button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="appointmentTabsContent">
                <!-- Upcoming Appointments -->
                <div class="tab-pane fade show active" id="upcoming" role="tabpanel" aria-labelledby="upcoming-tab">
                    <?php
                    $upcoming_appointments = array_filter($appointments, function($appointment) {
                        return ($appointment['status'] == 'pending' || $appointment['status'] == 'confirmed') && 
                               strtotime($appointment['appointment_date']) >= strtotime('today');
                    });
                    ?>
                    
                    <?php if (empty($upcoming_appointments)): ?>
                        <div class="text-center p-5">
                            <img src="<?= base_url('assets/images/empty-calendar.svg') ?>" alt="No Appointments" style="width: 150px; opacity: 0.5;" class="mb-3">
                            <h5>No Upcoming Appointments</h5>
                            <p class="text-muted">You don't have any upcoming appointments scheduled.</p>
                            <a href="<?= base_url('booking') ?>" class="btn btn-primary mt-2">
                                <i class="fas fa-plus"></i> Book an Appointment
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Service</th>
                                        <th>Date & Time</th>
                                        <th>Staff</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($upcoming_appointments as $appointment): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?= $appointment['service_name'] ?></div>
                                                <div class="small text-muted"><?= format_currency($appointment['service_price']) ?> • <?= $appointment['service_duration'] ?> min</div>
                                            </td>
                                            <td>
                                                <div class="fw-bold"><?= format_date($appointment['appointment_date']) ?></div>
                                                <div class="small text-muted"><?= format_time($appointment['start_time']) ?> - <?= format_time($appointment['end_time']) ?></div>
                                            </td>
                                            <td><?= $appointment['staff_name'] ?></td>
                                            <td>
                                                <span class="badge <?= get_appointment_status_class($appointment['status']) ?>">
                                                    <?= ucfirst($appointment['status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('customer/view-appointment/' . $appointment['id']) ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Past Appointments -->
                <div class="tab-pane fade" id="past" role="tabpanel" aria-labelledby="past-tab">
                    <?php
                    $past_appointments = array_filter($appointments, function($appointment) {
                        return $appointment['status'] == 'completed' || 
                              (($appointment['status'] == 'pending' || $appointment['status'] == 'confirmed') && 
                               strtotime($appointment['appointment_date']) < strtotime('today'));
                    });
                    ?>
                    
                    <?php if (empty($past_appointments)): ?>
                        <div class="text-center p-5">
                            <img src="<?= base_url('assets/images/empty-calendar.svg') ?>" alt="No Appointments" style="width: 150px; opacity: 0.5;" class="mb-3">
                            <h5>No Past Appointments</h5>
                            <p class="text-muted">You don't have any past appointments.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Service</th>
                                        <th>Date & Time</th>
                                        <th>Staff</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($past_appointments as $appointment): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?= $appointment['service_name'] ?></div>
                                                <div class="small text-muted"><?= format_currency($appointment['service_price']) ?> • <?= $appointment['service_duration'] ?> min</div>
                                            </td>
                                            <td>
                                                <div class="fw-bold"><?= format_date($appointment['appointment_date']) ?></div>
                                                <div class="small text-muted"><?= format_time($appointment['start_time']) ?> - <?= format_time($appointment['end_time']) ?></div>
                                            </td>
                                            <td><?= $appointment['staff_name'] ?></td>
                                            <td>
                                                <?php if ($appointment['status'] == 'pending' || $appointment['status'] == 'confirmed'): ?>
                                                    <span class="badge bg-secondary">Missed</span>
                                                <?php else: ?>
                                                    <span class="badge <?= get_appointment_status_class($appointment['status']) ?>">
                                                        <?= ucfirst($appointment['status']) ?>
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('customer/view-appointment/' . $appointment['id']) ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Cancelled Appointments -->
                <div class="tab-pane fade" id="cancelled" role="tabpanel" aria-labelledby="cancelled-tab">
                    <?php
                    $cancelled_appointments = array_filter($appointments, function($appointment) {
                        return $appointment['status'] == 'cancelled';
                    });
                    ?>
                    
                    <?php if (empty($cancelled_appointments)): ?>
                        <div class="text-center p-5">
                            <img src="<?= base_url('assets/images/empty-calendar.svg') ?>" alt="No Appointments" style="width: 150px; opacity: 0.5;" class="mb-3">
                            <h5>No Cancelled Appointments</h5>
                            <p class="text-muted">You don't have any cancelled appointments.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Service</th>
                                        <th>Date & Time</th>
                                        <th>Staff</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($cancelled_appointments as $appointment): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?= $appointment['service_name'] ?></div>
                                                <div class="small text-muted"><?= format_currency($appointment['service_price']) ?> • <?= $appointment['service_duration'] ?> min</div>
                                            </td>
                                            <td>
                                                <div class="fw-bold"><?= format_date($appointment['appointment_date']) ?></div>
                                                <div class="small text-muted"><?= format_time($appointment['start_time']) ?> - <?= format_time($appointment['end_time']) ?></div>
                                            </td>
                                            <td><?= $appointment['staff_name'] ?></td>
                                            <td>
                                                <span class="badge bg-danger">Cancelled</span>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('customer/view-appointment/' . $appointment['id']) ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Helper function
function get_appointment_status_class($status) {
    switch ($status) {
        case 'pending':
            return 'bg-warning';
        case 'confirmed':
            return 'bg-info';
        case 'completed':
            return 'bg-success';
        case 'cancelled':
            return 'bg-danger';
        default:
            return 'bg-secondary';
    }
}
?>
