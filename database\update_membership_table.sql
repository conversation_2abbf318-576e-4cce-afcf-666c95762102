-- Add payment_method and amount_paid columns to customer_memberships table if they don't exist

-- Check if payment_method column exists
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'customer_memberships' 
AND COLUMN_NAME = 'payment_method';

-- Add payment_method column if it doesn't exist
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE customer_memberships ADD COLUMN payment_method ENUM("cash", "card", "upi", "other") NOT NULL DEFAULT "cash" AFTER end_date',
    'SELECT "payment_method column already exists"');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if amount_paid column exists
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'customer_memberships' 
AND COLUMN_NAME = 'amount_paid';

-- Add amount_paid column if it doesn't exist
SET @query = IF(@column_exists = 0, 
    'ALTER TABLE customer_memberships ADD COLUMN amount_paid DECIMAL(10, 2) NOT NULL DEFAULT 0.00 AFTER payment_method',
    'SELECT "amount_paid column already exists"');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
