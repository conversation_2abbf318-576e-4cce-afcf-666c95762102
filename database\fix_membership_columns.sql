-- Check which column exists in customer_memberships table
SET @plan_id_exists = 0;
SET @membership_id_exists = 0;

SELECT COUNT(*) INTO @plan_id_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'customer_memberships' 
AND COLUMN_NAME = 'plan_id';

SELECT COUNT(*) INTO @membership_id_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'customer_memberships' 
AND COLUMN_NAME = 'membership_id';

-- If plan_id exists but membership_id doesn't, we're good
SET @query = IF(@plan_id_exists > 0 AND @membership_id_exists = 0, 
    'SELECT "Using plan_id column, no changes needed"',
    'SELECT "Column mismatch detected"');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- If membership_id exists but plan_id doesn't, rename it to plan_id
SET @query = IF(@membership_id_exists > 0 AND @plan_id_exists = 0, 
    'ALTER TABLE customer_memberships CHANGE membership_id plan_id INT NOT NULL',
    'SELECT "No need to rename membership_id to plan_id"');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- If both exist, drop the foreign key on membership_id and drop the column
SET @query = IF(@membership_id_exists > 0 AND @plan_id_exists > 0, 
    'SELECT "Both columns exist, need to fix"',
    'SELECT "No duplicate columns"');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- If both exist, we need to fix the foreign keys and drop the duplicate column
SET @fk_exists = 0;
SELECT COUNT(*) INTO @fk_exists
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'customer_memberships'
AND CONSTRAINT_TYPE = 'FOREIGN KEY'
AND CONSTRAINT_NAME LIKE '%membership_id%';

-- Drop the foreign key if it exists
SET @query = IF(@fk_exists > 0, 
    'ALTER TABLE customer_memberships DROP FOREIGN KEY customer_memberships_ibfk_2',
    'SELECT "No foreign key on membership_id to drop"');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Drop the membership_id column if both columns exist
SET @query = IF(@membership_id_exists > 0 AND @plan_id_exists > 0, 
    'ALTER TABLE customer_memberships DROP COLUMN membership_id',
    'SELECT "No need to drop membership_id column"');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Make sure the foreign key on plan_id exists
SET @fk_plan_exists = 0;
SELECT COUNT(*) INTO @fk_plan_exists
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'customer_memberships'
AND CONSTRAINT_TYPE = 'FOREIGN KEY'
AND CONSTRAINT_NAME LIKE '%plan_id%';

-- Add the foreign key if it doesn't exist
SET @query = IF(@fk_plan_exists = 0, 
    'ALTER TABLE customer_memberships ADD CONSTRAINT fk_plan_id FOREIGN KEY (plan_id) REFERENCES membership_plans(id)',
    'SELECT "Foreign key on plan_id already exists"');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
