<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-user-plus"></i> Add Member to <?= $membership['name'] ?></h1>
        <div>
            <a href="<?= base_url('memberships/members/' . $membership['id']) ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Members
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Membership Details -->
        <div class="col-md-4 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Membership Details</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h4><?= $membership['name'] ?></h4>
                        <?php if ($membership['status'] == 'active'): ?>
                            <span class="badge bg-success">Active</span>
                        <?php else: ?>
                            <span class="badge bg-secondary">Inactive</span>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <p><?= $membership['description'] ?></p>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span class="fw-bold">Duration:</span>
                            <span><?= $membership['duration'] ?> days</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span class="fw-bold">Price:</span>
                            <span class="text-primary"><?= format_currency($membership['price']) ?></span>
                        </div>
                        <?php if ($membership['service_discount'] > 0): ?>
                            <div class="d-flex justify-content-between">
                                <span class="fw-bold">Service Discount:</span>
                                <span><?= $membership['service_discount'] ?>%</span>
                            </div>
                        <?php endif; ?>
                        <?php if ($membership['product_discount'] > 0): ?>
                            <div class="d-flex justify-content-between">
                                <span class="fw-bold">Product Discount:</span>
                                <span><?= $membership['product_discount'] ?>%</span>
                            </div>
                        <?php endif; ?>
                    </div>


                </div>
            </div>
        </div>

        <!-- Add Member Form -->
        <div class="col-md-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Add Member</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('memberships/store-member/' . $membership['id']) ?>" method="post">
                        <div class="row">
                            <!-- Customer Selection -->
                            <div class="col-md-12 mb-3">
                                <label for="customer_id" class="form-label">Select Customer <span class="text-danger">*</span></label>
                                <select class="form-select" id="customer_id" name="customer_id" required>
                                    <option value="">Select Customer</option>
                                    <?php foreach ($customers as $customer): ?>
                                        <option value="<?= $customer['id'] ?>"><?= $customer['name'] ?> (<?= $customer['phone'] ?>)</option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">
                                    <a href="<?= base_url('customers/create') ?>" target="_blank">
                                        <i class="fas fa-plus-circle"></i> Add New Customer
                                    </a>
                                </div>
                            </div>

                            <!-- Start Date -->
                            <div class="col-md-6 mb-3">
                                <label for="start_date" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?= date('Y-m-d') ?>">
                            </div>

                            <!-- End Date -->
                            <div class="col-md-6 mb-3">
                                <label for="end_date" class="form-label">End Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="end_date" name="end_date" required>
                                <div class="mt-2 d-flex flex-wrap gap-2">
                                    <button type="button" class="btn btn-outline-secondary rounded-3 px-3 py-2" id="add_1_year">
                                        <i class="fas fa-calendar"></i> 1 Year
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary rounded-3 px-3 py-2" id="add_2_years">
                                        <i class="fas fa-calendar"></i> 2 Years
                                    </button>
                                </div>
                                <div class="mt-2 d-flex flex-wrap gap-2">
                                    <button type="button" class="btn btn-outline-secondary rounded-3 px-3 py-2" id="add_3_months">
                                        <i class="fas fa-calendar"></i> 3 Months
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary rounded-3 px-3 py-2" id="add_6_months">
                                        <i class="fas fa-calendar"></i> 6 Months
                                    </button>
                                </div>
                                <div class="form-text mt-2">You can manually set the end date or use the quick buttons above</div>
                            </div>

                            <!-- Payment Method -->
                            <div class="col-md-6 mb-3">
                                <label for="payment_method" class="form-label">Payment Method</label>
                                <select class="form-select" id="payment_method" name="payment_method">
                                    <option value="cash">Cash</option>
                                    <option value="card">Card</option>
                                    <option value="upi">UPI</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>

                            <!-- Amount Paid (Read-only) -->
                            <div class="col-md-6 mb-3">
                                <label for="amount_paid" class="form-label">Amount Paid</label>
                                <div class="input-group">
                                    <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                                    <input type="text" class="form-control" id="amount_paid" name="amount_paid" value="<?= $membership['price'] ?>" readonly>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="col-md-12 mb-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                            </div>

                            <!-- Submit Button -->
                            <div class="col-md-12 mt-3 text-center">
                                <button type="submit" class="btn btn-primary btn-lg px-5">
                                    <i class="fas fa-user-plus me-2"></i> Add Member
                                </button>
                                <a href="<?= base_url('memberships/members/' . $membership['id']) ?>" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-times me-1"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.btn-outline-secondary {
    border-color: #ced4da;
    color: #495057;
    background-color: #fff;
}
.btn-outline-secondary:hover {
    background-color: #f8f9fa;
    color: #212529;
    border-color: #adb5bd;
}
.rounded-3 {
    border-radius: 0.5rem !important;
}
.gap-2 {
    gap: 0.5rem !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to add a specified period to the start date
    function addPeriodToStartDate(years, months) {
        const startDateInput = document.getElementById('start_date');
        const startDate = startDateInput.value || new Date().toISOString().split('T')[0];

        // If start date is not set, set it to today
        if (!startDateInput.value) {
            startDateInput.value = startDate;
        }

        // Create a date object from the start date
        let endDate = new Date(startDate);

        // Add the specified period
        if (years) {
            endDate.setFullYear(endDate.getFullYear() + years);
        }

        if (months) {
            endDate.setMonth(endDate.getMonth() + months);
        }

        // Format the date as YYYY-MM-DD
        const formattedDate = endDate.toISOString().split('T')[0];
        document.getElementById('end_date').value = formattedDate;
    }

    // Set up button click handlers
    document.getElementById('add_1_year').addEventListener('click', function() {
        addPeriodToStartDate(1, 0);
    });

    document.getElementById('add_2_years').addEventListener('click', function() {
        addPeriodToStartDate(2, 0);
    });

    document.getElementById('add_3_months').addEventListener('click', function() {
        addPeriodToStartDate(0, 3);
    });

    document.getElementById('add_6_months').addEventListener('click', function() {
        addPeriodToStartDate(0, 6);
    });

    // Update end date when start date changes (optional)
    document.getElementById('start_date').addEventListener('change', function() {
        // Clear the end date when start date changes to force user to select a new end date
        document.getElementById('end_date').value = '';
    });

    // Refresh customer list when returning from add customer page
    window.addEventListener('focus', function() {
        // This could be enhanced with AJAX to refresh the customer list
        console.log('Window focused, could refresh customer list here');
    });
});
</script>
