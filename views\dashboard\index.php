<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
    <div>
        <span class="badge bg-primary p-2">Today: <?= date('d M Y') ?></span>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-5 fade-in">
    <div class="col-md-3 mb-4">
        <div class="stat-card card bg-primary text-white h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">TODAY'S APPOINTMENTS</h6>
                        <h2 class="display-4 mb-0 fw-bold"><?= count($today_appointments) ?></h2>
                        <p class="mt-2 mb-0">
                            <?php if (count($today_appointments) > 0): ?>
                                <span class="badge bg-light text-primary">+<?= count($today_appointments) ?> from yesterday</span>
                            <?php else: ?>
                                <span class="badge bg-light text-primary">No appointments today</span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer py-3">
                <a href="<?= base_url('appointments') ?>" class="text-white d-flex justify-content-between align-items-center">
                    <span>View Appointments</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="stat-card card bg-success text-white h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">TODAY'S SALES</h6>
                        <h2 class="display-4 mb-0 fw-bold"><?= format_currency($today_sales['total_amount'] ?? 0) ?></h2>
                        <p class="mt-2 mb-0">
                            <?php if (($today_sales['total_amount'] ?? 0) > 0): ?>
                                <span class="badge bg-light text-success">+<?= ($today_sales['count'] ?? 0) ?> invoices</span>
                            <?php else: ?>
                                <span class="badge bg-light text-success">No sales today</span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer py-3">
                <a href="<?= base_url('billing') ?>" class="text-white d-flex justify-content-between align-items-center">
                    <span>View Sales</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="stat-card card bg-warning text-white h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">LOW STOCK PRODUCTS</h6>
                        <h2 class="display-4 mb-0 fw-bold"><?= count($low_stock_products) ?></h2>
                        <p class="mt-2 mb-0">
                            <?php if (count($low_stock_products) > 0): ?>
                                <span class="badge bg-light text-warning">Needs attention</span>
                            <?php else: ?>
                                <span class="badge bg-light text-warning">Stock is good</span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer py-3">
                <a href="<?= base_url('inventory') ?>" class="text-white d-flex justify-content-between align-items-center">
                    <span>View Inventory</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="stat-card card bg-info text-white h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50">TOTAL CUSTOMERS</h6>
                        <h2 class="display-4 mb-0 fw-bold"><?= $sales_stats['total_customers'] ?? 0 ?></h2>
                        <p class="mt-2 mb-0">
                            <span class="badge bg-light text-info">Loyal customer base</span>
                        </p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer py-3">
                <a href="<?= base_url('customers') ?>" class="text-white d-flex justify-content-between align-items-center">
                    <span>View Customers</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row fade-in" style="animation-delay: 0.2s;">
    <!-- Today's Appointments -->
    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-calendar-day"></i> Today's Appointments</h5>
                    <span class="badge bg-white text-primary"><?= count($today_appointments) ?> Today</span>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($today_appointments)): ?>
                    <div class="p-4 text-center">
                        <img src="<?= base_url('assets/images/empty-calendar.svg') ?>" alt="No Appointments" style="width: 120px; opacity: 0.5;" class="mb-3">
                        <p class="text-muted mb-0">No appointments scheduled for today.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Customer</th>
                                    <th>Service</th>
                                    <th>Staff</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($today_appointments as $appointment): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-clock text-primary me-2"></i>
                                                <strong><?= date('h:i A', strtotime($appointment['start_time'])) ?></strong>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-user-circle text-secondary me-2"></i>
                                                <?= $appointment['customer_name'] ?>
                                            </div>
                                        </td>
                                        <td><?= $appointment['service_name'] ?></td>
                                        <td><?= $appointment['staff_name'] ?></td>
                                        <td>
                                            <?php if ($appointment['status'] == 'pending'): ?>
                                                <span class="badge bg-warning">Pending</span>
                                            <?php elseif ($appointment['status'] == 'confirmed'): ?>
                                                <span class="badge bg-primary">Confirmed</span>
                                            <?php elseif ($appointment['status'] == 'completed'): ?>
                                                <span class="badge bg-success">Completed</span>
                                            <?php elseif ($appointment['status'] == 'cancelled'): ?>
                                                <span class="badge bg-danger">Cancelled</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <span class="text-muted small">Showing <?= count($today_appointments) ?> appointments</span>
                <a href="<?= base_url('appointments') ?>" class="btn btn-sm btn-primary">
                    <i class="fas fa-calendar-alt me-1"></i> View All
                </a>
            </div>
        </div>
    </div>

    <!-- Low Stock Products -->
    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header bg-warning text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Low Stock Products</h5>
                    <span class="badge bg-white text-warning"><?= count($low_stock_products) ?> Items</span>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($low_stock_products)): ?>
                    <div class="p-4 text-center">
                        <img src="<?= base_url('assets/images/inventory.svg') ?>" alt="No Low Stock" style="width: 120px; opacity: 0.5;" class="mb-3">
                        <p class="text-muted mb-0">No products with low stock. Inventory levels are good!</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Category</th>
                                    <th>Current Stock</th>
                                    <th>Min Stock</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($low_stock_products as $product): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-box text-warning me-2"></i>
                                                <strong><?= $product['name'] ?></strong>
                                            </div>
                                        </td>
                                        <td><?= $product['category_name'] ?></td>
                                        <td>
                                            <span class="badge bg-danger"><?= $product['quantity'] ?></span>
                                        </td>
                                        <td><?= $product['min_quantity'] ?></td>
                                        <td>
                                            <a href="<?= base_url('inventory/edit/' . $product['id']) ?>" class="btn btn-sm btn-outline-warning">
                                                <i class="fas fa-plus-circle"></i> Restock
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <span class="text-muted small">Showing <?= count($low_stock_products) ?> low stock items</span>
                <a href="<?= base_url('inventory') ?>" class="btn btn-sm btn-warning">
                    <i class="fas fa-boxes me-1"></i> View Inventory
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row fade-in" style="animation-delay: 0.4s;">
    <!-- Recent Customers -->
    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-user-plus"></i> Recent Customers</h5>
                    <a href="<?= base_url('customers/create') ?>" class="btn btn-sm btn-light text-info">
                        <i class="fas fa-plus"></i> Add New
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($recent_customers)): ?>
                    <div class="p-4 text-center">
                        <img src="<?= base_url('assets/images/customers.svg') ?>" alt="No Customers" style="width: 120px; opacity: 0.5;" class="mb-3">
                        <p class="text-muted mb-0">No customers added yet.</p>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_customers as $customer): ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle bg-info text-white me-3">
                                            <?= strtoupper(substr($customer['name'], 0, 1)) ?>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?= $customer['name'] ?></h6>
                                            <div class="small text-muted">
                                                <i class="fas fa-envelope me-1"></i> <?= $customer['email'] ?>
                                            </div>
                                            <div class="small text-muted">
                                                <i class="fas fa-phone me-1"></i> <?= $customer['phone'] ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="small text-muted mb-1">Joined <?= format_date($customer['created_at']) ?></div>
                                        <a href="<?= base_url('customers/edit/' . $customer['id']) ?>" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-user-edit"></i> Profile
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <span class="text-muted small">Showing <?= count($recent_customers) ?> recent customers</span>
                <a href="<?= base_url('customers') ?>" class="btn btn-sm btn-info">
                    <i class="fas fa-users me-1"></i> View All
                </a>
            </div>
        </div>
    </div>

    <!-- Sales Statistics -->
    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> Sales Statistics</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-light text-success dropdown-toggle" type="button" id="salesDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="salesDropdown">
                            <li><a class="dropdown-item" href="#">This Week</a></li>
                            <li><a class="dropdown-item" href="#">This Month</a></li>
                            <li><a class="dropdown-item" href="#">This Year</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">Custom Range</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="text-muted mb-0">Today</h6>
                                    <div class="icon-circle bg-success-light text-success">
                                        <i class="fas fa-calendar-day"></i>
                                    </div>
                                </div>
                                <h3 class="mb-0"><?= format_currency($sales_stats['today'] ?? 0) ?></h3>
                                <div class="small text-success">
                                    <i class="fas fa-arrow-up"></i> 12% from yesterday
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="text-muted mb-0">This Week</h6>
                                    <div class="icon-circle bg-primary-light text-primary">
                                        <i class="fas fa-calendar-week"></i>
                                    </div>
                                </div>
                                <h3 class="mb-0"><?= format_currency($sales_stats['this_week'] ?? 0) ?></h3>
                                <div class="small text-primary">
                                    <i class="fas fa-arrow-up"></i> 8% from last week
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="text-muted mb-0">This Month</h6>
                                    <div class="icon-circle bg-info-light text-info">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                </div>
                                <h3 class="mb-0"><?= format_currency($sales_stats['this_month'] ?? 0) ?></h3>
                                <div class="small text-info">
                                    <i class="fas fa-arrow-up"></i> 15% from last month
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="text-muted mb-0">This Year</h6>
                                    <div class="icon-circle bg-warning-light text-warning">
                                        <i class="fas fa-calendar"></i>
                                    </div>
                                </div>
                                <h3 class="mb-0"><?= format_currency($sales_stats['this_year'] ?? 0) ?></h3>
                                <div class="small text-warning">
                                    <i class="fas fa-arrow-up"></i> 20% from last year
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <span class="text-muted small">Last updated: <?= date('d M Y, h:i A') ?></span>
                <a href="<?= base_url('reports/sales') ?>" class="btn btn-sm btn-success">
                    <i class="fas fa-chart-bar me-1"></i> Detailed Reports
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

.icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.bg-success-light {
    background-color: rgba(16, 185, 129, 0.1);
}

.bg-primary-light {
    background-color: rgba(99, 102, 241, 0.1);
}

.bg-info-light {
    background-color: rgba(59, 130, 246, 0.1);
}

.bg-warning-light {
    background-color: rgba(245, 158, 11, 0.1);
}
</style>
