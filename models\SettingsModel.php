<?php
/**
 * Settings Model
 */
class SettingsModel extends Model {
    protected $table = 'settings';
    
    /**
     * Get all settings as key-value pairs
     * 
     * @return array Settings
     */
    public function get() {
        $query = "SELECT setting_key, setting_value FROM " . $this->table;
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        $settings = [];
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($results as $row) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        return $settings;
    }
    
    /**
     * Get a specific setting value
     * 
     * @param string $key Setting key
     * @param mixed $default Default value if setting not found
     * @return mixed Setting value
     */
    public function getValue($key, $default = null) {
        $query = "SELECT setting_value FROM " . $this->table . " WHERE setting_key = :key LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':key', $key);
        $stmt->execute();
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result ? $result['setting_value'] : $default;
    }
    
    /**
     * Set a setting value
     * 
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @return bool Success or failure
     */
    public function setValue($key, $value) {
        // Check if setting exists
        $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE setting_key = :key";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':key', $key);
        $stmt->execute();
        
        if ($stmt->fetchColumn() > 0) {
            // Update existing setting
            $query = "UPDATE " . $this->table . " SET setting_value = :value, updated_at = NOW() WHERE setting_key = :key";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':key', $key);
            $stmt->bindParam(':value', $value);
            
            return $stmt->execute();
        } else {
            // Insert new setting
            $query = "INSERT INTO " . $this->table . " (setting_key, setting_value) VALUES (:key, :value)";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':key', $key);
            $stmt->bindParam(':value', $value);
            
            return $stmt->execute();
        }
    }
    
    /**
     * Update multiple settings at once
     * 
     * @param array $settings Array of key-value pairs
     * @return bool Success or failure
     */
    public function updateSettings($settings) {
        $success = true;
        
        foreach ($settings as $key => $value) {
            $result = $this->setValue($key, $value);
            if (!$result) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * Delete a setting
     * 
     * @param string $key Setting key
     * @return bool Success or failure
     */
    public function deleteSetting($key) {
        $query = "DELETE FROM " . $this->table . " WHERE setting_key = :key";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':key', $key);
        
        return $stmt->execute();
    }
    
    /**
     * Initialize default settings if they don't exist
     * 
     * @return bool Success or failure
     */
    public function initializeDefaultSettings() {
        $defaultSettings = [
            'salon_name' => 'Beauty Salon',
            'address' => '123 Main Street, City, Country',
            'phone' => '+1234567890',
            'email' => '<EMAIL>',
            'tax_rate' => '18',
            'currency' => 'USD',
            'currency_symbol' => '$',
            'appointment_interval' => '30',
            'business_hours_start' => '09:00',
            'business_hours_end' => '18:00',
            'weekend_days' => '0,6', // Sunday and Saturday
            'invoice_prefix' => 'INV-',
            'invoice_footer' => 'Thank you for your business!',
            'theme_color' => '#3498db',
            'logo' => '',
            'favicon' => '',
            'smtp_host' => '',
            'smtp_port' => '',
            'smtp_username' => '',
            'smtp_password' => '',
            'smtp_encryption' => 'tls'
        ];
        
        $success = true;
        
        foreach ($defaultSettings as $key => $value) {
            // Only set if not already exists
            if ($this->getValue($key) === null) {
                $result = $this->setValue($key, $value);
                if (!$result) {
                    $success = false;
                }
            }
        }
        
        return $success;
    }
}
