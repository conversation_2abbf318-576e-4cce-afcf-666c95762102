<?php
/**
 * Notifications Controller
 */
class NotificationsController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            $this->redirect(base_url('login'));
        }
    }
    
    /**
     * Display all notifications
     */
    public function index() {
        $user_id = $_SESSION['user_id'];
        $notification_model = new NotificationModel();
        $notifications = $notification_model->getAllForUser($user_id);
        
        $this->render('notifications/index', [
            'notifications' => $notifications
        ]);
    }
    
    /**
     * Mark notification as read
     */
    public function markAsRead() {
        // Check if request is POST
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('notifications'));
        }
        
        $notification_id = input('notification_id');
        
        if (!$notification_id) {
            flash('error', 'Invalid notification ID');
            $this->redirect(base_url('notifications'));
        }
        
        $notification_model = new NotificationModel();
        $result = $notification_model->markAsRead($notification_id);
        
        if ($result) {
            flash('success', 'Notification marked as read');
        } else {
            flash('error', 'Failed to mark notification as read');
        }
        
        // Redirect back to referrer or notifications page
        $redirect_url = $_SERVER['HTTP_REFERER'] ?? base_url('notifications');
        $this->redirect($redirect_url);
    }
    
    /**
     * Mark all notifications as read
     */
    public function markAllAsRead() {
        // Check if request is POST
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('notifications'));
        }
        
        $user_id = $_SESSION['user_id'];
        $notification_model = new NotificationModel();
        $result = $notification_model->markAllAsRead($user_id);
        
        if ($result) {
            flash('success', 'All notifications marked as read');
        } else {
            flash('error', 'Failed to mark notifications as read');
        }
        
        // Redirect back to referrer or notifications page
        $redirect_url = $_SERVER['HTTP_REFERER'] ?? base_url('notifications');
        $this->redirect($redirect_url);
    }
    
    /**
     * Get notifications for AJAX request
     */
    public function getNotifications() {
        // Check if request is AJAX
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] != 'XMLHttpRequest') {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid request']);
            exit;
        }
        
        $user_id = $_SESSION['user_id'];
        $notification_model = new NotificationModel();
        $notifications = $notification_model->getUnreadForUser($user_id);
        $count = count($notifications);
        
        echo json_encode([
            'count' => $count,
            'notifications' => $notifications
        ]);
        exit;
    }
}
