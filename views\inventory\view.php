<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-box"></i> Product Details</h1>
        <div>
            <a href="<?= base_url('inventory') ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Products
            </a>
            <div class="btn-group ms-2">
                <a href="<?= base_url('inventory/edit/' . $product['id']) ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-edit me-1"></i> Edit
                </a>
                <a href="<?= base_url('inventory/adjust-stock/' . $product['id']) ?>" class="btn btn-outline-info">
                    <i class="fas fa-boxes me-1"></i> Adjust Stock
                </a>
                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteProductModal">
                    <i class="fas fa-trash me-1"></i> Delete
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Product Details -->
        <div class="col-md-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Product Information</h5>
                    <span class="badge <?= $product['status'] == 'active' ? 'bg-success' : 'bg-secondary' ?>">
                        <?= ucfirst($product['status']) ?>
                    </span>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4 text-center mb-3 mb-md-0">
                            <?php if (!empty($product['image'])): ?>
                                <img src="<?= base_url('uploads/products/' . $product['image']) ?>" alt="<?= $product['name'] ?>" class="img-fluid rounded" style="max-height: 200px;">
                            <?php else: ?>
                                <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 200px;">
                                    <i class="fas fa-box fa-4x text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-8">
                            <h4><?= $product['name'] ?></h4>
                            <?php if (!empty($product['brand'])): ?>
                                <p class="text-muted mb-2"><i class="fas fa-trademark me-2"></i> <?= $product['brand'] ?></p>
                            <?php endif; ?>
                            <?php if (!empty($product['sku'])): ?>
                                <p class="mb-2"><strong>SKU:</strong> <?= $product['sku'] ?></p>
                            <?php endif; ?>
                            <p class="mb-2"><strong>Category:</strong> <?= $product['category_name'] ? $product['category_name'] : 'Uncategorized' ?></p>
                            <div class="d-flex align-items-center mb-2">
                                <strong class="me-3">Price:</strong>
                                <span class="fs-5 fw-bold text-primary"><?= format_currency($product['selling_price']) ?></span>
                            </div>
                            <div class="d-flex align-items-center">
                                <strong class="me-3">Stock:</strong>
                                <?php if ($product['quantity'] <= 0): ?>
                                    <span class="badge bg-danger">Out of Stock</span>
                                <?php elseif (isset($product['low_stock_threshold']) && $product['quantity'] <= $product['low_stock_threshold']): ?>
                                    <span class="badge bg-warning"><?= $product['quantity'] ?> Left</span>
                                <?php else: ?>
                                    <span class="badge bg-success"><?= $product['quantity'] ?> In Stock</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($product['description'])): ?>
                        <div class="mb-4">
                            <h6 class="border-bottom pb-2">Description</h6>
                            <p><?= nl2br($product['description']) ?></p>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="border-bottom pb-2">Pricing Details</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td>Purchase Price:</td>
                                    <td class="text-end"><?= format_currency($product['cost_price'] ?? 0) ?></td>
                                </tr>
                                <tr>
                                    <td>Selling Price:</td>
                                    <td class="text-end"><?= format_currency($product['selling_price'] ?? 0) ?></td>
                                </tr>
                                <tr>
                                    <td>Profit Margin:</td>
                                    <td class="text-end">
                                        <?php
                                        $cost_price = $product['cost_price'] ?? 0;
                                        $selling_price = $product['selling_price'] ?? 0;
                                        $profit = $selling_price - $cost_price;
                                        $margin = ($selling_price > 0) ? ($profit / $selling_price) * 100 : 0;
                                        echo number_format($margin, 2) . '%';
                                        ?>
                                    </td>
                                </tr>
                                <?php if (isset($product['tax_rate']) && $product['tax_rate'] > 0): ?>
                                    <tr>
                                        <td>Tax Rate:</td>
                                        <td class="text-end"><?= $product['tax_rate'] ?>%</td>
                                    </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="border-bottom pb-2">Inventory Details</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td>Current Stock:</td>
                                    <td class="text-end"><?= $product['quantity'] ?? 0 ?></td>
                                </tr>
                                <tr>
                                    <td>Low Stock Threshold:</td>
                                    <td class="text-end"><?= $product['low_stock_threshold'] ?? 0 ?></td>
                                </tr>
                                <?php if (!empty($product['supplier'])): ?>
                                    <tr>
                                        <td>Supplier:</td>
                                        <td class="text-end"><?= $product['supplier'] ?></td>
                                    </tr>
                                <?php endif; ?>
                                <?php if (!empty($product['expiry_date'])): ?>
                                    <tr>
                                        <td>Expiry Date:</td>
                                        <td class="text-end"><?= format_date($product['expiry_date']) ?></td>
                                    </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="small text-muted">Created</div>
                            <div><?= format_date($product['created_at']) ?></div>
                        </div>
                        <div class="col-4">
                            <div class="small text-muted">Last Updated</div>
                            <div><?= format_date($product['updated_at']) ?></div>
                        </div>
                        <div class="col-4">
                            <div class="small text-muted">Created By</div>
                            <div><?= $product['created_by_name'] ?? 'System' ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-md-4 mb-4">
            <!-- Stock History -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Stock History</h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($stock_history)): ?>
                        <div class="p-3 text-center">
                            <p class="text-muted mb-0">No stock adjustments recorded yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($stock_history as $history): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">
                                                <?php if ($history['adjustment_type'] == 'add'): ?>
                                                    <span class="text-success">+<?= abs($history['quantity_change']) ?></span>
                                                <?php elseif ($history['adjustment_type'] == 'remove'): ?>
                                                    <span class="text-danger">-<?= abs($history['quantity_change']) ?></span>
                                                <?php else: ?>
                                                    <span>Set to <?= $history['quantity_change'] ?></span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="small text-muted"><?= $history['reason'] ?></div>
                                        </div>
                                        <div class="text-end">
                                            <div class="small"><?= format_date($history['created_at']) ?></div>
                                            <div class="small text-muted"><?= $history['user_name'] ?? 'System' ?></div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                    <button type="button" class="btn btn-sm btn-primary w-100" data-bs-toggle="modal" data-bs-target="#stockAdjustmentModal">
                        <i class="fas fa-plus me-1"></i> Adjust Stock
                    </button>
                </div>
            </div>

            <!-- Sales History -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">Sales History</h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($sales_history)): ?>
                        <div class="p-3 text-center">
                            <p class="text-muted mb-0">No sales recorded for this product yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($sales_history as $sale): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold">Invoice #<?= str_pad($sale['invoice_id'], 6, '0', STR_PAD_LEFT) ?></div>
                                            <div class="small text-muted"><?= $sale['quantity'] ?> units @ <?= format_currency($sale['unit_price']) ?></div>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold"><?= format_currency($sale['total_price']) ?></div>
                                            <div class="small text-muted"><?= format_date($sale['created_at']) ?></div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                    <a href="<?= base_url('billing/create?product_id=' . $product['id']) ?>" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-shopping-cart me-1"></i> Create New Sale
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stock Adjustment Modal -->
<div class="modal fade" id="stockAdjustmentModal" tabindex="-1" aria-labelledby="stockAdjustmentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stockAdjustmentModalLabel">Adjust Stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= base_url('inventory/update-stock/' . $product['id']) ?>" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Product</label>
                        <div class="form-control bg-light"><?= $product['name'] ?></div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Current Stock</label>
                        <div class="form-control bg-light"><?= $product['quantity'] ?></div>
                    </div>
                    <div class="mb-3">
                        <label for="adjustment_type" class="form-label">Adjustment Type</label>
                        <select class="form-select" id="adjustment_type" name="adjustment_type">
                            <option value="add">Add Stock</option>
                            <option value="subtract">Remove Stock</option>
                            <option value="set">Set Exact Stock</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="quantity" class="form-label">Quantity</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" min="0" required>
                        <div class="form-text" id="quantity_help">Enter the quantity to add, remove, or set.</div>
                    </div>
                    <div class="mb-3">
                        <label for="reason" class="form-label">Reason</label>
                        <textarea class="form-control" id="reason" name="reason" rows="2" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- JavaScript for Stock Adjustment Modal -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const adjustmentTypeSelect = document.getElementById('adjustment_type');
    const quantityInput = document.getElementById('quantity');
    const quantityHelp = document.getElementById('quantity_help');
    const currentStock = <?= $product['quantity'] ?>;

    adjustmentTypeSelect.addEventListener('change', function() {
        updateQuantityHelp();
    });

    function updateQuantityHelp() {
        const adjustmentType = adjustmentTypeSelect.value;

        if (adjustmentType === 'add') {
            quantityHelp.textContent = 'Enter the quantity to add to the current stock.';
            quantityInput.min = 1;
            quantityInput.max = '';
        } else if (adjustmentType === 'subtract') {
            quantityHelp.textContent = `Enter the quantity to remove from the current stock (max: ${currentStock}).`;
            quantityInput.min = 1;
            quantityInput.max = currentStock;
        } else if (adjustmentType === 'set') {
            quantityHelp.textContent = 'Enter the exact quantity to set as the new stock level.';
            quantityInput.min = 0;
            quantityInput.max = '';
        }
    }

    // Initialize help text
    updateQuantityHelp();
});
</script>

<!-- Delete Product Modal -->
<div class="modal fade" id="deleteProductModal" tabindex="-1" aria-labelledby="deleteProductModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteProductModalLabel">Delete Product</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= base_url('inventory/delete') ?>" method="post">
                <input type="hidden" name="product_id" value="<?= $product['id'] ?>">
                <div class="modal-body">
                    <p>Are you sure you want to delete the product "<?= $product['name'] ?>"?</p>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i> This action cannot be undone. All data related to this product will be permanently deleted.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Product</button>
                </div>
            </form>
        </div>
    </div>
</div>
