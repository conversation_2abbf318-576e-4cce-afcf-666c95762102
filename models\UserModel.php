<?php
/**
 * User Model
 */
class UserModel extends Model {
    protected $table = 'users';

    /**
     * Find user by email
     *
     * @param string $email Email
     * @param int|null $exclude_id User ID to exclude (optional)
     * @return array|false User data or false if not found
     */
    public function findByEmail($email, $exclude_id = null) {
        if ($exclude_id) {
            $query = "SELECT * FROM " . $this->table . " WHERE email = :email AND id != :exclude_id LIMIT 1";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':exclude_id', $exclude_id);
        } else {
            $query = "SELECT * FROM " . $this->table . " WHERE email = :email LIMIT 1";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':email', $email);
        }

        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Check if email exists
     *
     * @param string $email Email
     * @param int|null $exclude_id User ID to exclude (optional)
     * @return bool
     */
    public function emailExists($email, $exclude_id = null) {
        if ($exclude_id) {
            $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE email = :email AND id != :exclude_id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':exclude_id', $exclude_id);
        } else {
            $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE email = :email";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':email', $email);
        }

        $stmt->execute();

        return $stmt->fetchColumn() > 0;
    }

    /**
     * Save remember token
     *
     * @param int $user_id User ID
     * @param string $token Token
     * @param int $expires Expiry timestamp
     * @return bool Success or failure
     */
    public function saveRememberToken($user_id, $token, $expires) {
        $query = "UPDATE " . $this->table . " SET remember_token = :token, remember_expires = :expires WHERE id = :user_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':token', $token);
        $stmt->bindParam(':expires', $expires);
        $stmt->bindParam(':user_id', $user_id);

        return $stmt->execute();
    }

    /**
     * Find user by remember token
     *
     * @param string $token Token
     * @return array|false User data or false if not found
     */
    public function findByRememberToken($token) {
        $query = "SELECT * FROM " . $this->table . " WHERE remember_token = :token AND remember_expires > :now LIMIT 1";
        $stmt = $this->db->prepare($query);
        $now = time();
        $stmt->bindParam(':token', $token);
        $stmt->bindParam(':now', $now);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Save reset token
     *
     * @param int $user_id User ID
     * @param string $token Token
     * @param int $expires Expiry timestamp
     * @return bool Success or failure
     */
    public function saveResetToken($user_id, $token, $expires) {
        $query = "UPDATE " . $this->table . " SET reset_token = :token, reset_expires = :expires WHERE id = :user_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':token', $token);
        $stmt->bindParam(':expires', $expires);
        $stmt->bindParam(':user_id', $user_id);

        return $stmt->execute();
    }

    /**
     * Find user by reset token
     *
     * @param string $token Token
     * @return array|false User data or false if not found
     */
    public function findByResetToken($token) {
        $query = "SELECT * FROM " . $this->table . " WHERE reset_token = :token AND reset_expires > :now LIMIT 1";
        $stmt = $this->db->prepare($query);
        $now = time();
        $stmt->bindParam(':token', $token);
        $stmt->bindParam(':now', $now);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Update password
     *
     * @param int $user_id User ID
     * @param string $password Hashed password
     * @return bool Success or failure
     */
    public function updatePassword($user_id, $password) {
        $query = "UPDATE " . $this->table . " SET password = :password WHERE id = :user_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':password', $password);
        $stmt->bindParam(':user_id', $user_id);

        return $stmt->execute();
    }

    /**
     * Clear reset token
     *
     * @param int $user_id User ID
     * @return bool Success or failure
     */
    public function clearResetToken($user_id) {
        $query = "UPDATE " . $this->table . " SET reset_token = NULL, reset_expires = NULL WHERE id = :user_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);

        return $stmt->execute();
    }

    /**
     * Get users by role
     *
     * @param string $role Role
     * @return array Users
     */
    public function getByRole($role) {
        $query = "SELECT * FROM " . $this->table . " WHERE role = :role";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':role', $role);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
