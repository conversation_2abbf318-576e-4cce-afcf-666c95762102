<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-plus-circle"></i> Create Payroll</h1>
        <a href="<?= base_url('payroll') ?>" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Payroll
        </a>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('payroll/store') ?>" method="post" id="payrollForm">
                <div class="row">
                    <!-- Staff Selection -->
                    <div class="col-md-12 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-user me-2"></i>Staff Information</h5>
                    </div>

                    <!-- Staff -->
                    <div class="col-md-6 mb-3">
                        <label for="staff_id" class="form-label">Select Staff <span class="text-danger">*</span></label>
                        <select class="form-select" id="staff_id" name="staff_id" required>
                            <option value="">Select Staff Member</option>
                            <?php foreach ($staff as $member): ?>
                                <?php if ($member['status'] == 'active'): ?>
                                    <option value="<?= $member['id'] ?>" data-salary="<?= $member['salary'] ?>" data-commission="<?= $member['commission_rate'] ?>">
                                        <?= $member['name'] ?> (<?= $member['position'] ?>)
                                    </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Pay Period -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-calendar-alt me-2"></i>Pay Period</h5>
                    </div>

                    <!-- Pay Period Start -->
                    <div class="col-md-4 mb-3">
                        <label for="pay_period_start" class="form-label">Start Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="pay_period_start" name="pay_period_start" required>
                    </div>

                    <!-- Pay Period End -->
                    <div class="col-md-4 mb-3">
                        <label for="pay_period_end" class="form-label">End Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="pay_period_end" name="pay_period_end" required>
                    </div>

                    <!-- Payment Date -->
                    <div class="col-md-4 mb-3">
                        <label for="payment_date" class="form-label">Payment Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?= date('Y-m-d') ?>" required>
                    </div>

                    <!-- Calculate Commissions Button -->
                    <div class="col-md-12 mb-3">
                        <button type="button" id="calculateCommissions" class="btn btn-outline-primary">
                            <i class="fas fa-calculator me-1"></i> Calculate Commissions
                        </button>
                        <div class="form-text">Calculate commissions based on services performed during the pay period</div>
                    </div>

                    <!-- Commission Details -->
                    <div class="col-md-12 mb-3" id="commissionsContainer" style="display: none;">
                        <div class="card border">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Commission Details</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm" id="commissionsTable">
                                        <thead>
                                            <tr>
                                                <th>Service</th>
                                                <th>Invoice #</th>
                                                <th>Date</th>
                                                <th>Service Amount</th>
                                                <th>Commission Rate</th>
                                                <th>Commission Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Commission rows will be added here -->
                                        </tbody>
                                        <tfoot>
                                            <tr class="table-light">
                                                <th colspan="5" class="text-end">Total Commission:</th>
                                                <th id="totalCommission">0.00</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Salary Details -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-money-bill-wave me-2"></i>Salary Details</h5>
                    </div>

                    <!-- Basic Salary -->
                    <div class="col-md-3 mb-3">
                        <label for="basic_salary" class="form-label">Basic Salary</label>
                        <div class="input-group">
                            <span class="input-group-text"><?= html_entity_decode(CURRENCY_SYMBOL, ENT_HTML5, "UTF-8") ?></span>
                            <input type="number" class="form-control" id="basic_salary" name="basic_salary" step="0.01" min="0" readonly>
                        </div>
                    </div>

                    <!-- Commission Amount -->
                    <div class="col-md-3 mb-3">
                        <label for="commission_amount" class="form-label">Commission</label>
                        <div class="input-group">
                            <span class="input-group-text"><?= html_entity_decode(CURRENCY_SYMBOL, ENT_HTML5, "UTF-8") ?></span>
                            <input type="number" class="form-control" id="commission_amount" name="commission_amount" step="0.01" min="0" value="0">
                        </div>
                    </div>

                    <!-- Bonus -->
                    <div class="col-md-3 mb-3">
                        <label for="bonus" class="form-label">Bonus</label>
                        <div class="input-group">
                            <span class="input-group-text"><?= html_entity_decode(CURRENCY_SYMBOL, ENT_HTML5, "UTF-8") ?></span>
                            <input type="number" class="form-control" id="bonus" name="bonus" step="0.01" min="0" value="0">
                        </div>
                    </div>

                    <!-- Deductions -->
                    <div class="col-md-3 mb-3">
                        <label for="deductions" class="form-label">Deductions</label>
                        <div class="input-group">
                            <span class="input-group-text"><?= html_entity_decode(CURRENCY_SYMBOL, ENT_HTML5, "UTF-8") ?></span>
                            <input type="number" class="form-control" id="deductions" name="deductions" step="0.01" min="0" value="0">
                        </div>
                    </div>

                    <!-- Net Salary -->
                    <div class="col-md-6 mb-3">
                        <label for="net_salary" class="form-label">Net Salary</label>
                        <div class="input-group">
                            <span class="input-group-text"><?= html_entity_decode(CURRENCY_SYMBOL, ENT_HTML5, "UTF-8") ?></span>
                            <input type="number" class="form-control" id="net_salary" name="net_salary" step="0.01" min="0" readonly>
                        </div>
                        <div class="form-text">Basic Salary + Commission + Bonus - Deductions</div>
                    </div>

                    <!-- Payment Method -->
                    <div class="col-md-6 mb-3">
                        <label for="payment_method" class="form-label">Payment Method</label>
                        <select class="form-select" id="payment_method" name="payment_method">
                            <option value="cash">Cash</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="check">Check</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <!-- Status -->
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="pending">Pending</option>
                            <option value="paid">Paid</option>
                        </select>
                    </div>

                    <!-- Notes -->
                    <div class="col-md-6 mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>

                    <!-- Hidden field for commission data -->
                    <input type="hidden" id="commission_data" name="commission_data" value="">

                    <!-- Submit Button -->
                    <div class="col-md-12 mt-4 text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-save me-2"></i> Create Payroll
                        </button>
                        <a href="<?= base_url('payroll') ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set staff_id if provided in URL
    const urlParams = new URLSearchParams(window.location.search);
    const staffId = urlParams.get('staff_id');
    if (staffId) {
        document.getElementById('staff_id').value = staffId;
        updateBasicSalary();
    }

    // Update basic salary when staff changes
    document.getElementById('staff_id').addEventListener('change', updateBasicSalary);

    // Calculate net salary when any amount changes
    document.getElementById('basic_salary').addEventListener('input', calculateNetSalary);
    document.getElementById('commission_amount').addEventListener('input', calculateNetSalary);
    document.getElementById('bonus').addEventListener('input', calculateNetSalary);
    document.getElementById('deductions').addEventListener('input', calculateNetSalary);

    // Calculate commissions button
    document.getElementById('calculateCommissions').addEventListener('click', calculateCommissions);

    // Form submission
    document.getElementById('payrollForm').addEventListener('submit', function(e) {
        // Validate form
        const staffId = document.getElementById('staff_id').value;
        const startDate = document.getElementById('pay_period_start').value;
        const endDate = document.getElementById('pay_period_end').value;

        if (!staffId) {
            e.preventDefault();
            alert('Please select a staff member');
            return;
        }

        if (!startDate || !endDate) {
            e.preventDefault();
            alert('Please select pay period start and end dates');
            return;
        }

        if (new Date(startDate) > new Date(endDate)) {
            e.preventDefault();
            alert('Start date cannot be after end date');
            return;
        }
    });

    // Function to update basic salary
    function updateBasicSalary() {
        const staffSelect = document.getElementById('staff_id');
        const basicSalaryInput = document.getElementById('basic_salary');

        if (staffSelect.value) {
            const selectedOption = staffSelect.options[staffSelect.selectedIndex];
            const salary = selectedOption.getAttribute('data-salary');
            basicSalaryInput.value = salary || 0;
            calculateNetSalary();
        } else {
            basicSalaryInput.value = '';
        }
    }

    // Function to calculate net salary
    function calculateNetSalary() {
        const basicSalary = parseFloat(document.getElementById('basic_salary').value) || 0;
        const commission = parseFloat(document.getElementById('commission_amount').value) || 0;
        const bonus = parseFloat(document.getElementById('bonus').value) || 0;
        const deductions = parseFloat(document.getElementById('deductions').value) || 0;

        const netSalary = basicSalary + commission + bonus - deductions;
        document.getElementById('net_salary').value = netSalary.toFixed(2);
    }

    // Function to calculate commissions
    function calculateCommissions() {
        const staffId = document.getElementById('staff_id').value;
        const startDate = document.getElementById('pay_period_start').value;
        const endDate = document.getElementById('pay_period_end').value;

        if (!staffId) {
            alert('Please select a staff member');
            return;
        }

        if (!startDate || !endDate) {
            alert('Please select pay period start and end dates');
            return;
        }

        if (new Date(startDate) > new Date(endDate)) {
            alert('Start date cannot be after end date');
            return;
        }

        // Show loading indicator
        document.getElementById('calculateCommissions').innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Calculating...';
        document.getElementById('calculateCommissions').disabled = true;

        // Make AJAX request to calculate commissions
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '<?= base_url('payroll/calculateCommissions') ?>', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

        xhr.onload = function() {
            // Reset button first
            document.getElementById('calculateCommissions').innerHTML = '<i class="fas fa-calculator me-1"></i> Calculate Commissions';
            document.getElementById('calculateCommissions').disabled = false;

            if (xhr.status === 200) {
                try {
                    console.log('Response:', xhr.responseText); // Debug: Log the raw response
                    const response = JSON.parse(xhr.responseText);

                    if (response.success) {
                        // Update commission table
                        const commissionsTable = document.getElementById('commissionsTable').getElementsByTagName('tbody')[0];
                        commissionsTable.innerHTML = '';

                        if (response.commissions && response.commissions.length > 0) {
                            response.commissions.forEach(function(commission) {
                                const row = commissionsTable.insertRow();

                                row.insertCell(0).textContent = commission.service_name || 'Unknown Service';
                                row.insertCell(1).textContent = commission.invoice_number || 'N/A';
                                row.insertCell(2).textContent = commission.invoice_date ? formatDate(commission.invoice_date) : 'N/A';
                                row.insertCell(3).textContent = formatCurrency(commission.service_amount || 0);
                                row.insertCell(4).textContent = (commission.commission_rate || 0) + '%';
                                row.insertCell(5).textContent = formatCurrency(commission.commission_amount || 0);
                            });

                            // Show commissions container
                            document.getElementById('commissionsContainer').style.display = 'block';

                            // Update total commission
                            document.getElementById('totalCommission').textContent = formatCurrency(response.total_commission || 0);

                            // Update commission amount input
                            document.getElementById('commission_amount').value = response.total_commission || 0;

                            // Store commission data for submission
                            document.getElementById('commission_data').value = JSON.stringify(response.commissions);

                            // Calculate net salary
                            calculateNetSalary();
                        } else {
                            alert('No commissions found for the selected period');
                            document.getElementById('commissionsContainer').style.display = 'none';
                            document.getElementById('commission_amount').value = 0;
                            calculateNetSalary();
                        }
                    } else {
                        console.error('Error response:', response); // Debug: Log the error response
                        alert(response.message || 'Failed to calculate commissions');
                    }
                } catch (e) {
                    console.error('Error parsing JSON response:', e, xhr.responseText); // Debug: Log the parsing error and raw response
                    alert('Failed to calculate commissions: ' + e.message);
                }
            } else {
                console.error('HTTP error:', xhr.status, xhr.statusText); // Debug: Log HTTP error details
                alert('Failed to calculate commissions. HTTP status: ' + xhr.status);
            }
        };

        xhr.onerror = function() {
            alert('Failed to calculate commissions');
            document.getElementById('calculateCommissions').innerHTML = '<i class="fas fa-calculator me-1"></i> Calculate Commissions';
            document.getElementById('calculateCommissions').disabled = false;
        };

        xhr.send('staff_id=' + encodeURIComponent(staffId) + '&start_date=' + encodeURIComponent(startDate) + '&end_date=' + encodeURIComponent(endDate));
    }

    // Helper function to format currency
    function formatCurrency(amount) {
        return '<?= html_entity_decode(CURRENCY_SYMBOL, ENT_HTML5, "UTF-8") ?>' + parseFloat(amount).toFixed(2);
    }

    // Helper function to format date
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }
});
</script>
