<?php
/**
 * Profile Controller
 */
class ProfileController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            $this->redirect(base_url('login'));
        }
    }

    /**
     * Display user profile
     */
    public function index() {
        // Get current user
        $user_id = $_SESSION['user_id'];
        $user_model = new UserModel();
        $user = $user_model->find($user_id);

        if (!$user) {
            flash('error', 'User not found');
            $this->redirect(base_url('dashboard'));
        }

        // Get additional profile data based on user role
        $profile_data = [];

        if ($user['role'] == 'staff') {
            $staff_model = new StaffModel();
            $profile_data = $staff_model->findByUserId($user_id);
        } else if ($user['role'] == 'customer') {
            $customer_model = new CustomerModel();
            $profile_data = $customer_model->findByUserId($user_id);
        }

        // Render view
        $this->render('profile/index', [
            'user' => $user,
            'profile_data' => $profile_data
        ]);
    }

    /**
     * Update user profile
     */
    public function update() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('profile'));
        }

        // Get current user
        $user_id = $_SESSION['user_id'];
        $user_model = new UserModel();
        $user = $user_model->find($user_id);

        if (!$user) {
            flash('error', 'User not found');
            $this->redirect(base_url('profile'));
        }

        // Get form data
        $name = input('name');
        $email = input('email');
        $current_password = input('current_password');
        $new_password = input('new_password');
        $confirm_password = input('confirm_password');

        // Validate data
        if (empty($name) || empty($email)) {
            flash('error', 'Name and email are required');
            $this->redirect(base_url('profile'));
        }

        // Check if email already exists (excluding current user)
        if ($email != $user['email'] && $user_model->emailExists($email)) {
            flash('error', 'Email already exists');
            $this->redirect(base_url('profile'));
        }

        // Update user data
        $user_data = [
            'name' => $name,
            'email' => $email
        ];

        // Update password if provided
        if (!empty($new_password)) {
            // Verify current password
            if (empty($current_password) || !password_verify($current_password, $user['password'])) {
                flash('error', 'Current password is incorrect');
                $this->redirect(base_url('profile'));
            }

            // Validate new password
            if ($new_password != $confirm_password) {
                flash('error', 'New passwords do not match');
                $this->redirect(base_url('profile'));
            }

            $user_data['password'] = password_hash($new_password, PASSWORD_DEFAULT);
        }

        // Update user
        $result = $user_model->update($user_id, $user_data);

        if (!$result) {
            flash('error', 'Failed to update profile');
            $this->redirect(base_url('profile'));
        }

        // Update session with new user data
        $_SESSION['user_name'] = $name;
        $_SESSION['user_email'] = $email;

        // Update additional profile data based on user role
        if ($user['role'] == 'staff') {
            $phone = input('phone');
            $address = input('address');

            $staff_model = new StaffModel();
            $staff = $staff_model->findByUserId($user_id);

            if ($staff) {
                $staff_data = [
                    'phone' => $phone,
                    'address' => $address
                ];

                $staff_model->update($staff['id'], $staff_data);
            }
        } else if ($user['role'] == 'customer') {
            $phone = input('phone');
            $address = input('address');
            $date_of_birth = input('date_of_birth');
            $gender = input('gender');

            $customer_model = new CustomerModel();
            $customer = $customer_model->findByUserId($user_id);

            if ($customer) {
                $customer_data = [
                    'phone' => $phone,
                    'address' => $address,
                    'date_of_birth' => $date_of_birth,
                    'gender' => $gender
                ];

                $customer_model->update($customer['id'], $customer_data);
            }
        }

        flash('success', 'Profile updated successfully');
        $this->redirect(base_url('profile'));
    }

    /**
     * Upload profile picture
     */
    public function uploadPicture() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('profile'));
        }

        // Get current user
        $user_id = $_SESSION['user_id'];
        $user_model = new UserModel();
        $user = $user_model->find($user_id);

        if (!$user) {
            flash('error', 'User not found');
            $this->redirect(base_url('profile'));
        }

        // Check if file is uploaded
        if (!isset($_FILES['profile_picture']) || $_FILES['profile_picture']['error'] != 0) {
            flash('error', 'No file uploaded or upload error');
            $this->redirect(base_url('profile'));
        }

        // Validate file
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2MB

        if (!in_array($_FILES['profile_picture']['type'], $allowed_types)) {
            flash('error', 'Invalid file type. Only JPEG, PNG, and GIF are allowed');
            $this->redirect(base_url('profile'));
        }

        if ($_FILES['profile_picture']['size'] > $max_size) {
            flash('error', 'File size exceeds the limit of 2MB');
            $this->redirect(base_url('profile'));
        }

        // Create uploads directory if it doesn't exist
        $upload_dir = 'uploads/profile';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // Generate unique filename
        $filename = 'profile_' . $user_id . '_' . time() . '.' . pathinfo($_FILES['profile_picture']['name'], PATHINFO_EXTENSION);
        $upload_path = $upload_dir . '/' . $filename;

        // Move uploaded file
        if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $upload_path)) {
            // Update user profile picture
            $user_data = [
                'profile_picture' => $filename
            ];

            $result = $user_model->update($user_id, $user_data);

            if (!$result) {
                flash('error', 'Failed to update profile picture');
                $this->redirect(base_url('profile'));
            }

            // Update session with new profile picture
            $_SESSION['profile_picture'] = $filename;

            flash('success', 'Profile picture updated successfully');
        } else {
            flash('error', 'Failed to upload profile picture');
        }

        $this->redirect(base_url('profile'));
    }
}
