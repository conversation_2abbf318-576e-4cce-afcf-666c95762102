<?php
/**
 * Payroll Controller
 */
class PayrollController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            $this->redirect(base_url('login'));
        }

        // Check if user has permission
        if (!has_role(['admin', 'manager'])) {
            flash('error', 'You do not have permission to access payroll management');
            $this->redirect(base_url('dashboard'));
        }
    }

    /**
     * Display all payroll records
     */
    public function index() {
        // Get all payroll records
        $payroll_model = new PayrollModel();
        $payroll = $payroll_model->all();

        // Render view
        $this->render('payroll/index', [
            'payroll' => $payroll
        ]);
    }

    /**
     * Display create payroll form
     */
    public function create() {
        // Get all staff members
        $staff_model = new StaffModel();
        $staff = $staff_model->getAllWithUsers();

        // Render view
        $this->render('payroll/create', [
            'staff' => $staff
        ]);
    }

    /**
     * Calculate commissions for a staff member
     */
    public function calculateCommissions() {
        // Check if request is AJAX
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            $this->redirect(base_url('payroll'));
        }

        // Get parameters
        $staff_id = input('staff_id');
        $start_date = input('start_date');
        $end_date = input('end_date');

        // Validate data
        if (empty($staff_id) || empty($start_date) || empty($end_date)) {
            echo json_encode(['success' => false, 'message' => 'Please provide all required parameters']);
            exit;
        }

        try {
            // Calculate commissions
            $payroll_model = new PayrollModel();
            $commission_data = $payroll_model->calculateCommissions($staff_id, $start_date, $end_date);

            // Get staff details
            $staff_model = new StaffModel();
            $staff = $staff_model->getWithUser($staff_id);

            if (!$staff) {
                echo json_encode(['success' => false, 'message' => 'Staff member not found']);
                exit;
            }

            echo json_encode([
                'success' => true,
                'commissions' => $commission_data['commissions'],
                'total_commission' => $commission_data['total_commission'],
                'staff' => $staff
            ]);
        } catch (Exception $e) {
            // Log the error
            error_log('Error calculating commissions: ' . $e->getMessage());

            // Return error response
            echo json_encode([
                'success' => false,
                'message' => 'Error calculating commissions: ' . $e->getMessage()
            ]);
        }
        exit;
    }

    /**
     * Store new payroll record
     */
    public function store() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('payroll'));
        }

        // Get form data
        $staff_id = input('staff_id');
        $pay_period_start = input('pay_period_start');
        $pay_period_end = input('pay_period_end');
        $basic_salary = input('basic_salary');
        $commission_amount = input('commission_amount');
        $bonus = input('bonus');
        $deductions = input('deductions');
        $net_salary = input('net_salary');
        $payment_date = input('payment_date');
        $payment_method = input('payment_method');
        $status = input('status');
        $notes = input('notes');
        $commission_data = input('commission_data');

        // Validate data
        if (empty($staff_id) || empty($pay_period_start) || empty($pay_period_end) || empty($payment_date)) {
            flash('error', 'Please fill in all required fields');
            $this->redirect(base_url('payroll/create'));
        }

        // Create payroll record
        $payroll_model = new PayrollModel();
        $payroll_data = [
            'staff_id' => $staff_id,
            'pay_period_start' => $pay_period_start,
            'pay_period_end' => $pay_period_end,
            'basic_salary' => $basic_salary,
            'commission_amount' => $commission_amount,
            'bonus' => $bonus,
            'deductions' => $deductions,
            'net_salary' => $net_salary,
            'payment_date' => $payment_date,
            'payment_method' => $payment_method,
            'status' => $status,
            'notes' => $notes
        ];

        $payroll_id = $payroll_model->create($payroll_data);

        if (!$payroll_id) {
            flash('error', 'Failed to create payroll record');
            $this->redirect(base_url('payroll/create'));
        }

        // Add commissions if available
        if (!empty($commission_data)) {
            $commission_items = json_decode($commission_data, true);

            if (is_array($commission_items)) {
                foreach ($commission_items as $item) {
                    $commission_item = [
                        'payroll_id' => $payroll_id,
                        'invoice_service_id' => $item['invoice_service_id'],
                        'service_amount' => $item['service_amount'],
                        'commission_rate' => $item['commission_rate'],
                        'commission_amount' => $item['commission_amount']
                    ];

                    $payroll_model->addCommission($commission_item);
                }
            }
        }

        flash('success', 'Payroll record created successfully');
        $this->redirect(base_url('payroll'));
    }

    /**
     * Display edit payroll form
     */
    public function edit($id) {
        // Get payroll record
        $payroll_model = new PayrollModel();
        $payroll = $payroll_model->find($id);

        if (!$payroll) {
            flash('error', 'Payroll record not found');
            $this->redirect(base_url('payroll'));
        }

        // Get all staff members
        $staff_model = new StaffModel();
        $staff = $staff_model->getAllWithUsers();

        // Get commissions
        $commissions = $payroll_model->getCommissions($id);

        // Render view
        $this->render('payroll/edit', [
            'payroll' => $payroll,
            'staff' => $staff,
            'commissions' => $commissions
        ]);
    }

    /**
     * Update payroll record
     */
    public function update($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('payroll'));
        }

        // Get payroll record
        $payroll_model = new PayrollModel();
        $payroll = $payroll_model->find($id);

        if (!$payroll) {
            flash('error', 'Payroll record not found');
            $this->redirect(base_url('payroll'));
        }

        // Get form data
        $staff_id = input('staff_id');
        $pay_period_start = input('pay_period_start');
        $pay_period_end = input('pay_period_end');
        $basic_salary = input('basic_salary');
        $commission_amount = input('commission_amount');
        $bonus = input('bonus');
        $deductions = input('deductions');
        $net_salary = input('net_salary');
        $payment_date = input('payment_date');
        $payment_method = input('payment_method');
        $status = input('status');
        $notes = input('notes');

        // Validate data
        if (empty($staff_id) || empty($pay_period_start) || empty($pay_period_end) || empty($payment_date)) {
            flash('error', 'Please fill in all required fields');
            $this->redirect(base_url('payroll/edit/' . $id));
        }

        // Update payroll record
        $payroll_data = [
            'staff_id' => $staff_id,
            'pay_period_start' => $pay_period_start,
            'pay_period_end' => $pay_period_end,
            'basic_salary' => $basic_salary,
            'commission_amount' => $commission_amount,
            'bonus' => $bonus,
            'deductions' => $deductions,
            'net_salary' => $net_salary,
            'payment_date' => $payment_date,
            'payment_method' => $payment_method,
            'status' => $status,
            'notes' => $notes
        ];

        $result = $payroll_model->update($id, $payroll_data);

        if (!$result) {
            flash('error', 'Failed to update payroll record');
            $this->redirect(base_url('payroll/edit/' . $id));
        }

        flash('success', 'Payroll record updated successfully');
        $this->redirect(base_url('payroll'));
    }

    /**
     * Delete payroll record
     */
    public function delete($id) {
        // Get payroll record
        $payroll_model = new PayrollModel();
        $payroll = $payroll_model->find($id);

        if (!$payroll) {
            flash('error', 'Payroll record not found');
            $this->redirect(base_url('payroll'));
        }

        // Delete payroll record
        $result = $payroll_model->delete($id);

        if (!$result) {
            flash('error', 'Failed to delete payroll record');
            $this->redirect(base_url('payroll'));
        }

        flash('success', 'Payroll record deleted successfully');
        $this->redirect(base_url('payroll'));
    }

    /**
     * View payroll record
     */
    public function view($id) {
        // Get payroll record
        $payroll_model = new PayrollModel();
        $payroll = $payroll_model->find($id);

        if (!$payroll) {
            flash('error', 'Payroll record not found');
            $this->redirect(base_url('payroll'));
        }

        // Get commissions
        $commissions = $payroll_model->getCommissions($id);

        // Render view
        $this->render('payroll/view', [
            'payroll' => $payroll,
            'commissions' => $commissions
        ]);
    }

    /**
     * Print payroll record
     */
    public function print($id) {
        // Get payroll record
        $payroll_model = new PayrollModel();
        $payroll = $payroll_model->find($id);

        if (!$payroll) {
            flash('error', 'Payroll record not found');
            $this->redirect(base_url('payroll'));
        }

        // Get commissions
        $commissions = $payroll_model->getCommissions($id);

        // Get settings
        $settings_model = new SettingsModel();
        $settings = $settings_model->get();

        // Render view without the main layout
        $this->renderStandalone('payroll/print', [
            'payroll' => $payroll,
            'commissions' => $commissions,
            'settings' => $settings
        ]);
    }
}
