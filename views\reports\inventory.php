<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-box"></i> Inventory Report</h1>
        <a href="<?= base_url('reports/export?type=inventory') ?>" class="btn btn-outline-primary">
            <i class="fas fa-download me-1"></i> Export CSV
        </a>
    </div>

    <div class="row">
        <!-- Reports Navigation -->
        <div class="col-md-3 mb-4">
            <?php include 'partials/reports_nav.php'; ?>
        </div>
        
        <!-- Reports Content -->
        <div class="col-md-9">
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Products</h6>
                                    <h4 class="mb-0"><?= $inventory_report['totals']['product_count'] ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-box text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Stock</h6>
                                    <h4 class="mb-0"><?= $inventory_report['totals']['total_stock'] ?> units</h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-cubes text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Inventory Value</h6>
                                    <h4 class="mb-0"><?= format_currency($inventory_report['totals']['inventory_value']) ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-dollar-sign text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Low Stock Alert -->
            <?php if ($inventory_report['totals']['low_stock_count'] > 0): ?>
                <div class="card shadow-sm mb-4 border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-1"></i> Low Stock Alert</h5>
                    </div>
                    <div class="card-body">
                        <p>There are <strong><?= $inventory_report['totals']['low_stock_count'] ?></strong> products that are at or below their minimum stock level.</p>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Category</th>
                                        <th class="text-end">Current Stock</th>
                                        <th class="text-end">Min Stock</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $low_stock_items = array_filter($inventory_report['data'], function($item) {
                                        return $item['current_stock'] <= $item['min_stock'];
                                    });
                                    
                                    foreach ($low_stock_items as $item): 
                                    ?>
                                        <tr>
                                            <td><?= $item['product_name'] ?></td>
                                            <td><?= $item['category_name'] ?></td>
                                            <td class="text-end <?= $item['current_stock'] == 0 ? 'text-danger fw-bold' : 'text-warning' ?>">
                                                <?= $item['current_stock'] ?>
                                            </td>
                                            <td class="text-end"><?= $item['min_stock'] ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Inventory Data Table -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Inventory Data</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($inventory_report['data'])): ?>
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No inventory data available.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Category</th>
                                        <th class="text-end">Stock</th>
                                        <th class="text-end">Cost Price</th>
                                        <th class="text-end">Selling Price</th>
                                        <th class="text-end">Profit Margin</th>
                                        <th class="text-end">Inventory Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($inventory_report['data'] as $row): ?>
                                        <tr>
                                            <td><?= $row['product_name'] ?></td>
                                            <td><?= $row['category_name'] ?></td>
                                            <td class="text-end <?= $row['current_stock'] <= $row['min_stock'] ? ($row['current_stock'] == 0 ? 'text-danger fw-bold' : 'text-warning') : '' ?>">
                                                <?= $row['current_stock'] ?>
                                            </td>
                                            <td class="text-end"><?= format_currency($row['cost_price']) ?></td>
                                            <td class="text-end"><?= format_currency($row['selling_price']) ?></td>
                                            <td class="text-end"><?= number_format($row['profit_percentage'], 1) ?>%</td>
                                            <td class="text-end"><?= format_currency($row['inventory_value']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="2">Total</th>
                                        <th class="text-end"><?= $inventory_report['totals']['total_stock'] ?></th>
                                        <th colspan="3"></th>
                                        <th class="text-end"><?= format_currency($inventory_report['totals']['inventory_value']) ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
