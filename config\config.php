<?php
/**
 * Main configuration file
 */

// Note: Error reporting is now handled in environment.php

// Database configuration - Environment-specific settings
if (ENVIRONMENT === 'development') {
    // Local database configuration
    define('DB_HOST', 'localhost');
    define('DB_NAME', 'salon_db2');
    define('DB_USER', 'root');
    define('DB_PASS', '');
} else {
    // Live server database configuration
    // Update these values with your live server database details
    define('DB_HOST', 'localhost');
    define('DB_NAME', 'u721900540_salon'); // Change this to your live database name
    define('DB_USER', 'u721900540_salon'); // Change this to your live database username
    define('DB_PASS', 'Salon@54321'); // Change this to your live database password
}

// Application configuration
define('APP_NAME', 'Salon Management System');
// Note: APP_URL is now handled by BASE_URL constant in environment.php

// Business configuration
// Use HTML entity for Rupee symbol to avoid encoding issues
if (!defined('CURRENCY_SYMBOL')) define('CURRENCY_SYMBOL', '₹');
if (!defined('COMPANY_NAME')) define('COMPANY_NAME', 'Salon Management System');
if (!defined('COMPANY_ADDRESS')) define('COMPANY_ADDRESS', '123 Main Street, City, State, ZIP');
if (!defined('COMPANY_PHONE')) define('COMPANY_PHONE', '+****************');
if (!defined('COMPANY_EMAIL')) define('COMPANY_EMAIL', '<EMAIL>');

// Autoload classes
spl_autoload_register(function ($class_name) {
    // Convert namespace to file path
    $class_file = str_replace('\\', '/', $class_name) . '.php';

    // Check in core directory
    if (file_exists(BASE_PATH . '/core/' . $class_file)) {
        require_once BASE_PATH . '/core/' . $class_file;
        return;
    }

    // Check in models directory
    if (file_exists(BASE_PATH . '/models/' . $class_file)) {
        require_once BASE_PATH . '/models/' . $class_file;
        return;
    }

    // Check in controllers directory
    if (file_exists(BASE_PATH . '/controllers/' . $class_file)) {
        require_once BASE_PATH . '/controllers/' . $class_file;
        return;
    }

    // Check in config directory
    if (file_exists(BASE_PATH . '/config/' . $class_file)) {
        require_once BASE_PATH . '/config/' . $class_file;
        return;
    }
});

// Load helper functions
require_once BASE_PATH . '/helpers/functions.php';


