<?php
/**
 * Staff Model
 */
class StaffModel extends Model {
    protected $table = 'staff';

    /**
     * Get staff with user data
     *
     * @param int $id Staff ID
     * @return array|false Staff data or false if not found
     */
    public function getWithUser($id) {
        $query = "SELECT s.*, u.name, u.email, u.status, u.role
                 FROM " . $this->table . " s
                 JOIN users u ON s.user_id = u.id
                 WHERE s.id = :id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get all staff with user data
     *
     * @return array Staff
     */
    public function getAllWithUsers() {
        $query = "SELECT s.*, u.name, u.email, u.status, u.role
                 FROM " . $this->table . " s
                 JOIN users u ON s.user_id = u.id
                 ORDER BY u.name";
        $stmt = $this->db->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get staff by service ID
     *
     * @param int $service_id Service ID
     * @return array Staff
     */
    public function getByServiceId($service_id) {
        $query = "SELECT s.*, u.name, u.email, u.status, u.role
                 FROM " . $this->table . " s
                 JOIN users u ON s.user_id = u.id
                 JOIN staff_services ss ON s.id = ss.staff_id
                 WHERE ss.service_id = :service_id AND u.status = 'active'
                 ORDER BY u.name";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':service_id', $service_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get services assigned to a staff member
     *
     * @param int $staff_id Staff ID
     * @return array Services
     */
    public function getServices($staff_id) {
        $query = "SELECT ss.*, s.name as service_name, s.price, s.duration, sc.name as category_name
                 FROM staff_services ss
                 JOIN services s ON ss.service_id = s.id
                 LEFT JOIN service_categories sc ON s.category_id = sc.id
                 WHERE ss.staff_id = :staff_id
                 ORDER BY s.name";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':staff_id', $staff_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Add a service to a staff member
     *
     * @param int $staff_id Staff ID
     * @param int $service_id Service ID
     * @return bool Success
     */
    public function addService($staff_id, $service_id) {
        // Check if the service is already assigned to the staff
        $query = "SELECT COUNT(*) FROM staff_services
                 WHERE staff_id = :staff_id AND service_id = :service_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':staff_id', $staff_id);
        $stmt->bindParam(':service_id', $service_id);
        $stmt->execute();

        if ($stmt->fetchColumn() > 0) {
            return true; // Service already assigned
        }

        // Add the service
        $query = "INSERT INTO staff_services (staff_id, service_id)
                 VALUES (:staff_id, :service_id)";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':staff_id', $staff_id);
        $stmt->bindParam(':service_id', $service_id);

        return $stmt->execute();
    }

    /**
     * Remove a service from a staff member
     *
     * @param int $staff_id Staff ID
     * @param int $service_id Service ID
     * @return bool Success
     */
    public function removeService($staff_id, $service_id) {
        $query = "DELETE FROM staff_services
                 WHERE staff_id = :staff_id AND service_id = :service_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':staff_id', $staff_id);
        $stmt->bindParam(':service_id', $service_id);

        return $stmt->execute();
    }

    /**
     * Find staff by user ID
     *
     * @param int $user_id User ID
     * @return array|false Staff data or false if not found
     */
    public function findByUserId($user_id) {
        $query = "SELECT * FROM " . $this->table . " WHERE user_id = :user_id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
