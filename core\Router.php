<?php
/**
 * Router class for handling URL routing
 */
class Router {
    private $routes = [];

    /**
     * Register a route
     *
     * @param string $method HTTP method (GET, POST, etc.)
     * @param string $url URL pattern
     * @param string $controller Controller name
     * @param string $action Action/method name
     */
    public function register($method, $url, $controller, $action) {
        $this->routes[] = [
            'method' => $method,
            'url' => $url,
            'controller' => $controller,
            'action' => $action
        ];
    }

    /**
     * Register a GET route
     */
    public function get($url, $controller, $action) {
        $this->register('GET', $url, $controller, $action);
    }

    /**
     * Register a POST route
     */
    public function post($url, $controller, $action) {
        $this->register('POST', $url, $controller, $action);
    }

    /**
     * Dispatch the request to the appropriate controller
     */
    public function dispatch() {
        // Get the request method and URL
        $method = $_SERVER['REQUEST_METHOD'];
        $url = $_SERVER['REQUEST_URI'];

        // Remove query string
        if (($pos = strpos($url, '?')) !== false) {
            $url = substr($url, 0, $pos);
        }

        // Simplify base path removal
        $base_path = '/projects/SALON5';

        // If URL starts with base path, remove it
        if (strpos($url, $base_path) === 0) {
            $url = substr($url, strlen($base_path));
        }

        // Ensure URL starts with a slash
        if (substr($url, 0, 1) !== '/') {
            $url = '/' . $url;
        }

        // Default route
        if ($url == '/') {
            // Redirect to dashboard if logged in with admin/manager/staff role
            if (is_logged_in()) {
                if (has_role(['admin', 'manager', 'staff'])) {
                    header('Location: ' . base_url('dashboard'));
                    exit;
                } else if (has_role(['customer'])) {
                    header('Location: ' . base_url('customer'));
                    exit;
                }
            }
            // Otherwise keep the home page
        }

        // Find matching route
        foreach ($this->routes as $route) {
            // Convert route URL to regex pattern
            $pattern = $this->convertRouteToRegex($route['url']);

            // Check if URL matches pattern and method matches
            if (preg_match($pattern, $url, $matches) && $route['method'] == $method) {
                // Remove the first match (full URL)
                array_shift($matches);

                // Create controller instance
                $controller_name = $route['controller'];
                $controller = new $controller_name();

                // Call the action with parameters
                call_user_func_array([$controller, $route['action']], $matches);
                return;
            }
        }

        // No route found - 404
        header("HTTP/1.0 404 Not Found");
        include BASE_PATH . '/views/errors/404.php';
    }

    /**
     * Convert route URL to regex pattern
     *
     * @param string $route Route URL with placeholders
     * @return string Regex pattern
     */
    private function convertRouteToRegex($route) {
        // Replace placeholders with regex patterns
        $route = preg_replace('/\{([a-zA-Z0-9_]+)\}/', '([^/]+)', $route);

        // Add start and end markers
        $route = '/^' . str_replace('/', '\/', $route) . '$/';

        return $route;
    }
}

