<?php
/**
 * Settings Controller
 *
 * Handles application settings
 */
class SettingsController extends Controller {
    private $settings_model;

    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            $this->redirect(base_url('login'));
        }

        // Check if user has admin privileges
        if (!has_role('admin')) {
            flash('error', 'You do not have permission to access settings');
            $this->redirect(base_url('dashboard'));
        }

        // Load models
        $this->settings_model = new SettingsModel();
    }

    /**
     * Display general settings
     */
    public function index() {
        // Get settings
        $settings = $this->settings_model->get();

        // Render view
        $this->render('settings/general', [
            'settings' => $settings,
            'active_tab' => 'general'
        ]);
    }



    /**
     * Save a single setting via AJAX
     */
    public function save_setting() {
        // Check if user has admin privileges
        if (!has_role('admin')) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Permission denied']);
            exit;
        }

        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            exit;
        }

        // Get form data
        $name = input('name');
        $value = input('value');

        if (empty($name)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Setting name is required']);
            exit;
        }



        // Update setting
        $result = $this->settings_model->setValue($name, $value);

        header('Content-Type: application/json');
        echo json_encode(['success' => $result, 'message' => $result ? 'Setting saved' : 'Failed to save setting']);
        exit;
    }

    /**
     * Save general settings
     */
    public function save_general() {
        // Check if user has admin privileges
        if (!has_role('admin')) {
            flash('error', 'You do not have permission to access settings');
            $this->redirect(base_url('dashboard'));
        }

        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('settings'));
        }

        // Get form data
        $salon_name = input('salon_name');
        $salon_address = input('salon_address');
        $salon_phone = input('salon_phone');
        $salon_email = input('salon_email');
        $salon_website = input('salon_website');
        $tax_rate = input('tax_rate');
        $currency = input('currency');
        $currency_symbol = input('currency_symbol');
        $appointment_interval = input('appointment_interval');
        $business_hours_start = input('business_hours_start');
        $business_hours_end = input('business_hours_end');
        $weekend_days = isset($_POST['weekend_days']) ? implode(',', $_POST['weekend_days']) : '';
        $invoice_prefix = input('invoice_prefix');
        $invoice_footer = input('invoice_footer');
        $theme_color = input('theme_color');

        // Validate data
        if (empty($salon_name)) {
            flash('error', 'Salon name is required');
            $this->redirect(base_url('settings'));
        }

        if (empty($tax_rate) || !is_numeric($tax_rate)) {
            $tax_rate = 0;
        }

        if (empty($appointment_interval) || !is_numeric($appointment_interval)) {
            $appointment_interval = 30;
        }

        // Update settings
        $settings = [
            'salon_name' => $salon_name,
            'salon_address' => $salon_address,
            'salon_phone' => $salon_phone,
            'salon_email' => $salon_email,
            'salon_website' => $salon_website,
            'tax_rate' => $tax_rate,
            'currency' => $currency,
            'currency_symbol' => $currency_symbol,
            'appointment_interval' => $appointment_interval,
            'business_hours_start' => $business_hours_start,
            'business_hours_end' => $business_hours_end,
            'weekend_days' => $weekend_days,
            'invoice_prefix' => $invoice_prefix,
            'invoice_footer' => $invoice_footer,
            'theme_color' => $theme_color
        ];

        foreach ($settings as $key => $value) {
            $this->settings_model->setValue($key, $value);
        }

        // Handle logo upload
        if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
            $this->handleLogoUpload('logo');
        }

        // Handle favicon upload
        if (isset($_FILES['favicon']) && $_FILES['favicon']['error'] === UPLOAD_ERR_OK) {
            $this->handleLogoUpload('favicon');
        }

        flash('success', 'Settings saved successfully');
        $this->redirect(base_url('settings'));
    }

    /**
     * Handle logo upload
     *
     * @param string $field Form field name
     * @return void
     */
    private function handleLogoUpload($field) {
        $upload_dir = BASE_PATH . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR;
        $file_name = $_FILES[$field]['name'];
        $file_tmp = $_FILES[$field]['tmp_name'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        // Check if directory exists, if not create it
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        // Generate unique file name
        $new_file_name = $field . '_' . time() . '.' . $file_ext;
        $file_path = $upload_dir . $new_file_name;

        // Move uploaded file
        if (move_uploaded_file($file_tmp, $file_path)) {
            // Update setting
            $this->settings_model->setValue($field, 'uploads/' . $new_file_name);
        } else {
            flash('error', 'Failed to upload ' . $field);
        }
    }

    /**
     * Display business settings
     */
    public function business() {
        // Get settings
        $settings = $this->settings_model->get();

        // Render view
        $this->render('settings/business', [
            'settings' => $settings,
            'active_tab' => 'business'
        ]);
    }

    /**
     * Save business settings
     */
    public function updateBusiness() {
        // Check if user has admin privileges
        if (!has_role('admin')) {
            flash('error', 'You do not have permission to access settings');
            $this->redirect(base_url('dashboard'));
        }

        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('settings/business'));
        }

        // Get form data
        $appointment_interval = input('appointment_interval');
        $business_hours_start = input('business_hours_start');
        $business_hours_end = input('business_hours_end');
        $weekend_days = isset($_POST['weekend_days']) ? implode(',', $_POST['weekend_days']) : '';
        $invoice_prefix = input('invoice_prefix');
        $invoice_footer = input('invoice_footer');

        // Validate data
        if (empty($appointment_interval) || !is_numeric($appointment_interval)) {
            $appointment_interval = 30;
        }

        // Update settings
        $settings = [
            'appointment_interval' => $appointment_interval,
            'business_hours_start' => $business_hours_start,
            'business_hours_end' => $business_hours_end,
            'weekend_days' => $weekend_days,
            'invoice_prefix' => $invoice_prefix,
            'invoice_footer' => $invoice_footer
        ];

        foreach ($settings as $key => $value) {
            $this->settings_model->setValue($key, $value);
        }

        flash('success', 'Business settings saved successfully');
        $this->redirect(base_url('settings/business'));
    }

    /**
     * Display appearance settings
     */
    public function appearance() {
        // Get settings
        $settings = $this->settings_model->get();

        // Get hero sliders
        $slider_model = new HeroSliderModel();
        $sliders = $slider_model->getAll();

        // Render view
        $this->render('settings/appearance', [
            'settings' => $settings,
            'sliders' => $sliders,
            'active_tab' => 'appearance'
        ]);
    }

    /**
     * Save appearance settings
     */
    public function updateAppearance() {
        // Check if user has admin privileges
        if (!has_role('admin')) {
            flash('error', 'You do not have permission to access settings');
            $this->redirect(base_url('dashboard'));
        }

        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('settings/appearance'));
        }

        // Get form data
        $theme_color = input('theme_color');

        // Update settings
        $settings = [
            'theme_color' => $theme_color
        ];

        foreach ($settings as $key => $value) {
            $this->settings_model->setValue($key, $value);
        }

        // Handle logo upload
        if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
            $this->handleLogoUpload('logo');
        }

        // Handle favicon upload
        if (isset($_FILES['favicon']) && $_FILES['favicon']['error'] === UPLOAD_ERR_OK) {
            $this->handleLogoUpload('favicon');
        }

        flash('success', 'Appearance settings saved successfully');
        $this->redirect(base_url('settings/appearance'));
    }

    /**
     * Display email settings
     */
    public function email() {
        // Get settings
        $settings = $this->settings_model->get();

        // Render view
        $this->render('settings/email', [
            'settings' => $settings,
            'active_tab' => 'email'
        ]);
    }

    /**
     * Save email settings
     */
    public function updateEmail() {
        // Check if user has admin privileges
        if (!has_role('admin')) {
            flash('error', 'You do not have permission to access settings');
            $this->redirect(base_url('dashboard'));
        }

        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('settings/email'));
        }

        // Get form data
        $smtp_host = input('smtp_host');
        $smtp_port = input('smtp_port');
        $smtp_username = input('smtp_username');
        $smtp_password = input('smtp_password');
        $smtp_encryption = input('smtp_encryption');
        $email_from_name = input('email_from_name');
        $email_from_address = input('email_from_address');

        // Update settings
        $settings = [
            'smtp_host' => $smtp_host,
            'smtp_port' => $smtp_port,
            'smtp_username' => $smtp_username,
            'smtp_encryption' => $smtp_encryption,
            'email_from_name' => $email_from_name,
            'email_from_address' => $email_from_address
        ];

        // Only update password if provided
        if (!empty($smtp_password)) {
            $settings['smtp_password'] = $smtp_password;
        }

        foreach ($settings as $key => $value) {
            $this->settings_model->setValue($key, $value);
        }

        flash('success', 'Email settings saved successfully');
        $this->redirect(base_url('settings/email'));
    }
}
