<?php
/**
 * Service Category Model
 */
class ServiceCategoryModel extends Model {
    protected $table = 'service_categories';

    /**
     * Get all categories with service counts
     *
     * @param bool $only_with_services Only return categories that have services
     * @return array Categories with service counts
     */
    public function getAllWithServiceCounts($only_with_services = false) {
        $query = "SELECT c.*, COUNT(s.id) as service_count
                 FROM " . $this->table . " c
                 LEFT JOIN services s ON c.id = s.category_id AND s.status = 'active'
                 GROUP BY c.id";

        if ($only_with_services) {
            $query .= " HAVING service_count > 0";
        }

        $query .= " ORDER BY c.name";

        $stmt = $this->db->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get categories that have active services
     *
     * @return array Categories
     */
    public function getCategoriesWithServices() {
        return $this->getAllWithServiceCounts(true);
    }
}
