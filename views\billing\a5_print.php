<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @page {
            size: A5; /* A5 size */
            margin: 0;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            line-height: 1.3;
            color: #333;
            background-color: #fff;
            width: 148mm; /* A5 width */
            height: 210mm; /* A5 height */
            margin: 0 auto;
            padding: 10px;
        }
        .invoice-header {
            padding: 10px 0;
            border-bottom: 1px solid #ddd;
        }
        .invoice-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .invoice-details {
            margin-top: 10px;
            margin-bottom: 10px;
            font-size: 9px;
        }
        .invoice-details-title {
            font-weight: bold;
            margin-bottom: 3px;
        }
        .table {
            font-size: 9px;
        }
        .table th {
            background-color: #f8f9fa;
            padding: 4px;
        }
        .table td {
            padding: 4px;
        }
        .table-totals {
            margin-top: 10px;
            font-size: 9px;
        }
        .table-totals td {
            padding: 3px;
        }
        .table-totals .total-row {
            font-weight: bold;
            font-size: 11px;
            border-top: 1px solid #ddd;
        }
        .footer {
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
            font-size: 9px;
            color: #666;
            text-align: center;
        }
        .no-print {
            display: none;
        }
        .salon-info {
            font-size: 9px;
        }
        .customer-info {
            font-size: 9px;
        }
        .section-title {
            font-size: 11px;
            font-weight: bold;
            margin: 8px 0 4px 0;
        }
        .membership-info {
            font-weight: bold;
            color: #28a745;
            font-style: italic;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                margin: 0;
                padding: 5mm;
                width: 148mm;
                height: 210mm;
            }
            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="no-print mb-3 mt-3 text-center">
        <button class="btn btn-primary btn-sm" onclick="window.print()">Print Invoice</button>
        <button class="btn btn-secondary btn-sm" onclick="window.close()">Close</button>
    </div>

    <div class="invoice-header">
        <div class="row">
            <div class="col-6">
                <div class="invoice-title">INVOICE</div>
                <div>#<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?></div>
            </div>
            <div class="col-6 text-end">
                <div class="salon-info">
                    <strong><?= $settings['salon_name'] ?? 'Vishal\'s makeover' ?></strong><br>
                    <?= $settings['salon_address'] ?? 'Sadar Bazar' ?><br>
                    <?= $settings['salon_phone'] ?? '+918573061818' ?><br>
                    <?= $settings['salon_email'] ?? '<EMAIL>' ?>
                </div>
            </div>
        </div>
    </div>

    <div class="invoice-details">
        <div class="row">
            <div class="col-6">
                <div class="invoice-details-title">Bill To:</div>
                <div class="customer-info">
                    <strong><?= $invoice['customer_name'] ?></strong><br>
                    <?= $invoice['customer_phone'] ?><br>
                    <?= $invoice['customer_email'] ?>
                    <?php if (!empty($invoice['customer_address'])): ?>
                        <br><?= $invoice['customer_address'] ?>
                    <?php endif; ?>
                    
                    <?php 
                    // Check if customer has an active membership
                    $membership_model = new MembershipModel();
                    $membership = $membership_model->getActiveMembershipByCustomerId($invoice['customer_id']);
                    if ($membership): 
                    ?>
                    <br><span class="membership-info"><?= $membership['membership_name'] ?> MEMBERSHIP</span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-6 text-end">
                <div class="invoice-details-title">Invoice Details:</div>
                <div class="customer-info">
                    <strong>Invoice Date:</strong> <?= format_date($invoice['invoice_date']) ?><br>
                    <strong>Status:</strong>
                    <?php if ($invoice['payment_status'] == 'unpaid'): ?>
                        <span class="text-warning">Unpaid</span>
                    <?php elseif ($invoice['payment_status'] == 'partial'): ?>
                        <span class="text-info">Partial</span>
                    <?php elseif ($invoice['payment_status'] == 'paid'): ?>
                        <span class="text-success">Paid</span>
                    <?php elseif ($invoice['payment_status'] == 'cancelled'): ?>
                        <span class="text-danger">Cancelled</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php if (!empty($invoice['services'])): ?>
        <div class="section-title">Services</div>
        <div class="table-responsive mb-2">
            <table class="table table-sm table-bordered">
                <thead>
                    <tr>
                        <th>Service</th>
                        <th>Category</th>
                        <th>Staff</th>
                        <th class="text-end">Price</th>
                        <th class="text-end">Qty</th>
                        <th class="text-end">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($invoice['services'] as $service): ?>
                        <tr>
                            <td>
                                <div class="fw-bold"><?= $service['service_name'] ?></div>
                            </td>
                            <td><?= $service['service_category_name'] ?></td>
                            <td><?= $service['staff_name'] ?? 'N/A' ?></td>
                            <td class="text-end"><?= format_currency(isset($service['price']) ? $service['price'] : ($service['unit_price'] ?? 0)) ?></td>
                            <td class="text-end"><?= $service['quantity'] ?></td>
                            <td class="text-end"><?= format_currency(isset($service['price']) ? $service['price'] * $service['quantity'] : ($service['unit_price'] ?? 0) * $service['quantity']) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>

    <?php if (!empty($invoice['products'])): ?>
        <div class="section-title">Products</div>
        <div class="table-responsive mb-2">
            <table class="table table-sm table-bordered">
                <thead>
                    <tr>
                        <th>Product</th>
                        <th>Category</th>
                        <th class="text-end">Price</th>
                        <th class="text-end">Qty</th>
                        <th class="text-end">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($invoice['products'] as $product): ?>
                        <tr>
                            <td>
                                <div class="fw-bold"><?= $product['product_name'] ?></div>
                            </td>
                            <td><?= $product['product_category_name'] ?></td>
                            <td class="text-end"><?= format_currency(isset($product['price']) ? $product['price'] : ($product['unit_price'] ?? 0)) ?></td>
                            <td class="text-end"><?= $product['quantity'] ?></td>
                            <td class="text-end"><?= format_currency(isset($product['price']) ? $product['price'] * $product['quantity'] : ($product['unit_price'] ?? 0) * $product['quantity']) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-7">
            <?php if (!empty($invoice['notes'])): ?>
                <div class="mb-2">
                    <div class="section-title">Notes:</div>
                    <div class="p-2 bg-light" style="font-size: 9px;">
                        <?= nl2br($invoice['notes']) ?>
                    </div>
                </div>
            <?php endif; ?>

            <div class="mb-2">
                <div class="section-title">Payment Information:</div>
                <div style="font-size: 9px;">
                    <strong>Payment Method:</strong> <?= ucfirst(str_replace('_', ' ', $invoice['payment_method'])) ?><br>
                    <?php if ($invoice['payment_amount'] > 0): ?>
                        <strong>Amount Paid:</strong> <?= format_currency($invoice['payment_amount']) ?><br>
                        <?php if (isset($invoice['wallet_amount']) && $invoice['wallet_amount'] > 0): ?>
                            <strong>Wallet Payment:</strong> <?= format_currency($invoice['wallet_amount']) ?><br>
                            <?php if ($invoice['payment_method'] == 'mixed'): ?>
                                <strong>Other Payment:</strong> <?= format_currency($invoice['payment_amount'] - $invoice['wallet_amount']) ?><br>
                            <?php endif; ?>
                            <?php
                            // Get customer's remaining wallet balance
                            $wallet_model = new WalletModel();
                            $current_wallet = $wallet_model->getByCustomerId($invoice['customer_id']);
                            $remaining_balance = $current_wallet ? $current_wallet['balance'] : 0;
                            ?>
                            <strong>Remaining Wallet Balance:</strong> <?= format_currency($remaining_balance) ?><br>
                        <?php endif; ?>
                        <strong>Payment Date:</strong> <?= format_date($invoice['invoice_date']) ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-5">
            <table class="table-totals ms-auto">
                <tr>
                    <td class="text-end"><strong>Subtotal:</strong></td>
                    <td class="text-end" style="min-width: 80px;"><?= format_currency($invoice['subtotal']) ?></td>
                </tr>
                <?php if ($invoice['discount_amount'] > 0): ?>
                    <?php
                        // Calculate discount percentage
                        $discount_percentage = ($invoice['discount_amount'] / $invoice['subtotal']) * 100;
                    ?>
                    <tr>
                        <td class="text-end"><strong>Discount (<?= number_format($discount_percentage, 2) ?>%):</strong></td>
                        <td class="text-end text-danger">-<?= format_currency($invoice['discount_amount']) ?></td>
                    </tr>
                    <?php if (isset($invoice['coupon'])): ?>
                    <tr>
                        <td class="text-end"><small class="text-muted">Coupon Applied:</small></td>
                        <td class="text-end"><small class="text-muted"><?= $invoice['coupon']['coupon_code'] ?>
                            (<?= $invoice['coupon']['discount_type'] === 'percentage' ? $invoice['coupon']['discount_value'] . '%' : format_currency($invoice['coupon']['discount_value']) ?>)</small></td>
                    </tr>
                    <?php elseif (isset($invoice['customer_id']) && $membership): ?>
                    <tr>
                        <td class="text-end"><small class="text-success"><?= $membership['membership_name'] ?> Membership:</small></td>
                        <td class="text-end"><small class="text-success"><?= $membership['service_discount'] ?>% off services</small></td>
                    </tr>
                    <?php endif; ?>
                <?php endif; ?>
                <?php if ($invoice['tax_amount'] > 0): ?>
                    <tr>
                        <td class="text-end"><strong>Tax:</strong></td>
                        <td class="text-end"><?= format_currency($invoice['tax_amount']) ?></td>
                    </tr>
                <?php endif; ?>
                <tr class="total-row">
                    <td class="text-end"><strong>Total:</strong></td>
                    <td class="text-end"><?= format_currency($invoice['total_amount']) ?></td>
                </tr>
                <tr>
                    <td class="text-end"><strong>Amount Paid:</strong></td>
                    <td class="text-end text-success"><?= format_currency($invoice['payment_amount']) ?></td>
                </tr>
                <?php if ($invoice['payment_amount'] < $invoice['total_amount']): ?>
                    <tr>
                        <td class="text-end"><strong>Balance Due:</strong></td>
                        <td class="text-end text-danger"><?= format_currency($invoice['total_amount'] - $invoice['payment_amount']) ?></td>
                    </tr>
                <?php endif; ?>
            </table>
        </div>
    </div>

    <div class="footer">
        <p>Thank you for your business!</p>
        <p>This invoice was generated on <?= format_datetime(date('Y-m-d H:i:s')) ?></p>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            // Delay printing to ensure everything is loaded
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>
