<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-user"></i> Staff Profile</h1>
        <div>
            <a href="<?= base_url('staff/edit/' . $staff['id']) ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Profile
            </a>
            <a href="<?= base_url('staff') ?>" class="btn btn-outline-primary ms-2">
                <i class="fas fa-arrow-left"></i> Back to Staff
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Staff Information -->
        <div class="col-md-4 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Staff Information</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar-circle mx-auto mb-3">
                            <span class="avatar-initials"><?= substr($staff['name'], 0, 1) ?></span>
                        </div>
                        <h4><?= $staff['name'] ?></h4>
                        <span class="badge bg-info"><?= isset($staff['role']) ? ucfirst($staff['role']) : 'Staff' ?></span>
                        <span class="badge <?= $staff['status'] == 'active' ? 'bg-success' : 'bg-secondary' ?>">
                            <?= ucfirst($staff['status']) ?>
                        </span>
                    </div>

                    <div class="mb-3">
                        <h6 class="border-bottom pb-2">Contact Information</h6>
                        <div class="d-flex mb-2">
                            <div class="text-muted me-2" style="width: 30px;"><i class="fas fa-envelope"></i></div>
                            <div><?= $staff['email'] ?></div>
                        </div>
                        <div class="d-flex mb-2">
                            <div class="text-muted me-2" style="width: 30px;"><i class="fas fa-phone"></i></div>
                            <div><?= $staff['phone'] ? $staff['phone'] : 'Not provided' ?></div>
                        </div>
                        <div class="d-flex">
                            <div class="text-muted me-2" style="width: 30px;"><i class="fas fa-map-marker-alt"></i></div>
                            <div><?= $staff['address'] ? $staff['address'] : 'Not provided' ?></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6 class="border-bottom pb-2">Employment Details</h6>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Position:</span>
                            <span class="fw-bold"><?= $staff['position'] ? $staff['position'] : 'Not specified' ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Joining Date:</span>
                            <span class="fw-bold"><?= format_date($staff['joining_date']) ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Monthly Salary:</span>
                            <span class="fw-bold"><?= format_currency($staff['salary']) ?></span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span class="text-muted">Commission Rate:</span>
                            <span class="fw-bold"><?= $staff['commission_rate'] ?>%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Services & Payroll -->
        <div class="col-md-8 mb-4">
            <!-- Services -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Services Provided</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($services)): ?>
                        <div class="text-center py-4">
                            <p class="text-muted">No services assigned to this staff member.</p>
                            <a href="<?= base_url('staff/edit/' . $staff['id']) ?>" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> Assign Services
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($services as $service): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card border">
                                        <div class="card-body p-3">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1"><?= $service['service_name'] ?></h6>
                                                    <div class="small text-muted">
                                                        <?= $service['duration'] ?> min | <?= format_currency($service['price']) ?>
                                                    </div>
                                                </div>
                                                <span class="badge bg-primary"><?= $staff['commission_rate'] ?>% Commission</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Payroll History -->
            <div class="card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Payroll History</h5>
                    <a href="<?= base_url('payroll/create?staff_id=' . $staff['id']) ?>" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> Create Payroll
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($payroll_history)): ?>
                        <div class="text-center py-4">
                            <p class="text-muted">No payroll records found for this staff member.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Pay Period</th>
                                        <th>Payment Date</th>
                                        <th>Basic Salary</th>
                                        <th>Commission</th>
                                        <th>Net Salary</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($payroll_history as $payroll): ?>
                                        <tr>
                                            <td>
                                                <?= format_date($payroll['pay_period_start']) ?> to <?= format_date($payroll['pay_period_end']) ?>
                                            </td>
                                            <td><?= format_date($payroll['payment_date']) ?></td>
                                            <td><?= format_currency($payroll['basic_salary']) ?></td>
                                            <td><?= format_currency($payroll['commission_amount']) ?></td>
                                            <td class="fw-bold"><?= format_currency($payroll['net_salary']) ?></td>
                                            <td>
                                                <?php if ($payroll['status'] == 'paid'): ?>
                                                    <span class="badge bg-success">Paid</span>
                                                <?php elseif ($payroll['status'] == 'pending'): ?>
                                                    <span class="badge bg-warning">Pending</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary"><?= ucfirst($payroll['status']) ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="<?= base_url('payroll/view/' . $payroll['id']) ?>" class="btn btn-outline-primary" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('payroll/print/' . $payroll['id']) ?>" class="btn btn-outline-info" title="Print" target="_blank">
                                                        <i class="fas fa-print"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 100px;
    height: 100px;
    background-color: #3498db;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.avatar-initials {
    color: white;
    font-size: 48px;
    font-weight: bold;
    text-transform: uppercase;
}
</style>
