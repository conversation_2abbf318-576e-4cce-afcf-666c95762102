<?php
/**
 * Discount Controller (formerly Membership Controller)
 * Handles membership plans and discount management
 */
class DiscountController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            flash('error', 'Please login to access this page');
            header('Location: ' . base_url('login'));
            exit;
        }

        // Check if user has admin or manager role
        if (!has_role(['admin', 'manager'])) {
            flash('error', 'You do not have permission to access this page');
            header('Location: ' . base_url());
            exit;
        }
    }

    /**
     * Display all memberships
     */
    public function index() {
        // Get all memberships
        $membership_model = new MembershipModel();
        $memberships = $membership_model->all();

        // Get active members count for each membership
        foreach ($memberships as &$membership) {
            $membership['active_members'] = $membership_model->countActiveMembers($membership['id']);
        }

        // Render view
        $this->render('discounts/index', [
            'memberships' => $memberships
        ]);
    }

    /**
     * Display create membership form
     */
    public function create() {
        // Get services for benefits
        $service_model = new ServiceModel();
        $services = $service_model->all();

        // Render view
        $this->render('discounts/create', [
            'services' => $services
        ]);
    }

    /**
     * Store new membership
     */
    public function store() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('discounts'));
        }

        // Get form data
        $name = input('name');
        $description = input('description');
        $price = input('price');
        $duration = input('duration');
        $duration_unit = input('duration_unit');
        $benefits = input('benefits');
        $discount_percentage = input('discount_percentage');
        $status = input('status');

        // Validate data
        if (empty($name)) {
            flash('error', 'Membership name is required');
            $this->redirect(base_url('discounts/create'));
        }

        if (empty($price) || !is_numeric($price) || $price < 0) {
            flash('error', 'Please enter a valid price');
            $this->redirect(base_url('discounts/create'));
        }

        if (empty($duration) || !is_numeric($duration) || $duration <= 0) {
            flash('error', 'Please enter a valid duration');
            $this->redirect(base_url('discounts/create'));
        }

        // Create membership
        $membership_model = new MembershipModel();

        // Convert duration to days based on the selected unit
        $days = $duration;
        if ($duration_unit == 'months') {
            $days = $duration * 30; // Approximate
        } else if ($duration_unit == 'years') {
            $days = $duration * 365; // Approximate
        }

        $membership_data = [
            'name' => $name,
            'description' => $description,
            'price' => $price,
            'duration' => $days,
            'service_discount' => $discount_percentage,
            'product_discount' => $discount_percentage,
            'status' => $status
        ];

        $result = $membership_model->create($membership_data);

        if (!$result) {
            flash('error', 'Failed to create membership');
            $this->redirect(base_url('discounts/create'));
        }

        flash('success', 'Membership created successfully');
        $this->redirect(base_url('discounts'));
    }

    /**
     * Display edit membership form
     */
    public function edit($id) {
        // Get membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->find($id);

        if (!$membership) {
            flash('error', 'Membership not found');
            $this->redirect(base_url('discounts'));
        }

        // Get services for benefits
        $service_model = new ServiceModel();
        $services = $service_model->all();

        // Render view
        $this->render('discounts/edit', [
            'membership' => $membership,
            'services' => $services
        ]);
    }

    /**
     * Update membership
     */
    public function update($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('discounts'));
        }

        // Get membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->find($id);

        if (!$membership) {
            flash('error', 'Membership not found');
            $this->redirect(base_url('discounts'));
        }

        // Get form data
        $name = input('name');
        $description = input('description');
        $price = input('price');
        $duration = input('duration');
        $duration_unit = input('duration_unit');
        $benefits = input('benefits');
        $discount_percentage = input('discount_percentage');
        $status = input('status');

        // Validate data
        if (empty($name)) {
            flash('error', 'Membership name is required');
            $this->redirect(base_url('discounts/edit/' . $id));
        }

        if (empty($price) || !is_numeric($price) || $price < 0) {
            flash('error', 'Please enter a valid price');
            $this->redirect(base_url('discounts/edit/' . $id));
        }

        if (empty($duration) || !is_numeric($duration) || $duration <= 0) {
            flash('error', 'Please enter a valid duration');
            $this->redirect(base_url('discounts/edit/' . $id));
        }

        // Update membership

        // Convert duration to days based on the selected unit
        $days = $duration;
        if ($duration_unit == 'months') {
            $days = $duration * 30; // Approximate
        } else if ($duration_unit == 'years') {
            $days = $duration * 365; // Approximate
        }

        $membership_data = [
            'name' => $name,
            'description' => $description,
            'price' => $price,
            'duration' => $days,
            'service_discount' => $discount_percentage,
            'product_discount' => $discount_percentage,
            'status' => $status
        ];

        $result = $membership_model->update($id, $membership_data);

        if (!$result) {
            flash('error', 'Failed to update membership');
            $this->redirect(base_url('discounts/edit/' . $id));
        }

        flash('success', 'Membership updated successfully');
        $this->redirect(base_url('discounts'));
    }

    /**
     * Delete membership
     */
    public function delete($id) {
        // Get membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->find($id);

        if (!$membership) {
            flash('error', 'Membership not found');
            $this->redirect(base_url('discounts'));
        }

        // Check if membership has active members
        $active_members = $membership_model->countActiveMembers($id);
        if ($active_members > 0) {
            flash('error', 'Cannot delete membership with active members');
            $this->redirect(base_url('discounts'));
        }

        // Delete membership
        $result = $membership_model->delete($id);

        if (!$result) {
            flash('error', 'Failed to delete membership');
            $this->redirect(base_url('discounts'));
        }

        flash('success', 'Membership deleted successfully');
        $this->redirect(base_url('discounts'));
    }

    /**
     * Display membership members
     */
    public function members($id) {
        // Get membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->find($id);

        if (!$membership) {
            flash('error', 'Membership not found');
            $this->redirect(base_url('discounts'));
        }

        // Get members
        $members = $membership_model->getMembers($id);

        // Render view
        $this->render('discounts/members', [
            'membership' => $membership,
            'members' => $members
        ]);
    }

    /**
     * Display add member form
     */
    public function addMember($id) {
        // Get membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->find($id);

        if (!$membership) {
            flash('error', 'Membership not found');
            $this->redirect(base_url('discounts'));
        }

        // Get customers with user data (including names)
        $customer_model = new CustomerModel();
        $customers = $customer_model->getAllWithUsers();

        // Render view
        $this->render('discounts/add_member', [
            'membership' => $membership,
            'customers' => $customers
        ]);
    }

    /**
     * Store new member
     */
    public function storeMember($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('discounts/members/' . $id));
        }

        // Get membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->find($id);

        if (!$membership) {
            flash('error', 'Membership not found');
            $this->redirect(base_url('discounts'));
        }

        // Get form data
        $customer_id = input('customer_id');
        $start_date = input('start_date');
        $end_date = input('end_date');
        $payment_method = input('payment_method');
        $notes = input('notes');

        // Validate data
        if (empty($customer_id)) {
            flash('error', 'Please select a customer');
            $this->redirect(base_url('discounts/add-member/' . $id));
        }

        if (empty($start_date)) {
            flash('error', 'Please enter start date');
            $this->redirect(base_url('discounts/add-member/' . $id));
        }

        if (empty($end_date)) {
            flash('error', 'Please enter end date');
            $this->redirect(base_url('discounts/add-member/' . $id));
        }

        // Check if customer already has an active membership for this plan
        $existing_member = $membership_model->getActiveMemberByCustomerAndPlan($customer_id, $id);
        if ($existing_member) {
            flash('error', 'Customer already has an active membership for this plan');
            $this->redirect(base_url('discounts/add-member/' . $id));
        }

        // Create member with required fields
        $member_data = [
            'customer_id' => $customer_id,
            'plan_id' => $id,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'status' => 'active'
        ];

        // Add optional fields if they have values
        if (!empty($payment_method)) {
            $member_data['payment_method'] = $payment_method;
        }

        if (isset($membership['price']) && $membership['price'] > 0) {
            $member_data['amount_paid'] = $membership['price'];
        }

        if (!empty($notes)) {
            $member_data['notes'] = $notes;
        }

        $result = $membership_model->addMember($member_data);

        if (!$result) {
            flash('error', 'Failed to add member');
            $this->redirect(base_url('discounts/add-member/' . $id));
        }

        flash('success', 'Member added successfully');
        $this->redirect(base_url('discounts/members/' . $id));
    }

    /**
     * Cancel membership
     */
    public function cancelMembership($id) {
        // Get member
        $membership_model = new MembershipModel();
        $member = $membership_model->getMemberById($id);

        if (!$member) {
            flash('error', 'Member not found');
            $this->redirect(base_url('discounts'));
        }

        // Update member status
        $result = $membership_model->updateMemberStatus($id, 'cancelled');

        if (!$result) {
            flash('error', 'Failed to cancel membership');
            $this->redirect(base_url('discounts/members/' . $member['plan_id']));
        }

        flash('success', 'Membership cancelled successfully');
        $this->redirect(base_url('discounts/members/' . $member['plan_id']));
    }

    /**
     * Renew membership
     */
    public function renewMembership($id) {
        // Get member
        $membership_model = new MembershipModel();
        $member = $membership_model->getMemberById($id);

        if (!$member) {
            flash('error', 'Member not found');
            $this->redirect(base_url('discounts'));
        }

        // Update member status
        $result = $membership_model->updateMemberStatus($id, 'active');

        if (!$result) {
            flash('error', 'Failed to renew membership');
            $this->redirect(base_url('discounts/members/' . $member['plan_id']));
        }

        flash('success', 'Membership renewed successfully');
        $this->redirect(base_url('discounts/members/' . $member['plan_id']));
    }
}
