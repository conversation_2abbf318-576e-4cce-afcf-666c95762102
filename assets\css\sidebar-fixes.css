/* Hide dropdown arrow */
.dropdown-toggle::after {
    display: none !important;
}

/* Sidebar Scrolling Fixes */
.sidebar {
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.sidebar::-webkit-scrollbar {
    width: 5px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
}

/* Keep active menu item in view */
.sidebar-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border-left-color: var(--primary);
    position: relative;
}

/* Hide sidebar toggle on desktop */
@media (min-width: 992px) {
    .sidebar-toggle {
        display: none;
    }
}

/* Fix main content display */
.main-content {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Adjust display-4 size */
@media (min-width: 992px) {
    .display-4 {
        font-size: 2rem !important;
        font-weight: 500 !important;
    }
}

/* Mobile sidebar fixes */
@media (max-width: 991px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: 1050; /* Higher z-index to appear above content */
    }

    .sidebar.show {
        transform: translateX(0);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }

    /* Add overlay when sidebar is shown on mobile */
    .sidebar-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1040;
    }

    .sidebar-overlay.show {
        display: block;
    }

    /* Fix mobile toggle button */
    .mobile-sidebar-toggle {
        display: block;
        padding: 10px;
        margin-right: 10px;
        position: relative;
        z-index: 1060;
    }

    .mobile-sidebar-toggle i {
        font-size: 1.5rem;
        display: block;
        line-height: 1;
    }
}
