<?php
/**
 * Billing Controller
 */
class BillingController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            flash('error', 'Please login to access this page');
            header('Location: ' . base_url('login'));
            exit;
        }

        // Check if user has admin, manager, or staff role
        if (!has_role(['admin', 'manager', 'staff'])) {
            flash('error', 'You do not have permission to access this page');
            header('Location: ' . base_url());
            exit;
        }
    }

    /**
     * Display all invoices
     */
    public function index() {
        // Get filter parameters
        $status = input('status');
        $date_from = input('date_from');
        $date_to = input('date_to');
        $customer_id = input('customer_id');

        // Get invoices
        $invoice_model = new InvoiceModel();
        $invoices = $invoice_model->getAllWithDetails($status, $date_from, $date_to, $customer_id);

        // Get customers for filter
        $customer_model = new CustomerModel();
        $customers = $customer_model->getAllWithUsers();

        // Get sales statistics
        $sales_stats = $invoice_model->getSalesStatistics();

        // Render view
        $this->render('billing/index', [
            'invoices' => $invoices,
            'customers' => $customers,
            'sales_stats' => $sales_stats,
            'selected_status' => $status,
            'selected_date_from' => $date_from,
            'selected_date_to' => $date_to,
            'selected_customer' => $customer_id
        ]);
    }

    /**
     * Display invoice creation form
     */
    public function create() {
        // Get top customers
        $customer_model = new CustomerModel();
        $top_customers = $customer_model->getTopCustomers(10);

        // Get all customers for reference
        $all_customers = $customer_model->getAllWithUsers();

        // Get top 10 most used services
        $service_model = new ServiceModel();
        $top_services = $service_model->getTopUsedServices(10);

        // Get all services for search functionality
        $all_services = $service_model->getAllActive();

        // Get staff
        $staff_model = new StaffModel();
        $staff = $staff_model->getAllWithUsers();

        // Get products
        $product_model = new ProductModel();
        $products = $product_model->getAllWithCategories();

        // Get settings for tax rate
        $setting_model = new SettingModel();
        $settings = $setting_model->getAllSettings();

        // Add invoice-specific CSS for mobile
        $styles = '<link rel="stylesheet" href="' . base_url('assets/css/invoice-mobile.css') . '">';

        // Render view
        $this->render('billing/create', [
            'top_customers' => $top_customers,
            'all_customers' => $all_customers,
            'top_services' => $top_services,
            'all_services' => $all_services,
            'staff' => $staff,
            'products' => $products,
            'settings' => $settings,
            'styles' => $styles
        ]);
    }

    /**
     * Store new invoice
     */
    public function store() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('billing/create'));
        }

        // Get form data
        $customer_id = input('customer_id');
        $invoice_date = input('invoice_date');
        $payment_method = input('payment_method');
        $payment_amount = input('payment_amount');
        $wallet_amount = input('wallet_amount', 0);
        $notes = input('notes');
        $status = input('payment_status');

        // Get services and products
        $services = isset($_POST['services']) ? array_filter($_POST['services'], function($service) {
            return !empty($service['service_id']);
        }) : [];
        $products = isset($_POST['products']) ? array_filter($_POST['products'], function($product) {
            return !empty($product['product_id']);
        }) : [];

        // Validate form data
        if (empty($customer_id) || empty($invoice_date) || empty($payment_method)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('billing/create'));
        }

        if (empty($services) && empty($products)) {
            flash('error', 'Please add at least one service or product');
            $this->redirect(base_url('billing/create'));
        }

        // Calculate totals
        $subtotal = 0;
        $discount = 0;
        $tax = 0;

        // Calculate services subtotal
        $service_model = new ServiceModel();
        foreach ($services as $service) {
            $service_data = $service_model->find($service['service_id']);
            if ($service_data) {
                // Use the price from the form if available, otherwise use the service price
                $price = isset($service['unit_price']) && !empty($service['unit_price']) ? $service['unit_price'] : $service_data['price'];
                $subtotal += $price * $service['quantity'];
            }
        }

        // Calculate products subtotal
        $product_model = new ProductModel();
        foreach ($products as $product) {
            $product_data = $product_model->find($product['product_id']);
            if ($product_data) {
                // Use the price from the form if available, otherwise use the product price
                $price = isset($product['unit_price']) && !empty($product['unit_price']) ? $product['unit_price'] : $product_data['price'];
                $subtotal += $price * $product['quantity'];

                // Update product quantity
                $new_quantity = $product_data['quantity'] - $product['quantity'];
                if ($new_quantity < 0) {
                    flash('error', 'Not enough stock for product: ' . $product_data['name']);
                    $this->redirect(base_url('billing/create'));
                }
                $product_model->updateQuantity($product['product_id'], $new_quantity);
            }
        }

        // Get discount percentage and pre-calculated value
        $discount_percentage = input('discount_amount');
        $discount_value = input('discount_value');
        $coupon_id = input('coupon_id');

        // Get tax rate and amount
        $tax_rate = input('tax_rate');
        $tax_amount = input('tax_amount');

        // Use the tax amount from the form
        $tax = !empty($tax_amount) ? floatval($tax_amount) : 0;

        // Use the pre-calculated discount value from JavaScript
        $discount = !empty($discount_value) ? floatval($discount_value) : 0;

        // Calculate subtotal with tax
        $subtotal_with_tax = $subtotal + $tax;

        // Calculate total (rounded to nearest integer)
        $total_exact = $subtotal_with_tax - $discount;
        $total_amount = round($total_exact);

        // Handle wallet payment if specified
        if ($wallet_amount > 0) {
            $wallet_model = new WalletModel();

            // Check if customer has sufficient wallet balance
            if (!$wallet_model->hasSufficientBalance($customer_id, $wallet_amount)) {
                flash('error', 'Insufficient wallet balance');
                $this->redirect(base_url('billing/create'));
            }

            // Adjust payment method if wallet is used
            if ($wallet_amount >= $total_amount) {
                $payment_method = 'wallet';
                $payment_amount = $total_amount;
                $wallet_amount = $total_amount;
            } else {
                $payment_method = 'mixed';
                $payment_amount = $wallet_amount + ($payment_amount - $wallet_amount);
            }
        }

        // Create invoice
        $invoice_model = new InvoiceModel();
        $invoice_data = [
            'customer_id' => $customer_id,
            'invoice_date' => $invoice_date,
            'subtotal' => $subtotal,
            'discount_amount' => $discount,
            'wallet_amount' => $wallet_amount,
            'tax_amount' => $tax,
            'total_amount' => $total_amount,
            'payment_amount' => $payment_amount,
            'payment_method' => $payment_method,
            'notes' => $notes,
            'payment_status' => $status
        ];

        $invoice_id = $invoice_model->create($invoice_data);

        if (!$invoice_id) {
            flash('error', 'Failed to create invoice');
            $this->redirect(base_url('billing/create'));
        }

        // Add services to invoice
        $invoice_service_model = new InvoiceServiceModel();
        foreach ($services as $service) {
            $service_data = $service_model->find($service['service_id']);
            if ($service_data) {
                // Use the price from the form if available, otherwise use the service price
                $price = isset($service['unit_price']) && !empty($service['unit_price']) ? $service['unit_price'] : $service_data['price'];

                $invoice_service_data = [
                    'invoice_id' => $invoice_id,
                    'service_id' => $service['service_id'],
                    'quantity' => $service['quantity'],
                    'unit_price' => $price,
                    'total_price' => $price * $service['quantity']
                ];

                // Only add staff_id if it's provided and not empty
                if (isset($service['staff_id']) && !empty($service['staff_id'])) {
                    $invoice_service_data['staff_id'] = $service['staff_id'];
                } else {
                    // If no staff_id is provided, use the first available staff
                    $staff_model = new StaffModel();
                    $all_staff = $staff_model->getAllWithUsers();
                    if (!empty($all_staff)) {
                        $invoice_service_data['staff_id'] = $all_staff[0]['id'];
                    } else {
                        // If no staff exists, create a default one
                        $user_model = new UserModel();
                        $default_user = $user_model->findByEmail('<EMAIL>');

                        if ($default_user) {
                            $staff_data = [
                                'user_id' => $default_user['id'],
                                'position' => 'Default Staff',
                                'commission_rate' => 0
                            ];
                            $staff_id = $staff_model->create($staff_data);
                            $invoice_service_data['staff_id'] = $staff_id;
                        } else {
                            // Last resort - use a hardcoded staff_id of 1
                            $invoice_service_data['staff_id'] = 1;
                        }
                    }
                }

                $invoice_service_model->create($invoice_service_data);
            }
        }

        // Add products to invoice
        $invoice_product_model = new InvoiceProductModel();
        foreach ($products as $product) {
            $product_data = $product_model->find($product['product_id']);
            if ($product_data) {
                // Use the price from the form if available, otherwise use the product price
                $price = isset($product['unit_price']) && !empty($product['unit_price']) ? $product['unit_price'] : $product_data['price'];

                $invoice_product_data = [
                    'invoice_id' => $invoice_id,
                    'product_id' => $product['product_id'],
                    'quantity' => $product['quantity'],
                    'unit_price' => $price,
                    'total_price' => $price * $product['quantity']
                ];

                $invoice_product_model->create($invoice_product_data);
            }
        }

        // Record coupon usage if a coupon was applied
        if (!empty($coupon_id)) {
            $coupon_model = new CouponModel();
            $coupon_model->recordUsage($coupon_id, $invoice_id, $discount);
        }

        // Deduct wallet amount if wallet payment was used
        if ($wallet_amount > 0) {
            $wallet_model = new WalletModel();
            $wallet_result = $wallet_model->deductMoney(
                $customer_id,
                $wallet_amount,
                'Payment for Invoice #' . $invoice_id,
                $_SESSION['user_id'],
                'invoice_payment',
                $invoice_id
            );

            if (!$wallet_result) {
                // If wallet deduction fails, we should handle this appropriately
                // For now, we'll log the error but still proceed
                error_log("Failed to deduct wallet amount for invoice #" . $invoice_id);
                flash('warning', 'Invoice created but wallet deduction failed. Please check wallet manually.');
            }
        }

        flash('success', 'Invoice created successfully');
        $this->redirect(base_url('billing/view/' . $invoice_id));
    }

    /**
     * Display invoice details
     */
    public function view($id) {
        // Get invoice
        $invoice_model = new InvoiceModel();
        $invoice = $invoice_model->getWithDetails($id);

        if (!$invoice) {
            flash('error', 'Invoice not found');
            $this->redirect(base_url('billing'));
        }

        // Debug: Check if services are being retrieved
        if (empty($invoice['services'])) {
            // If no services found, let's check the invoice_services table directly
            $db = new Database();
            $conn = $db->getConnection();

            $query = "SELECT * FROM invoice_services WHERE invoice_id = :invoice_id";
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':invoice_id', $id);
            $stmt->execute();
            $services_raw = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (!empty($services_raw)) {
                // Services exist in the database but weren't retrieved properly
                // Let's manually get the service details
                foreach ($services_raw as $service) {
                    $query = "SELECT s.name as service_name, s.description as service_description,
                             s.duration as service_duration, sc.name as service_category_name,
                             u.name as staff_name
                             FROM services s
                             JOIN service_categories sc ON s.category_id = sc.id
                             JOIN staff st ON :staff_id = st.id
                             JOIN users u ON st.user_id = u.id
                             WHERE s.id = :service_id";
                    $stmt = $conn->prepare($query);
                    $stmt->bindParam(':service_id', $service['service_id']);
                    $stmt->bindParam(':staff_id', $service['staff_id']);
                    $stmt->execute();
                    $service_details = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($service_details) {
                        // Merge the service details with the service data
                        $service = array_merge($service, $service_details);
                        $invoice['services'][] = $service;
                    }
                }
            }
        }

        // Determine discount type
        if ($invoice['discount_amount'] > 0) {
            // Check if coupon was used
            if (isset($invoice['coupon'])) {
                $invoice['discount_type'] = 'coupon';
            } else {
                // Check if customer has an active membership
                $customer_id = $invoice['customer_id'];
                $membership_model = new MembershipModel();
                $membership = $membership_model->getActiveMembershipByCustomerId($customer_id);

                if ($membership) {
                    $invoice['discount_type'] = 'membership';
                    $invoice['membership'] = $membership;
                } else {
                    $invoice['discount_type'] = 'manual';
                }
            }
        }

        // Get settings
        $settings_model = new SettingsModel();
        $settings = $settings_model->get();

        // Render view
        $this->render('billing/view', [
            'invoice' => $invoice,
            'settings' => $settings
        ]);
    }

    /**
     * Print invoice (A5 format)
     */
    public function print($id) {
        // Get invoice
        $invoice_model = new InvoiceModel();
        $invoice = $invoice_model->getWithDetails($id);

        if (!$invoice) {
            flash('error', 'Invoice not found');
            $this->redirect(base_url('billing'));
        }

        // Debug: Check if services are being retrieved
        if (empty($invoice['services'])) {
            // If no services found, let's check the invoice_services table directly
            $db = new Database();
            $conn = $db->getConnection();

            $query = "SELECT * FROM invoice_services WHERE invoice_id = :invoice_id";
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':invoice_id', $id);
            $stmt->execute();
            $services_raw = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (!empty($services_raw)) {
                // Services exist in the database but weren't retrieved properly
                // Let's manually get the service details
                foreach ($services_raw as $service) {
                    $query = "SELECT s.name as service_name, s.description as service_description,
                             s.duration as service_duration, sc.name as service_category_name,
                             u.name as staff_name
                             FROM services s
                             JOIN service_categories sc ON s.category_id = sc.id
                             JOIN staff st ON :staff_id = st.id
                             JOIN users u ON st.user_id = u.id
                             WHERE s.id = :service_id";
                    $stmt = $conn->prepare($query);
                    $stmt->bindParam(':service_id', $service['service_id']);
                    $stmt->bindParam(':staff_id', $service['staff_id']);
                    $stmt->execute();
                    $service_details = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($service_details) {
                        // Merge the service details with the service data
                        $service = array_merge($service, $service_details);
                        $invoice['services'][] = $service;
                    }
                }
            }
        }

        // Get salon settings
        $setting_model = new SettingModel();
        $settings = $setting_model->getAllSettings();

        // Render view without the main layout
        $this->renderStandalone('billing/a5_print', [
            'invoice' => $invoice,
            'settings' => $settings
        ]);
    }

    /**
     * Print invoice (A4 format)
     */
    public function printA4($id) {
        // Get invoice
        $invoice_model = new InvoiceModel();
        $invoice = $invoice_model->getWithDetails($id);

        if (!$invoice) {
            flash('error', 'Invoice not found');
            $this->redirect(base_url('billing'));
        }

        // Debug: Check if services are being retrieved
        if (empty($invoice['services'])) {
            // If no services found, let's check the invoice_services table directly
            $db = new Database();
            $conn = $db->getConnection();

            $query = "SELECT * FROM invoice_services WHERE invoice_id = :invoice_id";
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':invoice_id', $id);
            $stmt->execute();
            $services_raw = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (!empty($services_raw)) {
                // Services exist in the database but weren't retrieved properly
                // Let's manually get the service details
                foreach ($services_raw as $service) {
                    $query = "SELECT s.name as service_name, s.description as service_description,
                             s.duration as service_duration, sc.name as service_category_name,
                             u.name as staff_name
                             FROM services s
                             JOIN service_categories sc ON s.category_id = sc.id
                             JOIN staff st ON :staff_id = st.id
                             JOIN users u ON st.user_id = u.id
                             WHERE s.id = :service_id";
                    $stmt = $conn->prepare($query);
                    $stmt->bindParam(':service_id', $service['service_id']);
                    $stmt->bindParam(':staff_id', $service['staff_id']);
                    $stmt->execute();
                    $service_details = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($service_details) {
                        // Merge the service details with the service data
                        $service = array_merge($service, $service_details);
                        $invoice['services'][] = $service;
                    }
                }
            }
        }

        // Get salon settings
        $setting_model = new SettingModel();
        $settings = $setting_model->getAllSettings();

        // Render view without the main layout
        $this->renderStandalone('billing/print', [
            'invoice' => $invoice,
            'settings' => $settings
        ]);
    }

    /**
     * Print thermal receipt (2.5 inch width)
     */
    public function thermalPrint($id) {
        // Get invoice
        $invoice_model = new InvoiceModel();
        $invoice = $invoice_model->getWithDetails($id);

        if (!$invoice) {
            flash('error', 'Invoice not found');
            $this->redirect(base_url('billing'));
        }

        // Debug: Check if services are being retrieved
        if (empty($invoice['services'])) {
            // If no services found, let's check the invoice_services table directly
            $db = new Database();
            $conn = $db->getConnection();

            $query = "SELECT * FROM invoice_services WHERE invoice_id = :invoice_id";
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':invoice_id', $id);
            $stmt->execute();
            $services_raw = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (!empty($services_raw)) {
                // Services exist in the database but weren't retrieved properly
                // Let's manually get the service details
                foreach ($services_raw as $service) {
                    $query = "SELECT s.name as service_name, s.description as service_description,
                             s.duration as service_duration, sc.name as service_category_name,
                             u.name as staff_name
                             FROM services s
                             JOIN service_categories sc ON s.category_id = sc.id
                             JOIN staff st ON :staff_id = st.id
                             JOIN users u ON st.user_id = u.id
                             WHERE s.id = :service_id";
                    $stmt = $conn->prepare($query);
                    $stmt->bindParam(':service_id', $service['service_id']);
                    $stmt->bindParam(':staff_id', $service['staff_id']);
                    $stmt->execute();
                    $service_details = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($service_details) {
                        // Merge the service details with the service data
                        $service = array_merge($service, $service_details);
                        $invoice['services'][] = $service;
                    }
                }
            }
        }

        // Get salon settings
        $setting_model = new SettingModel();
        $settings = $setting_model->getAllSettings();

        // Render view without the main layout
        $this->renderStandalone('billing/thermal_print', [
            'invoice' => $invoice,
            'settings' => $settings
        ]);
    }

    /**
     * Update invoice status
     */
    public function updateStatus($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('billing'));
        }

        // Get invoice
        $invoice_model = new InvoiceModel();
        $invoice = $invoice_model->find($id);

        if (!$invoice) {
            flash('error', 'Invoice not found');
            $this->redirect(base_url('billing'));
        }

        // Get form data
        $status = input('status');

        // Validate form data
        if (empty($status)) {
            flash('error', 'Please select a status');
            $this->redirect(base_url('billing/view/' . $id));
        }

        // Update invoice status
        $result = $invoice_model->update($id, ['status' => $status]);

        if (!$result) {
            flash('error', 'Failed to update invoice status');
            $this->redirect(base_url('billing/view/' . $id));
        }

        flash('success', 'Invoice status updated successfully');
        $this->redirect(base_url('billing/view/' . $id));
    }

    /**
     * Display invoice edit form
     */
    public function edit($id) {
        // Get invoice
        $invoice_model = new InvoiceModel();
        $invoice = $invoice_model->getWithDetails($id);

        if (!$invoice) {
            flash('error', 'Invoice not found');
            $this->redirect(base_url('billing'));
        }

        // Get top customers
        $customer_model = new CustomerModel();
        $top_customers = $customer_model->getTopCustomers(10);

        // Get all customers for reference
        $all_customers = $customer_model->getAllWithUsers();

        // Get top 10 most used services
        $service_model = new ServiceModel();
        $top_services = $service_model->getTopUsedServices(10);

        // Get all services for search functionality
        $all_services = $service_model->getAllActive();

        // Get staff
        $staff_model = new StaffModel();
        $staff = $staff_model->getAllWithUsers();

        // Get products
        $product_model = new ProductModel();
        $products = $product_model->getAllWithCategories();

        // Get settings for tax rate
        $setting_model = new SettingModel();
        $settings = $setting_model->getAllSettings();

        // Add invoice-specific CSS for mobile
        $styles = '<link rel="stylesheet" href="' . base_url('assets/css/invoice-mobile.css') . '">';

        // Render view
        $this->render('billing/edit', [
            'invoice' => $invoice,
            'top_customers' => $top_customers,
            'all_customers' => $all_customers,
            'top_services' => $top_services,
            'all_services' => $all_services,
            'staff' => $staff,
            'products' => $products,
            'settings' => $settings,
            'styles' => $styles
        ]);
    }

    /**
     * Update invoice
     */
    public function update($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('billing/edit/' . $id));
        }

        // Get invoice
        $invoice_model = new InvoiceModel();
        $invoice = $invoice_model->find($id);

        if (!$invoice) {
            flash('error', 'Invoice not found');
            $this->redirect(base_url('billing'));
        }

        // Get form data
        $customer_id = input('customer_id');
        $invoice_date = input('invoice_date');
        $payment_method = input('payment_method');
        $payment_amount = input('payment_amount');
        $wallet_amount = input('wallet_amount', 0);
        $notes = input('notes');
        $status = input('payment_status');

        // Get services and products
        $services = isset($_POST['services']) ? array_filter($_POST['services'], function($service) {
            return !empty($service['service_id']);
        }) : [];
        $products = isset($_POST['products']) ? array_filter($_POST['products'], function($product) {
            return !empty($product['product_id']);
        }) : [];

        // Validate form data
        if (empty($customer_id) || empty($invoice_date) || empty($payment_method)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('billing/edit/' . $id));
        }

        if (empty($services) && empty($products)) {
            flash('error', 'Please add at least one service or product');
            $this->redirect(base_url('billing/edit/' . $id));
        }

        // Calculate totals
        $subtotal = 0;
        $discount = 0;
        $tax = 0;

        // Calculate services subtotal
        $service_model = new ServiceModel();
        foreach ($services as $service) {
            $service_data = $service_model->find($service['service_id']);
            if ($service_data) {
                // Use the price from the form if available, otherwise use the service price
                $price = isset($service['unit_price']) && !empty($service['unit_price']) ? $service['unit_price'] : $service_data['price'];
                $subtotal += $price * $service['quantity'];
            }
        }

        // Calculate products subtotal
        $product_model = new ProductModel();
        foreach ($products as $product) {
            $product_data = $product_model->find($product['product_id']);
            if ($product_data) {
                // Use the price from the form if available, otherwise use the product price
                $price = isset($product['unit_price']) && !empty($product['unit_price']) ? $product['unit_price'] : $product_data['price'];
                $subtotal += $price * $product['quantity'];
            }
        }

        // Get discount percentage and pre-calculated value
        $discount_percentage = input('discount_amount');
        $discount_value = input('discount_value');
        $coupon_id = input('coupon_id');

        // Get tax rate and amount
        $tax_rate = input('tax_rate');
        $tax_amount = input('tax_amount');

        // Use the tax amount from the form
        $tax = !empty($tax_amount) ? floatval($tax_amount) : 0;

        // Use the pre-calculated discount value from JavaScript
        $discount = !empty($discount_value) ? floatval($discount_value) : 0;

        // Calculate subtotal after discount
        $subtotal_after_discount = $subtotal - $discount;

        // Calculate tax on the discounted subtotal
        $tax = $subtotal_after_discount * ($tax_rate / 100);

        // Calculate total (rounded to nearest integer)
        $total_exact = $subtotal_after_discount + $tax;
        $total_amount = round($total_exact);

        // Handle wallet payment if specified
        if ($wallet_amount > 0) {
            $wallet_model = new WalletModel();

            // Check if customer has sufficient wallet balance
            if (!$wallet_model->hasSufficientBalance($customer_id, $wallet_amount)) {
                flash('error', 'Insufficient wallet balance');
                $this->redirect(base_url('billing/edit/' . $id));
            }

            // Adjust payment method if wallet is used
            if ($wallet_amount >= $total_amount) {
                $payment_method = 'wallet';
                $payment_amount = $total_amount;
                $wallet_amount = $total_amount;
            } else {
                $payment_method = 'mixed';
                $payment_amount = $wallet_amount + ($payment_amount - $wallet_amount);
            }
        }

        // Update invoice
        $invoice_data = [
            'customer_id' => $customer_id,
            'invoice_date' => $invoice_date,
            'subtotal' => $subtotal,
            'discount_amount' => $discount,
            'wallet_amount' => $wallet_amount,
            'tax_amount' => $tax,
            'total_amount' => $total_amount,
            'payment_amount' => $payment_amount,
            'payment_method' => $payment_method,
            'notes' => $notes,
            'payment_status' => $status
        ];

        $result = $invoice_model->update($id, $invoice_data);

        if (!$result) {
            flash('error', 'Failed to update invoice');
            $this->redirect(base_url('billing/edit/' . $id));
        }

        // Delete existing invoice services
        $invoice_service_model = new InvoiceServiceModel();
        $invoice_service_model->deleteByInvoiceId($id);

        // Add services to invoice
        foreach ($services as $service) {
            $service_data = $service_model->find($service['service_id']);
            if ($service_data) {
                // Use the price from the form if available, otherwise use the service price
                $price = isset($service['unit_price']) && !empty($service['unit_price']) ? $service['unit_price'] : $service_data['price'];

                $invoice_service_data = [
                    'invoice_id' => $id,
                    'service_id' => $service['service_id'],
                    'quantity' => $service['quantity'],
                    'unit_price' => $price,
                    'total_price' => $price * $service['quantity']
                ];

                // Only add staff_id if it's provided and not empty
                if (isset($service['staff_id']) && !empty($service['staff_id'])) {
                    $invoice_service_data['staff_id'] = $service['staff_id'];
                } else {
                    // If no staff_id is provided, use the first available staff
                    $staff_model = new StaffModel();
                    $all_staff = $staff_model->getAllWithUsers();
                    if (!empty($all_staff)) {
                        $invoice_service_data['staff_id'] = $all_staff[0]['id'];
                    } else {
                        // If no staff exists, create a default one
                        $user_model = new UserModel();
                        $default_user = $user_model->findByEmail('<EMAIL>');

                        if ($default_user) {
                            $staff_data = [
                                'user_id' => $default_user['id'],
                                'position' => 'Default Staff',
                                'commission_rate' => 0
                            ];
                            $staff_id = $staff_model->create($staff_data);
                            $invoice_service_data['staff_id'] = $staff_id;
                        } else {
                            // Last resort - use a hardcoded staff_id of 1
                            $invoice_service_data['staff_id'] = 1;
                        }
                    }
                }

                $invoice_service_model->create($invoice_service_data);
            }
        }

        // Delete existing invoice products
        $invoice_product_model = new InvoiceProductModel();
        $invoice_product_model->deleteByInvoiceId($id);

        // Add products to invoice
        foreach ($products as $product) {
            $product_data = $product_model->find($product['product_id']);
            if ($product_data) {
                // Use the price from the form if available, otherwise use the product price
                $price = isset($product['unit_price']) && !empty($product['unit_price']) ? $product['unit_price'] : $product_data['price'];

                $invoice_product_data = [
                    'invoice_id' => $id,
                    'product_id' => $product['product_id'],
                    'quantity' => $product['quantity'],
                    'unit_price' => $price,
                    'total_price' => $price * $product['quantity']
                ];

                $invoice_product_model->create($invoice_product_data);
            }
        }

        // Record coupon usage if a coupon was applied
        if (!empty($coupon_id)) {
            $coupon_model = new CouponModel();
            $coupon_model->recordUsage($coupon_id, $id, $discount);
        }

        // Handle wallet transaction if wallet payment was used
        if ($wallet_amount > 0) {
            $wallet_model = new WalletModel();

            // Check if there was a previous wallet transaction for this invoice
            $previous_wallet_amount = isset($invoice['wallet_amount']) ? $invoice['wallet_amount'] : 0;

            if ($previous_wallet_amount != $wallet_amount) {
                // If wallet amount changed, we need to adjust the wallet balance
                if ($previous_wallet_amount > 0) {
                    // Refund previous wallet amount
                    $wallet_model->addMoney(
                        $customer_id,
                        $previous_wallet_amount,
                        'Refund for Invoice #' . $id . ' (updated)',
                        $_SESSION['user_id'],
                        'refund',
                        $id
                    );
                }

                // Deduct new wallet amount
                $wallet_result = $wallet_model->deductMoney(
                    $customer_id,
                    $wallet_amount,
                    'Payment for Invoice #' . $id . ' (updated)',
                    $_SESSION['user_id'],
                    'invoice_payment',
                    $id
                );

                if (!$wallet_result) {
                    error_log("Failed to process wallet transaction for updated invoice #" . $id);
                    flash('warning', 'Invoice updated but wallet transaction failed. Please check wallet manually.');
                }
            }
        }

        flash('success', 'Invoice updated successfully');
        $this->redirect(base_url('billing/view/' . $id));
    }

    /**
     * Mark invoice as paid
     */
    public function markAsPaid($id) {
        // Get invoice
        $invoice_model = new InvoiceModel();
        $invoice = $invoice_model->find($id);

        if (!$invoice) {
            flash('error', 'Invoice not found');
            $this->redirect(base_url('billing'));
        }

        // Check if invoice is already paid
        if ($invoice['payment_status'] == 'paid') {
            flash('error', 'Invoice is already paid');
            $this->redirect(base_url('billing/view/' . $id));
        }

        // Check if invoice is cancelled
        if ($invoice['payment_status'] == 'cancelled') {
            flash('error', 'Cannot mark a cancelled invoice as paid');
            $this->redirect(base_url('billing/view/' . $id));
        }

        // Update invoice status to paid and payment amount to total amount
        $result = $invoice_model->update($id, [
            'payment_status' => 'paid',
            'payment_amount' => $invoice['total_amount']
        ]);

        if (!$result) {
            flash('error', 'Failed to mark invoice as paid');
            $this->redirect(base_url('billing/view/' . $id));
        }

        flash('success', 'Invoice marked as paid successfully');
        $this->redirect(base_url('billing/view/' . $id));
    }

    /**
     * Cancel invoice
     */
    public function cancel($id) {
        // Get invoice
        $invoice_model = new InvoiceModel();
        $invoice = $invoice_model->find($id);

        if (!$invoice) {
            flash('error', 'Invoice not found');
            $this->redirect(base_url('billing'));
        }

        // Check if invoice is already cancelled
        if ($invoice['payment_status'] == 'cancelled') {
            flash('error', 'Invoice is already cancelled');
            $this->redirect(base_url('billing/view/' . $id));
        }

        // Update invoice status to cancelled
        $result = $invoice_model->update($id, ['payment_status' => 'cancelled']);

        if (!$result) {
            flash('error', 'Failed to cancel invoice');
            $this->redirect(base_url('billing/view/' . $id));
        }

        flash('success', 'Invoice cancelled successfully');
        $this->redirect(base_url('billing/view/' . $id));
    }

    /**
     * Search services via AJAX
     */
    public function searchServices() {
        // Get search term - check both POST and GET
        $search = $_SERVER['REQUEST_METHOD'] === 'POST' ? input('term') : ($_GET['term'] ?? '');

        // Initialize service model
        $service_model = new ServiceModel();

        if (empty($search)) {
            // If no search term, return top services
            $services = $service_model->getTopUsedServices(10);
        } else {
            // Search services by term
            $services = $service_model->searchServices($search);
        }

        // Format services for response
        $formatted_services = [];
        foreach ($services as $service) {
            $formatted_services[] = [
                'id' => $service['id'],
                'text' => $service['name'] . ' - ' . $service['category_name'] . ' (' . format_currency($service['price']) . ')',
                'price' => $service['price']
            ];
        }

        // Set proper headers
        header('Content-Type: application/json');
        echo json_encode($formatted_services);
        exit;
    }

    /**
     * Search customers via AJAX
     */
    public function searchCustomers() {
        // Get search term
        $search = input('term');

        // Initialize customer model
        $customer_model = new CustomerModel();

        if (empty($search)) {
            // If no search term, return top customers
            $customers = $customer_model->getTopCustomers(10);
        } else {
            // Search customers by term
            $customers = $customer_model->searchCustomers($search);
        }

        // Format customers for response
        $formatted_customers = [];
        foreach ($customers as $customer) {
            $formatted_customers[] = [
                'id' => $customer['id'],
                'text' => $customer['name'] . ' (' . $customer['phone'] . ')',
                'phone' => $customer['phone'],
                'email' => $customer['email']
            ];
        }

        // Set proper headers
        header('Content-Type: application/json');
        echo json_encode($formatted_customers);
        exit;
    }

    /**
     * Search products via AJAX
     */
    public function searchProducts() {
        // Get search term
        $search = input('term');

        // Log the search request
        error_log("Product search request received. Term: " . ($search ?: 'empty'));

        // Initialize product model
        $product_model = new ProductModel();

        if (empty($search)) {
            // If no search term, return top products (limited to 10)
            $products = $product_model->getAllWithCategories(null, 'active', 10);
            error_log("Fetching top 10 active products");
        } else {
            // Search products by term
            $products = $product_model->search($search);
            error_log("Searching for products with term: " . $search);
        }

        // Log the number of products found
        error_log("Number of products found: " . count($products));

        // Check if products array is empty
        if (empty($products)) {
            // Try to get all products without filters as a fallback
            error_log("No products found with filters. Trying to get all products.");
            $products = $product_model->getAllWithCategories();
            error_log("Fallback: Number of products found: " . count($products));
        }

        // Format products for response
        $formatted_products = [];
        foreach ($products as $product) {
            // Check if selling_price exists, otherwise use cost_price
            $price = isset($product['selling_price']) ? $product['selling_price'] :
                   (isset($product['cost_price']) ? $product['cost_price'] : 0);

            $formatted_products[] = [
                'id' => $product['id'],
                'text' => $product['name'] . ' - ' . ($product['category_name'] ?? 'No Category') . ' (' . format_currency($price) . ') - Stock: ' . $product['quantity'],
                'price' => $price,
                'stock' => $product['quantity']
            ];
        }

        // Log the formatted products
        error_log("Formatted products: " . json_encode($formatted_products));

        // Set proper headers
        header('Content-Type: application/json');
        echo json_encode($formatted_products);
        exit;
    }

    /**
     * Get customer membership information via AJAX
     */
    public function getCustomerMembership() {
        // Set proper headers first to ensure no HTML is output before JSON
        header('Content-Type: application/json');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

        try {
            // Log that this method was called
            error_log("getCustomerMembership method called");

            // Get customer ID
            $customer_id = input('customer_id');
            error_log("Customer ID: " . $customer_id);

            if (empty($customer_id)) {
                // Return error if no customer ID provided
                echo json_encode([
                    'success' => false,
                    'message' => 'Customer ID is required'
                ]);
                exit;
            }

        // Initialize customer model
        $customer_model = new CustomerModel();

        // Get customer membership
        error_log("Attempting to get membership from CustomerModel->getMembership()");
        $membership = $customer_model->getMembership($customer_id);
        error_log("Membership data from CustomerModel: " . ($membership ? json_encode($membership) : "No membership found"));

        // Prepare response
        $response = [
            'success' => true,
            'has_membership' => false,
            'membership' => null
        ];

        if ($membership) {
            error_log("Processing membership data from CustomerModel");
            // Ensure discount values are numeric
            $service_discount = isset($membership['service_discount']) ? floatval($membership['service_discount']) : 0;
            $product_discount = isset($membership['product_discount']) ? floatval($membership['product_discount']) : 0;

            $response['has_membership'] = true;
            $response['membership'] = [
                'id' => $membership['id'],
                'name' => $membership['name'] ?? 'Membership',
                'service_discount' => $service_discount,
                'product_discount' => $product_discount,
                'end_date' => $membership['end_date'] ?? date('Y-m-d', strtotime('+1 year'))
            ];

            error_log("Formatted membership data: " . json_encode($response['membership']));
        } else {
            error_log("No membership found in CustomerModel, trying MembershipModel as fallback");
            // Try to get membership using MembershipModel as a fallback
            $membership_model = new MembershipModel();
            $membership = $membership_model->getActiveMembershipByCustomerId($customer_id);
            error_log("Membership data from MembershipModel: " . ($membership ? json_encode($membership) : "No membership found"));

            if ($membership) {
                error_log("Processing membership data from MembershipModel");
                // Ensure discount values are numeric
                $service_discount = isset($membership['service_discount']) ? floatval($membership['service_discount']) : 0;
                $product_discount = isset($membership['product_discount']) ? floatval($membership['product_discount']) : 0;

                $response['has_membership'] = true;
                $response['membership'] = [
                    'id' => $membership['id'],
                    'name' => $membership['membership_name'] ?? 'Membership',
                    'service_discount' => $service_discount,
                    'product_discount' => $product_discount,
                    'end_date' => $membership['end_date'] ?? date('Y-m-d', strtotime('+1 year'))
                ];

                error_log("Formatted membership data from fallback: " . json_encode($response['membership']));
            } else {
                error_log("No active membership found for customer ID: " . $customer_id);

                // Try a direct database query as a last resort
                try {
                    error_log("Trying direct database query as last resort");
                    $db = new Database();
                    $conn = $db->getConnection();

                    $query = "SELECT cm.*, mp.name, mp.service_discount, mp.product_discount
                             FROM customer_memberships cm
                             JOIN membership_plans mp ON cm.plan_id = mp.id
                             WHERE cm.customer_id = :customer_id
                             AND cm.status = 'active'
                             AND cm.end_date >= CURDATE()
                             ORDER BY cm.end_date DESC
                             LIMIT 1";

                    $stmt = $conn->prepare($query);
                    $stmt->bindParam(':customer_id', $customer_id);
                    $stmt->execute();

                    $direct_membership = $stmt->fetch(PDO::FETCH_ASSOC);
                    error_log("Direct query result: " . ($direct_membership ? json_encode($direct_membership) : "No membership found"));

                    if ($direct_membership) {
                        $service_discount = isset($direct_membership['service_discount']) ? floatval($direct_membership['service_discount']) : 0;
                        $product_discount = isset($direct_membership['product_discount']) ? floatval($direct_membership['product_discount']) : 0;

                        $response['has_membership'] = true;
                        $response['membership'] = [
                            'id' => $direct_membership['id'],
                            'name' => $direct_membership['name'] ?? 'Membership',
                            'service_discount' => $service_discount,
                            'product_discount' => $product_discount,
                            'end_date' => $direct_membership['end_date'] ?? date('Y-m-d', strtotime('+1 year'))
                        ];

                        error_log("Formatted membership data from direct query: " . json_encode($response['membership']));
                    }
                } catch (PDOException $e) {
                    error_log("Error in direct membership query: " . $e->getMessage());
                }
            }
        }

        // Prepare the JSON response
        $json_response = json_encode($response);
        error_log("Final response: " . $json_response);
        echo $json_response;
        exit;

        } catch (Exception $e) {
            // Handle any unexpected errors
            error_log("Unexpected error in getCustomerMembership: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'message' => 'An unexpected error occurred',
                'error' => $e->getMessage()
            ]);
            exit;
        }
    }

    /**
     * Record payment for an invoice
     */
    public function recordPayment($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('billing/view/' . $id));
        }

        // Get invoice
        $invoice_model = new InvoiceModel();
        $invoice = $invoice_model->find($id);

        if (!$invoice) {
            flash('error', 'Invoice not found');
            $this->redirect(base_url('billing'));
        }

        // Check if invoice is already paid
        if ($invoice['payment_status'] == 'paid') {
            flash('error', 'Invoice is already paid');
            $this->redirect(base_url('billing/view/' . $id));
        }

        // Check if invoice is cancelled
        if ($invoice['payment_status'] == 'cancelled') {
            flash('error', 'Cannot record payment for a cancelled invoice');
            $this->redirect(base_url('billing/view/' . $id));
        }

        // Get form data
        $payment_amount = input('payment_amount');
        $payment_method = input('payment_method');
        $payment_note = input('payment_note');

        // Validate form data
        if (empty($payment_amount) || empty($payment_method)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('billing/view/' . $id));
        }

        // Calculate new payment amount
        $new_payment_amount = $invoice['payment_amount'] + $payment_amount;

        // Determine payment status
        $payment_status = 'partial';
        if ($new_payment_amount >= $invoice['total_amount']) {
            $payment_status = 'paid';
            $new_payment_amount = $invoice['total_amount']; // Ensure payment doesn't exceed total
        }

        // Update invoice
        $result = $invoice_model->update($id, [
            'payment_amount' => $new_payment_amount,
            'payment_status' => $payment_status,
            'payment_method' => $payment_method
        ]);

        if (!$result) {
            flash('error', 'Failed to record payment');
            $this->redirect(base_url('billing/view/' . $id));
        }

        flash('success', 'Payment recorded successfully');
        $this->redirect(base_url('billing/view/' . $id));
    }

    /**
     * Delete invoice
     */
    public function delete($id) {
        // Get invoice
        $invoice_model = new InvoiceModel();
        $invoice = $invoice_model->find($id);

        if (!$invoice) {
            flash('error', 'Invoice not found');
            $this->redirect(base_url('billing'));
        }

        // Check if invoice is paid
        if ($invoice['payment_status'] == 'paid') {
            flash('error', 'Cannot delete a paid invoice');
            $this->redirect(base_url('billing/view/' . $id));
        }

        // Delete invoice services
        $invoice_service_model = new InvoiceServiceModel();
        $invoice_service_model->deleteByInvoiceId($id);

        // Delete invoice products
        $invoice_product_model = new InvoiceProductModel();
        $invoice_product_model->deleteByInvoiceId($id);

        // Delete invoice
        $result = $invoice_model->delete($id);

        if (!$result) {
            flash('error', 'Failed to delete invoice');
            $this->redirect(base_url('billing/view/' . $id));
        }

        flash('success', 'Invoice deleted successfully');
        $this->redirect(base_url('billing'));
    }
}

