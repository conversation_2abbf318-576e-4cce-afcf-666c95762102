<div class="container">
    <div class="row mb-5">
        <div class="col-md-12 text-center">
            <h1 class="display-4">Choose Date & Time</h1>
            <p class="lead">Select a convenient date and time for your appointment</p>
        </div>
    </div>
    
    <!-- Selected Service and Staff Info -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Booking Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Service</h5>
                            <p class="mb-1"><strong><?= $service['name'] ?></strong></p>
                            <p class="mb-1"><?= $service['description'] ?></p>
                            <p class="mb-1">
                                <span class="badge bg-secondary">
                                    <i class="fas fa-clock"></i> <?= $service['duration'] ?> mins
                                </span>
                            </p>
                            <p class="text-primary"><?= format_currency($service['price']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <h5>Staff</h5>
                            <p class="mb-1"><strong><?= $staff['name'] ?></strong></p>
                            <p class="mb-1"><?= $staff['position'] ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Date Selection -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Select Date</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($dates as $index => $date): ?>
                            <div class="col-md-3 col-6 mb-3">
                                <div class="card date-card <?= $index === 0 ? 'selected' : '' ?>" data-date="<?= $date['date'] ?>">
                                    <div class="card-body text-center">
                                        <h6 class="mb-1"><?= $date['day_name'] ?></h6>
                                        <h5 class="mb-0"><?= $date['formatted_date'] ?></h5>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Time Selection -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Select Time</h5>
                </div>
                <div class="card-body">
                    <div id="time_slots_container" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p>Loading available time slots...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Customer Information Form -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Your Information</h5>
                </div>
                <div class="card-body">
                    <form id="booking-form" action="<?= base_url('booking/confirm') ?>" method="post">
                        <input type="hidden" name="service_id" value="<?= $service['id'] ?>">
                        <input type="hidden" name="staff_id" value="<?= $staff['id'] ?>">
                        <input type="hidden" id="selected_date" name="date" value="<?= $dates[0]['date'] ?>">
                        <input type="hidden" id="selected_time" name="time" value="">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required 
                                       value="<?= isset($_SESSION['user_name']) ? $_SESSION['user_name'] : '' ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" required
                                       value="<?= isset($_SESSION['user_email']) ? $_SESSION['user_email'] : '' ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="notes" class="form-label">Special Requests</label>
                                <textarea class="form-control" id="notes" name="notes" rows="1"></textarea>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="confirm-btn" disabled>
                                <i class="fas fa-calendar-check"></i> Confirm Booking
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Navigation Buttons -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between">
                <a href="<?= base_url('booking/staff?service_id=' . $service['id']) ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Staff Selection
                </a>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Variables
    var selectedDate = document.getElementById('selected_date').value;
    var serviceId = <?= $service['id'] ?>;
    var staffId = <?= $staff['id'] ?>;
    
    // Load time slots for the first date
    loadTimeSlots(selectedDate, serviceId, staffId);
    
    // Date card selection
    var dateCards = document.querySelectorAll('.date-card');
    dateCards.forEach(function(card) {
        card.addEventListener('click', function() {
            // Remove selected class from all cards
            dateCards.forEach(function(c) {
                c.classList.remove('selected');
            });
            
            // Add selected class to clicked card
            this.classList.add('selected');
            
            // Get date
            var date = this.dataset.date;
            
            // Set hidden input value
            document.getElementById('selected_date').value = date;
            
            // Load time slots for selected date
            loadTimeSlots(date, serviceId, staffId);
        });
    });
    
    // Function to load time slots
    function loadTimeSlots(date, serviceId, staffId) {
        var timeSlotsContainer = document.getElementById('time_slots_container');
        
        // Show loading
        timeSlotsContainer.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p>Loading available time slots...</p></div>';
        
        // Generate time slots (in a real app, this would be an AJAX request)
        setTimeout(function() {
            var html = '<div class="row">';
            
            // Generate time slots from 9 AM to 6 PM with 30-minute intervals
            var startHour = 9;
            var endHour = 18;
            
            for (var hour = startHour; hour < endHour; hour++) {
                for (var minute = 0; minute < 60; minute += 30) {
                    var timeHour = hour;
                    var ampm = 'AM';
                    
                    if (timeHour >= 12) {
                        ampm = 'PM';
                        if (timeHour > 12) {
                            timeHour -= 12;
                        }
                    }
                    
                    var timeString = timeHour + ':' + (minute === 0 ? '00' : minute) + ' ' + ampm;
                    var timeValue = (hour < 10 ? '0' + hour : hour) + ':' + (minute === 0 ? '00' : minute) + ':00';
                    
                    // Randomly determine if slot is available (in a real app, this would come from the server)
                    var isAvailable = Math.random() > 0.3;
                    var disabledClass = isAvailable ? '' : 'disabled';
                    
                    html += '<div class="col-md-3 col-6 mb-3">';
                    html += '<div class="time-slot ' + disabledClass + '" data-time="' + timeValue + '">';
                    html += timeString;
                    html += '</div>';
                    html += '</div>';
                }
            }
            
            html += '</div>';
            
            timeSlotsContainer.innerHTML = html;
            
            // Time slot selection
            var timeSlots = document.querySelectorAll('.time-slot:not(.disabled)');
            timeSlots.forEach(function(slot) {
                slot.addEventListener('click', function() {
                    // Remove selected class from all slots
                    timeSlots.forEach(function(s) {
                        s.classList.remove('selected');
                    });
                    
                    // Add selected class to clicked slot
                    this.classList.add('selected');
                    
                    // Get time
                    var time = this.dataset.time;
                    
                    // Set hidden input value
                    document.getElementById('selected_time').value = time;
                    
                    // Enable confirm button
                    document.getElementById('confirm-btn').disabled = false;
                });
            });
        }, 1000);
    }
});
</script>

<style>
.date-card, .time-slot {
    cursor: pointer;
    transition: all 0.3s;
}

.date-card:hover, .time-slot:hover:not(.disabled) {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.date-card.selected {
    border: 2px solid #4e73df;
    background-color: #f8f9ff;
}

.time-slot {
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    text-align: center;
}

.time-slot.selected {
    background-color: #4e73df;
    color: white;
    border-color: #4e73df;
}

.time-slot.disabled {
    background-color: #f8f9fa;
    color: #adb5bd;
    cursor: not-allowed;
}
</style>
