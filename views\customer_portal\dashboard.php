<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-tachometer-alt"></i> Customer Dashboard</h1>
        <div>
            <a href="<?= base_url('profile') ?>" class="btn btn-outline-primary">
                <i class="fas fa-user-cog"></i> My Profile
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Customer Info Card -->
        <div class="col-md-4 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user-circle"></i> My Information</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <?php if (!empty($user['profile_picture'])): ?>
                            <img src="<?= base_url('uploads/profile/' . $user['profile_picture']) ?>" alt="Profile Picture" class="rounded-circle img-thumbnail" style="width: 120px; height: 120px; object-fit: cover;">
                        <?php else: ?>
                            <div class="avatar-circle bg-primary text-white mx-auto" style="width: 120px; height: 120px; font-size: 3rem;">
                                <?= strtoupper(substr($user['name'], 0, 1)) ?>
                            </div>
                        <?php endif; ?>
                        <h4 class="mt-3"><?= $user['name'] ?></h4>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex mb-2">
                            <div class="icon-circle bg-light text-primary me-3">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <div class="text-muted small">Email</div>
                                <div><?= $user['email'] ?></div>
                            </div>
                        </div>
                        <div class="d-flex mb-2">
                            <div class="icon-circle bg-light text-primary me-3">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div>
                                <div class="text-muted small">Phone</div>
                                <div><?= $customer['phone'] ?></div>
                            </div>
                        </div>
                        <?php if (!empty($customer['address'])): ?>
                        <div class="d-flex mb-2">
                            <div class="icon-circle bg-light text-primary me-3">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div>
                                <div class="text-muted small">Address</div>
                                <div><?= $customer['address'] ?></div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="text-center">
                        <a href="<?= base_url('profile') ?>" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="col-md-8 mb-4">
            <div class="row">
                <!-- Appointments Card -->
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-body text-center p-4">
                            <div class="icon-circle bg-primary text-white mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <h4>My Appointments</h4>
                            <p class="text-muted">View your upcoming and past appointments</p>
                            <a href="<?= base_url('customer/appointments') ?>" class="btn btn-primary">
                                <i class="fas fa-calendar-check"></i> View Appointments
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Invoices Card -->
                <div class="col-md-6 mb-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-body text-center p-4">
                            <div class="icon-circle bg-success text-white mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                                <i class="fas fa-file-invoice-dollar"></i>
                            </div>
                            <h4>My Invoices</h4>
                            <p class="text-muted">View your billing history and payment status</p>
                            <a href="<?= base_url('customer/invoices') ?>" class="btn btn-success">
                                <i class="fas fa-receipt"></i> View Invoices
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Membership Card -->
                <div class="col-md-12">
                    <div class="card shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="icon-circle bg-info text-white mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                                <i class="fas fa-id-card"></i>
                            </div>
                            <h4>My Membership</h4>
                            <p class="text-muted">View your membership details and benefits</p>
                            <a href="<?= base_url('customer/memberships') ?>" class="btn btn-info">
                                <i class="fas fa-crown"></i> View Membership
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <!-- Recent Appointments -->
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-calendar-check"></i> Recent Appointments</h5>
                    <a href="<?= base_url('customer/appointments') ?>" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($appointments)): ?>
                        <div class="p-4 text-center">
                            <img src="<?= base_url('assets/images/empty-calendar.svg') ?>" alt="No Appointments" style="width: 120px; opacity: 0.5;" class="mb-3">
                            <p class="text-muted mb-0">No appointments found.</p>
                            <a href="<?= base_url('booking') ?>" class="btn btn-sm btn-primary mt-3">
                                <i class="fas fa-plus"></i> Book an Appointment
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php 
                            $count = 0;
                            foreach ($appointments as $appointment): 
                                if ($count >= 3) break; // Show only 3 recent appointments
                                $count++;
                            ?>
                                <a href="<?= base_url('customer/view-appointment/' . $appointment['id']) ?>" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?= $appointment['service_name'] ?></h6>
                                        <small><?= format_date($appointment['appointment_date']) ?></small>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <small class="text-muted">
                                                <i class="fas fa-clock"></i> <?= format_time($appointment['start_time']) ?> - <?= format_time($appointment['end_time']) ?>
                                            </small>
                                            <br>
                                            <small class="text-muted">
                                                <i class="fas fa-user"></i> <?= $appointment['staff_name'] ?>
                                            </small>
                                        </div>
                                        <span class="badge <?= get_appointment_status_class($appointment['status']) ?>">
                                            <?= ucfirst($appointment['status']) ?>
                                        </span>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                        <?php if (count($appointments) > 3): ?>
                            <div class="card-footer text-center">
                                <a href="<?= base_url('customer/appointments') ?>" class="btn btn-sm btn-outline-primary">
                                    View All Appointments
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Invoices -->
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-file-invoice"></i> Recent Invoices</h5>
                    <a href="<?= base_url('customer/invoices') ?>" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($invoices)): ?>
                        <div class="p-4 text-center">
                            <img src="<?= base_url('assets/images/invoice.svg') ?>" alt="No Invoices" style="width: 120px; opacity: 0.5;" class="mb-3">
                            <p class="text-muted mb-0">No invoices found.</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php 
                            $count = 0;
                            foreach ($invoices as $invoice): 
                                if ($count >= 3) break; // Show only 3 recent invoices
                                $count++;
                            ?>
                                <a href="<?= base_url('customer/view-invoice/' . $invoice['id']) ?>" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">Invoice #<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?></h6>
                                        <small><?= format_date($invoice['invoice_date']) ?></small>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <small class="text-muted">
                                                <i class="fas fa-money-bill-wave"></i> <?= format_currency($invoice['total_amount']) ?>
                                            </small>
                                        </div>
                                        <span class="badge <?= get_payment_status_class($invoice['payment_status']) ?>">
                                            <?= ucfirst($invoice['payment_status']) ?>
                                        </span>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                        <?php if (count($invoices) > 3): ?>
                            <div class="card-footer text-center">
                                <a href="<?= base_url('customer/invoices') ?>" class="btn btn-sm btn-outline-primary">
                                    View All Invoices
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.icon-circle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}
</style>

<?php
// Helper functions
function get_appointment_status_class($status) {
    switch ($status) {
        case 'pending':
            return 'bg-warning';
        case 'confirmed':
            return 'bg-info';
        case 'completed':
            return 'bg-success';
        case 'cancelled':
            return 'bg-danger';
        default:
            return 'bg-secondary';
    }
}

function get_payment_status_class($status) {
    switch ($status) {
        case 'paid':
            return 'bg-success';
        case 'partial':
            return 'bg-info';
        case 'unpaid':
            return 'bg-warning';
        default:
            return 'bg-secondary';
    }
}
?>
