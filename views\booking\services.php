<div class="container">
    <div class="row mb-5">
        <div class="col-md-12 text-center">
            <h1 class="display-4">Choose a Service</h1>
            <p class="lead">Select the service you would like to book</p>
        </div>
    </div>

    <!-- Category Filter -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-body">
                    <h5 class="card-title mb-3">Filter by Category</h5>
                    <div class="category-scroll-container">
                        <div class="category-buttons d-flex gap-2">
                            <a href="<?= base_url('booking/services') ?>" class="btn <?= empty($selected_category) ? 'btn-primary' : 'btn-outline-primary' ?> flex-shrink-0">
                                All Categories
                            </a>
                            <?php foreach ($categories as $category): ?>
                                <a href="<?= base_url('booking/services?category_id=' . $category['id']) ?>"
                                   class="btn <?= $selected_category == $category['id'] ? 'btn-primary' : 'btn-outline-primary' ?> flex-shrink-0">
                                    <?= $category['name'] ?>
                                    <span class="badge bg-light text-dark ms-1"><?= $category['service_count'] ?></span>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Services List -->
    <div class="row">
        <?php if (empty($services)): ?>
            <div class="col-md-12">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No services found in this category. Please select another category.
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($services as $service): ?>
                <div class="col-md-4 mb-4">
                    <div class="card service-card h-100 shadow" data-service-id="<?= $service['id'] ?>">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><?= $service['name'] ?></h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text"><?= $service['description'] ?></p>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="badge bg-info">
                                    <i class="fas fa-tag"></i> <?= $service['category_name'] ?>
                                </span>
                                <span class="badge bg-secondary">
                                    <i class="fas fa-clock"></i> <?= $service['duration'] ?> mins
                                </span>
                            </div>
                            <h4 class="text-primary mb-0"><?= format_currency($service['price']) ?></h4>
                        </div>
                        <div class="card-footer">
                            <a href="<?= base_url('booking/staff?service_id=' . $service['id']) ?>" class="btn btn-primary w-100">
                                <i class="fas fa-arrow-right"></i> Select
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Navigation Buttons -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between">
                <a href="<?= base_url('booking') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for service selection -->
<form id="service-form" action="<?= base_url('booking/staff') ?>" method="get" class="d-none">
    <input type="hidden" id="selected_service" name="service_id" value="">
</form>

<style>
.category-scroll-container {
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: #dee2e6 transparent;
}

.category-scroll-container::-webkit-scrollbar {
    height: 6px;
}

.category-scroll-container::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 3px;
}

.category-scroll-container::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 3px;
}

.category-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

.category-buttons {
    min-width: max-content;
    padding-bottom: 5px;
}

.category-buttons .btn {
    white-space: nowrap;
    min-width: fit-content;
}

.category-buttons .badge {
    font-size: 0.7em;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Service card selection
    var serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(function(card) {
        card.addEventListener('click', function() {
            // Get service ID
            var serviceId = this.dataset.serviceId;

            // Set hidden input value
            document.getElementById('selected_service').value = serviceId;

            // Submit form
            document.getElementById('service-form').submit();
        });
    });
});
</script>
