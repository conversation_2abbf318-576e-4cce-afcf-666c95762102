<?php
/**
 * Contact Model
 */
class ContactModel extends Model {
    protected $table = 'contact_messages';
    
    /**
     * Get all contact messages
     * 
     * @param string $status Filter by status (optional)
     * @return array Contact messages
     */
    public function getAllMessages($status = null) {
        $query = "SELECT * FROM " . $this->table;
        
        if ($status) {
            $query .= " WHERE status = :status";
        }
        
        $query .= " ORDER BY created_at DESC";
        
        $stmt = $this->db->prepare($query);
        
        if ($status) {
            $stmt->bindParam(':status', $status);
        }
        
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Mark message as read
     * 
     * @param int $id Message ID
     * @return bool Success or failure
     */
    public function markAsRead($id) {
        $query = "UPDATE " . $this->table . " SET status = 'read' WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        
        return $stmt->execute();
    }
    
    /**
     * Get unread message count
     * 
     * @return int Unread message count
     */
    public function getUnreadCount() {
        $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE status = 'unread'";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchColumn();
    }
}
