<?php
/**
 * Wallet Model
 */
class WalletModel extends Model {
    protected $table = 'customer_wallets';

    /**
     * Get wallet by customer ID
     *
     * @param int $customer_id Customer ID
     * @return array|false Wallet data or false if not found
     */
    public function getByCustomerId($customer_id) {
        $query = "SELECT * FROM " . $this->table . " WHERE customer_id = :customer_id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':customer_id', $customer_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Create wallet for customer if it doesn't exist
     *
     * @param int $customer_id Customer ID
     * @return bool Success or failure
     */
    public function createWalletIfNotExists($customer_id) {
        // Check if wallet already exists
        $existing = $this->getByCustomerId($customer_id);
        if ($existing) {
            return true;
        }

        // Create new wallet
        $query = "INSERT INTO " . $this->table . " (customer_id, balance) VALUES (:customer_id, 0.00)";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':customer_id', $customer_id);
        
        return $stmt->execute();
    }

    /**
     * Add money to customer wallet
     *
     * @param int $customer_id Customer ID
     * @param float $amount Amount to add
     * @param string $description Transaction description
     * @param int $created_by User ID who performed the transaction
     * @param string $reference_type Type of reference (manual_add, adjustment, etc.)
     * @param int $reference_id Reference ID (optional)
     * @return bool Success or failure
     */
    public function addMoney($customer_id, $amount, $description, $created_by, $reference_type = 'manual_add', $reference_id = null) {
        try {
            $this->db->beginTransaction();

            // Create wallet if it doesn't exist
            $this->createWalletIfNotExists($customer_id);

            // Get current balance
            $wallet = $this->getByCustomerId($customer_id);
            $balance_before = $wallet ? $wallet['balance'] : 0;
            $balance_after = $balance_before + $amount;

            // Update wallet balance
            $query = "UPDATE " . $this->table . " SET balance = :balance WHERE customer_id = :customer_id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':balance', $balance_after);
            $stmt->bindParam(':customer_id', $customer_id);
            
            if (!$stmt->execute()) {
                $this->db->rollBack();
                return false;
            }

            // Record transaction
            $transaction_query = "INSERT INTO wallet_transactions 
                                (customer_id, transaction_type, amount, balance_before, balance_after, 
                                 reference_type, reference_id, description, created_by) 
                                VALUES 
                                (:customer_id, 'credit', :amount, :balance_before, :balance_after, 
                                 :reference_type, :reference_id, :description, :created_by)";
            
            $transaction_stmt = $this->db->prepare($transaction_query);
            $transaction_stmt->bindParam(':customer_id', $customer_id);
            $transaction_stmt->bindParam(':amount', $amount);
            $transaction_stmt->bindParam(':balance_before', $balance_before);
            $transaction_stmt->bindParam(':balance_after', $balance_after);
            $transaction_stmt->bindParam(':reference_type', $reference_type);
            $transaction_stmt->bindParam(':reference_id', $reference_id);
            $transaction_stmt->bindParam(':description', $description);
            $transaction_stmt->bindParam(':created_by', $created_by);

            if (!$transaction_stmt->execute()) {
                $this->db->rollBack();
                return false;
            }

            $this->db->commit();
            return true;

        } catch (PDOException $e) {
            $this->db->rollBack();
            error_log("Wallet addMoney error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Deduct money from customer wallet
     *
     * @param int $customer_id Customer ID
     * @param float $amount Amount to deduct
     * @param string $description Transaction description
     * @param int $created_by User ID who performed the transaction
     * @param string $reference_type Type of reference (invoice_payment, adjustment, etc.)
     * @param int $reference_id Reference ID (optional)
     * @return bool Success or failure
     */
    public function deductMoney($customer_id, $amount, $description, $created_by, $reference_type = 'invoice_payment', $reference_id = null) {
        try {
            $this->db->beginTransaction();

            // Get current balance
            $wallet = $this->getByCustomerId($customer_id);
            if (!$wallet) {
                $this->db->rollBack();
                return false;
            }

            $balance_before = $wallet['balance'];
            
            // Check if sufficient balance
            if ($balance_before < $amount) {
                $this->db->rollBack();
                return false;
            }

            $balance_after = $balance_before - $amount;

            // Update wallet balance
            $query = "UPDATE " . $this->table . " SET balance = :balance WHERE customer_id = :customer_id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':balance', $balance_after);
            $stmt->bindParam(':customer_id', $customer_id);
            
            if (!$stmt->execute()) {
                $this->db->rollBack();
                return false;
            }

            // Record transaction
            $transaction_query = "INSERT INTO wallet_transactions 
                                (customer_id, transaction_type, amount, balance_before, balance_after, 
                                 reference_type, reference_id, description, created_by) 
                                VALUES 
                                (:customer_id, 'debit', :amount, :balance_before, :balance_after, 
                                 :reference_type, :reference_id, :description, :created_by)";
            
            $transaction_stmt = $this->db->prepare($transaction_query);
            $transaction_stmt->bindParam(':customer_id', $customer_id);
            $transaction_stmt->bindParam(':amount', $amount);
            $transaction_stmt->bindParam(':balance_before', $balance_before);
            $transaction_stmt->bindParam(':balance_after', $balance_after);
            $transaction_stmt->bindParam(':reference_type', $reference_type);
            $transaction_stmt->bindParam(':reference_id', $reference_id);
            $transaction_stmt->bindParam(':description', $description);
            $transaction_stmt->bindParam(':created_by', $created_by);

            if (!$transaction_stmt->execute()) {
                $this->db->rollBack();
                return false;
            }

            $this->db->commit();
            return true;

        } catch (PDOException $e) {
            $this->db->rollBack();
            error_log("Wallet deductMoney error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all wallets with customer details
     *
     * @param string $search Search term (optional)
     * @param float $min_balance Minimum balance filter (optional)
     * @param float $max_balance Maximum balance filter (optional)
     * @return array Wallets with customer details
     */
    public function getAllWithCustomerDetails($search = null, $min_balance = null, $max_balance = null) {
        $query = "SELECT w.*, u.name as customer_name, u.email as customer_email, c.phone as customer_phone
                 FROM " . $this->table . " w
                 JOIN customers c ON w.customer_id = c.id
                 JOIN users u ON c.user_id = u.id
                 WHERE 1=1";

        $params = [];

        if ($search) {
            $query .= " AND (u.name LIKE :search OR u.email LIKE :search OR c.phone LIKE :search)";
            $params[':search'] = '%' . $search . '%';
        }

        if ($min_balance !== null && $min_balance !== '') {
            $query .= " AND w.balance >= :min_balance";
            $params[':min_balance'] = $min_balance;
        }

        if ($max_balance !== null && $max_balance !== '') {
            $query .= " AND w.balance <= :max_balance";
            $params[':max_balance'] = $max_balance;
        }

        $query .= " ORDER BY w.balance DESC, u.name ASC";

        $stmt = $this->db->prepare($query);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get transaction history for a customer
     *
     * @param int $customer_id Customer ID
     * @param string $date_from Date from filter (optional)
     * @param string $date_to Date to filter (optional)
     * @param string $transaction_type Transaction type filter (optional)
     * @return array Transaction history
     */
    public function getTransactionHistory($customer_id, $date_from = null, $date_to = null, $transaction_type = null) {
        $query = "SELECT wt.*, u.name as created_by_name
                 FROM wallet_transactions wt
                 LEFT JOIN users u ON wt.created_by = u.id
                 WHERE wt.customer_id = :customer_id";

        $params = [':customer_id' => $customer_id];

        if ($date_from) {
            $query .= " AND DATE(wt.created_at) >= :date_from";
            $params[':date_from'] = $date_from;
        }

        if ($date_to) {
            $query .= " AND DATE(wt.created_at) <= :date_to";
            $params[':date_to'] = $date_to;
        }

        if ($transaction_type) {
            $query .= " AND wt.transaction_type = :transaction_type";
            $params[':transaction_type'] = $transaction_type;
        }

        $query .= " ORDER BY wt.created_at DESC";

        $stmt = $this->db->prepare($query);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get wallet statistics
     *
     * @return array Statistics
     */
    public function getStatistics() {
        $query = "SELECT
                    COUNT(*) as total_wallets,
                    COALESCE(SUM(balance), 0) as total_balance,
                    COALESCE(AVG(balance), 0) as average_balance,
                    COUNT(CASE WHEN balance > 0 THEN 1 END) as wallets_with_balance,
                    COALESCE(MAX(balance), 0) as highest_balance
                 FROM " . $this->table;

        $stmt = $this->db->prepare($query);
        $stmt->execute();

        $stats = $stmt->fetch(PDO::FETCH_ASSOC);

        // Ensure all values are properly formatted
        return [
            'total_wallets' => (int) $stats['total_wallets'],
            'total_balance' => (float) $stats['total_balance'],
            'average_balance' => (float) $stats['average_balance'],
            'wallets_with_balance' => (int) $stats['wallets_with_balance'],
            'highest_balance' => (float) $stats['highest_balance']
        ];
    }

    /**
     * Check if customer has sufficient balance
     *
     * @param int $customer_id Customer ID
     * @param float $amount Amount to check
     * @return bool True if sufficient balance, false otherwise
     */
    public function hasSufficientBalance($customer_id, $amount) {
        $wallet = $this->getByCustomerId($customer_id);
        return $wallet && $wallet['balance'] >= $amount;
    }
}
