<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-wallet"></i> Customer Wallets</h1>
        <a href="<?= base_url('wallet/add-money') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Money
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Wallets</h6>
                            <h3 class="mb-0"><?= $stats['total_wallets'] ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-wallet fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Balance</h6>
                            <h3 class="mb-0"><?= format_currency($stats['total_balance']) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Active Wallets</h6>
                            <h3 class="mb-0"><?= $stats['wallets_with_balance'] ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Highest Balance</h6>
                            <h3 class="mb-0"><?= format_currency($stats['highest_balance']) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-crown fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="<?= base_url('wallet') ?>">
                <div class="row">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search Customer</label>
                        <input type="text" class="form-control" id="search" name="search" value="<?= $search ?>" placeholder="Name, email, or phone">
                    </div>
                    <div class="col-md-3">
                        <label for="min_balance" class="form-label">Min Balance</label>
                        <input type="number" class="form-control" id="min_balance" name="min_balance" value="<?= $min_balance ?>" step="0.01" min="0">
                    </div>
                    <div class="col-md-3">
                        <label for="max_balance" class="form-label">Max Balance</label>
                        <input type="number" class="form-control" id="max_balance" name="max_balance" value="<?= $max_balance ?>" step="0.01" min="0">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="<?= base_url('wallet') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Wallets List -->
    <div class="card shadow-sm">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Customer Wallets</h5>
            <span class="badge bg-primary"><?= count($wallets) ?> wallets</span>
        </div>
        <div class="card-body">
            <?php if (empty($wallets)): ?>
                <div class="text-center py-5">
                    <img src="<?= base_url('assets/images/empty-wallet.svg') ?>" alt="No Wallets" style="width: 120px; opacity: 0.5;" class="mb-3">
                    <h5>No Wallets Found</h5>
                    <p class="text-muted">No customer wallets match your search criteria.</p>
                    <a href="<?= base_url('wallet/add-money') ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Money to Wallet
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th>Customer</th>
                                <th>Contact</th>
                                <th>Balance</th>
                                <th>Last Updated</th>
                                <th width="20%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($wallets as $index => $wallet): ?>
                                <tr>
                                    <td><?= $index + 1 ?></td>
                                    <td>
                                        <div class="fw-bold"><?= $wallet['customer_name'] ?></div>
                                        <div class="small text-muted"><?= $wallet['customer_email'] ?></div>
                                    </td>
                                    <td><?= $wallet['customer_phone'] ?></td>
                                    <td>
                                        <span class="badge <?= $wallet['balance'] > 0 ? 'bg-success' : 'bg-secondary' ?> fs-6">
                                            <?= format_currency($wallet['balance']) ?>
                                        </span>
                                    </td>
                                    <td><?= format_datetime($wallet['updated_at']) ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= base_url('wallet/view/' . $wallet['customer_id']) ?>" class="btn btn-outline-primary" title="View Wallet">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('wallet/add-money/' . $wallet['customer_id']) ?>" class="btn btn-outline-success" title="Add Money">
                                                <i class="fas fa-plus"></i>
                                            </a>
                                            <a href="<?= base_url('wallet/adjust-balance/' . $wallet['customer_id']) ?>" class="btn btn-outline-warning" title="Adjust Balance">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?= base_url('wallet/transactions/' . $wallet['customer_id']) ?>" class="btn btn-outline-info" title="Transaction History">
                                                <i class="fas fa-history"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.btn-group .btn {
    transition: all 0.2s ease;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
}

.badge.fs-6 {
    font-size: 0.9rem !important;
}
</style>
