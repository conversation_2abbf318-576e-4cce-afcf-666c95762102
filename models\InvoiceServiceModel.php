<?php
/**
 * Invoice Service Model
 */
class InvoiceServiceModel extends Model {
    protected $table = 'invoice_services';
    
    /**
     * Delete by invoice ID
     * 
     * @param int $invoice_id Invoice ID
     * @return bool Success or failure
     */
    public function deleteByInvoiceId($invoice_id) {
        $query = "DELETE FROM " . $this->table . " WHERE invoice_id = :invoice_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':invoice_id', $invoice_id);
        
        return $stmt->execute();
    }
}
