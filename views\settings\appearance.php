<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-cogs"></i> Settings</h1>
    </div>

    <div class="row">
        <!-- Settings Navigation -->
        <div class="col-md-3 mb-4">
            <?php include 'partials/settings_nav.php'; ?>
        </div>

        <!-- Settings Content -->
        <div class="col-md-9">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Appearance Settings</h5>
                </div>
                <div class="card-body">
                    <!-- Tabs for different appearance settings -->
                    <ul class="nav nav-tabs mb-4" id="appearanceTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general-content" type="button" role="tab" aria-controls="general-content" aria-selected="true">General</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="hero-slider-tab" data-bs-toggle="tab" data-bs-target="#hero-slider-content" type="button" role="tab" aria-controls="hero-slider-content" aria-selected="false">Hero Slider</button>
                        </li>
                    </ul>

                    <div class="tab-content" id="appearanceTabsContent">
                        <!-- General Appearance Settings -->
                        <div class="tab-pane fade show active" id="general-content" role="tabpanel" aria-labelledby="general-tab">
                            <form action="<?= base_url('settings/update-appearance') ?>" method="post" enctype="multipart/form-data">
                                <div class="row">
                                    <!-- Theme Color -->
                                    <div class="col-md-6 mb-4">
                                        <label for="theme_color" class="form-label">Theme Color</label>
                                        <input type="color" class="form-control form-control-color w-100" id="theme_color" name="theme_color" value="<?= $settings['theme_color'] ?? '#3498db' ?>">
                                        <div class="form-text">Select a primary color for your salon theme</div>
                                    </div>

                            <!-- Logo Preview -->
                            <div class="col-md-6 mb-4">
                                <label class="form-label">Current Logo</label>
                                <div class="border rounded p-3 text-center">
                                    <?php if (!empty($settings['logo'])): ?>
                                        <img src="<?= base_url('uploads/' . $settings['logo']) ?>" alt="Logo" class="img-fluid" style="max-height: 100px;">
                                    <?php else: ?>
                                        <div class="text-muted">No logo uploaded</div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Logo Upload -->
                            <div class="col-md-6 mb-4">
                                <label for="logo" class="form-label">Upload Logo</label>
                                <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                <div class="form-text">Recommended size: 200x80 pixels</div>
                            </div>

                            <!-- Favicon Preview -->
                            <div class="col-md-6 mb-4">
                                <label class="form-label">Current Favicon</label>
                                <div class="border rounded p-3 text-center">
                                    <?php if (!empty($settings['favicon'])): ?>
                                        <img src="<?= base_url('uploads/' . $settings['favicon']) ?>" alt="Favicon" class="img-fluid" style="max-height: 32px;">
                                    <?php else: ?>
                                        <div class="text-muted">No favicon uploaded</div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Favicon Upload -->
                            <div class="col-md-6 mb-4">
                                <label for="favicon" class="form-label">Upload Favicon</label>
                                <input type="file" class="form-control" id="favicon" name="favicon" accept="image/x-icon,image/png">
                                <div class="form-text">Recommended size: 32x32 pixels</div>
                            </div>

                                    <!-- Submit Button -->
                                    <div class="col-md-12 mt-3 text-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i> Save Changes
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Hero Slider Settings -->
                        <div class="tab-pane fade" id="hero-slider-content" role="tabpanel" aria-labelledby="hero-slider-tab">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h5 class="mb-0">Hero Sliders</h5>
                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addSliderModal">
                                    <i class="fas fa-plus me-1"></i> Add New Slider
                                </button>
                            </div>

                            <!-- Sliders List -->
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="80">Image</th>
                                            <th>Title</th>
                                            <th>Subtitle</th>
                                            <th width="100">Status</th>
                                            <th width="120">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="sliders-list">
                                        <?php if (empty($sliders)): ?>
                                            <tr>
                                                <td colspan="5" class="text-center py-3">No sliders found. Add your first slider!</td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($sliders as $slider): ?>
                                                <tr data-id="<?= $slider['id'] ?>">
                                                    <td>
                                                        <img src="<?= base_url($slider['image_path']) ?>" alt="<?= $slider['title'] ?>" class="img-thumbnail" style="width: 60px; height: 40px; object-fit: cover;">
                                                    </td>
                                                    <td><?= $slider['title'] ?></td>
                                                    <td><?= $slider['subtitle'] ?></td>
                                                    <td>
                                                        <span class="badge bg-<?= $slider['status'] == 'active' ? 'success' : 'secondary' ?>">
                                                            <?= ucfirst($slider['status']) ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="<?= base_url('hero-slider/edit/' . $slider['id']) ?>" class="btn btn-outline-primary">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-outline-danger delete-slider" data-id="<?= $slider['id'] ?>">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>

                            <div class="alert alert-info mt-3">
                                <i class="fas fa-info-circle me-2"></i> Hero sliders will be displayed on your homepage. You can add multiple sliders and arrange them by dragging and dropping.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Slider Modal -->
<div class="modal fade" id="addSliderModal" tabindex="-1" aria-labelledby="addSliderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addSliderModalLabel">Add New Slider</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= base_url('hero-slider/add') ?>" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="title" class="form-label">Title</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="subtitle" class="form-label">Subtitle</label>
                            <input type="text" class="form-control" id="subtitle" name="subtitle">
                        </div>
                        <div class="col-md-12 mb-3">
                            <label for="image" class="form-label">Slider Image</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*" required>
                            <div class="form-text">Recommended size: 1920x600 pixels</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="button_text" class="form-label">Button Text</label>
                            <input type="text" class="form-control" id="button_text" name="button_text">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="button_link" class="form-label">Button Link</label>
                            <input type="text" class="form-control" id="button_link" name="button_link">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Slider</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Preview uploaded logo
    document.getElementById('logo').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.querySelector('.col-md-6:nth-child(2) .border');
                preview.innerHTML = `<img src="${e.target.result}" alt="Logo Preview" class="img-fluid" style="max-height: 100px;">`;
            }
            reader.readAsDataURL(file);
        }
    });

    // Preview uploaded favicon
    document.getElementById('favicon').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.querySelector('.col-md-6:nth-child(4) .border');
                preview.innerHTML = `<img src="${e.target.result}" alt="Favicon Preview" class="img-fluid" style="max-height: 32px;">`;
            }
            reader.readAsDataURL(file);
        }
    });

    // Preview slider image in modal
    document.getElementById('image')?.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // Create preview if it doesn't exist
                let previewContainer = document.getElementById('slider-image-preview');
                if (!previewContainer) {
                    previewContainer = document.createElement('div');
                    previewContainer.id = 'slider-image-preview';
                    previewContainer.className = 'mt-3 border rounded p-2 text-center';
                    document.querySelector('.col-md-12.mb-3').appendChild(previewContainer);
                }

                previewContainer.innerHTML = `<img src="${e.target.result}" alt="Slider Preview" class="img-fluid" style="max-height: 200px;">`;
            }
            reader.readAsDataURL(file);
        }
    });

    // Delete slider confirmation
    const deleteButtons = document.querySelectorAll('.delete-slider');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const sliderId = this.getAttribute('data-id');
            if (confirm('Are you sure you want to delete this slider?')) {
                window.location.href = `<?= base_url('hero-slider/delete/') ?>${sliderId}`;
            }
        });
    });

    // Make sliders sortable
    if (typeof Sortable !== 'undefined' && document.getElementById('sliders-list')) {
        new Sortable(document.getElementById('sliders-list'), {
            animation: 150,
            ghostClass: 'bg-light',
            onEnd: function() {
                // Get the new order
                const rows = document.querySelectorAll('#sliders-list tr[data-id]');
                const orderData = {};

                rows.forEach((row, index) => {
                    const id = row.getAttribute('data-id');
                    orderData[id] = index + 1;
                });

                // Send the new order to the server
                fetch('<?= base_url('hero-slider/update-order') ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `order=${JSON.stringify(orderData)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        const alertDiv = document.createElement('div');
                        alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
                        alertDiv.innerHTML = `
                            <i class="fas fa-check-circle me-2"></i> Slider order updated successfully
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;
                        document.querySelector('.table-responsive').after(alertDiv);

                        // Auto dismiss after 3 seconds
                        setTimeout(() => {
                            alertDiv.remove();
                        }, 3000);
                    }
                })
                .catch(error => {
                    console.error('Error updating slider order:', error);
                });
            }
        });
    }
});
</script>
