<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-plus-circle"></i> Add Money to Wallet</h1>
        <div>
            <a href="<?= base_url('wallet') ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Wallets
            </a>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('wallet/store-add-money') ?>" method="post" id="add-money-form">
                <div class="row">
                    <!-- Customer Selection -->
                    <div class="col-md-6 mb-3">
                        <label for="customer_id" class="form-label">Customer <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <select class="form-select" id="customer_id" name="customer_id" required <?= $selected_customer ? 'disabled' : '' ?>>
                                <option value="">Select a customer...</option>
                                <?php foreach ($customers as $customer): ?>
                                    <option value="<?= $customer['id'] ?>" 
                                            <?= $selected_customer && $selected_customer['id'] == $customer['id'] ? 'selected' : '' ?>>
                                        <?= $customer['name'] ?> - <?= $customer['phone'] ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if ($selected_customer): ?>
                                <input type="hidden" name="customer_id" value="<?= $selected_customer['id'] ?>">
                            <?php endif; ?>
                        </div>
                        <?php if ($selected_customer): ?>
                            <div class="form-text text-info">
                                <i class="fas fa-info-circle"></i> Selected: <?= $selected_customer['name'] ?> (<?= $selected_customer['email'] ?>)
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Amount -->
                    <div class="col-md-6 mb-3">
                        <label for="amount" class="form-label">Amount <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-rupee-sign"></i></span>
                            <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0.01" required placeholder="0.00">
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="col-md-12 mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3" placeholder="Optional description for this transaction"></textarea>
                    </div>

                    <!-- Current Wallet Balance (if customer selected) -->
                    <div class="col-md-12 mb-3" id="current-balance-section" style="display: none;">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-wallet"></i> Current Wallet Balance</h6>
                            <h4 class="mb-0" id="current-balance">₹0.00</h4>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-md-12">
                        <div class="d-flex justify-content-end">
                            <a href="<?= base_url('wallet') ?>" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus"></i> Add Money
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Quick Amount Buttons -->
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Amount</h5>
        </div>
        <div class="card-body">
            <p class="text-muted mb-3">Click on any amount below to quickly set the amount field:</p>
            <div class="row">
                <div class="col-md-2 col-6 mb-2">
                    <button type="button" class="btn btn-outline-primary w-100 quick-amount" data-amount="100">₹100</button>
                </div>
                <div class="col-md-2 col-6 mb-2">
                    <button type="button" class="btn btn-outline-primary w-100 quick-amount" data-amount="250">₹250</button>
                </div>
                <div class="col-md-2 col-6 mb-2">
                    <button type="button" class="btn btn-outline-primary w-100 quick-amount" data-amount="500">₹500</button>
                </div>
                <div class="col-md-2 col-6 mb-2">
                    <button type="button" class="btn btn-outline-primary w-100 quick-amount" data-amount="1000">₹1,000</button>
                </div>
                <div class="col-md-2 col-6 mb-2">
                    <button type="button" class="btn btn-outline-primary w-100 quick-amount" data-amount="2000">₹2,000</button>
                </div>
                <div class="col-md-2 col-6 mb-2">
                    <button type="button" class="btn btn-outline-primary w-100 quick-amount" data-amount="5000">₹5,000</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const customerSelect = document.getElementById('customer_id');
    const currentBalanceSection = document.getElementById('current-balance-section');
    const currentBalanceDisplay = document.getElementById('current-balance');
    const quickAmountButtons = document.querySelectorAll('.quick-amount');
    const amountInput = document.getElementById('amount');

    // Handle customer selection change
    customerSelect.addEventListener('change', function() {
        const customerId = this.value;
        if (customerId) {
            // Fetch current wallet balance
            fetch(`<?= base_url('wallet/get-balance/') ?>${customerId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentBalanceDisplay.textContent = data.formatted_balance;
                        currentBalanceSection.style.display = 'block';
                    } else {
                        currentBalanceSection.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error fetching balance:', error);
                    currentBalanceSection.style.display = 'none';
                });
        } else {
            currentBalanceSection.style.display = 'none';
        }
    });

    // Handle quick amount buttons
    quickAmountButtons.forEach(button => {
        button.addEventListener('click', function() {
            const amount = this.getAttribute('data-amount');
            amountInput.value = amount;
            amountInput.focus();
        });
    });

    // If customer is pre-selected, load their balance
    if (customerSelect.value) {
        customerSelect.dispatchEvent(new Event('change'));
    }

    // Form validation
    document.getElementById('add-money-form').addEventListener('submit', function(e) {
        const customerId = document.getElementById('customer_id').value;
        const amount = document.getElementById('amount').value;

        if (!customerId) {
            alert('Please select a customer');
            e.preventDefault();
            return false;
        }

        if (!amount || parseFloat(amount) <= 0) {
            alert('Please enter a valid amount');
            e.preventDefault();
            return false;
        }

        return true;
    });
});
</script>

<style>
.quick-amount {
    transition: all 0.2s ease;
}

.quick-amount:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.form-control:focus, .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
</style>
