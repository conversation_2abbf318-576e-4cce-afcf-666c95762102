
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title><?= isset($page_title) ? $page_title . ' - ' . APP_NAME : APP_NAME ?></title>

    <!-- Open Graph Meta Tags for WhatsApp and Social Media -->
    <meta property="og:title" content="<?= isset($page_title) ? $page_title . ' - ' . get_salon_name() : get_salon_name() ?>">
    <meta property="og:description" content="<?= isset($page_description) ? $page_description : 'Professional salon services including hair care, skin care, nail care, makeup, and spa treatments. Book your appointment today!' ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= current_url() ?>">
    <meta property="og:site_name" content="<?= get_salon_name() ?>">
    <?php if (!empty(get_setting('logo_path'))): ?>
    <meta property="og:image" content="<?= base_url(get_setting('logo_path')) ?>">
    <meta property="og:image:alt" content="<?= get_salon_name() ?> Logo">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <?php else: ?>
    <meta property="og:image" content="<?= base_url('assets/images/salon.jpg') ?>">
    <meta property="og:image:alt" content="<?= get_salon_name() ?> - Professional Salon Services">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <?php endif; ?>

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?= isset($page_title) ? $page_title . ' - ' . get_salon_name() : get_salon_name() ?>">
    <meta name="twitter:description" content="<?= isset($page_description) ? $page_description : 'Professional salon services including hair care, skin care, nail care, makeup, and spa treatments. Book your appointment today!' ?>">
    <?php if (!empty(get_setting('logo_path'))): ?>
    <meta name="twitter:image" content="<?= base_url(get_setting('logo_path')) ?>">
    <?php else: ?>
    <meta name="twitter:image" content="<?= base_url('assets/images/salon.jpg') ?>">
    <?php endif; ?>

    <!-- Additional Meta Tags -->
    <meta name="description" content="<?= isset($page_description) ? $page_description : 'Professional salon services including hair care, skin care, nail care, makeup, and spa treatments. Book your appointment today!' ?>">
    <meta name="keywords" content="salon, beauty, hair care, skin care, nail care, makeup, spa, <?= get_salon_name() ?>">
    <meta name="author" content="<?= get_salon_name() ?>">

    <!-- Favicon -->
    <?php if (!empty(get_setting('favicon_path'))): ?>
    <link rel="icon" type="image/x-icon" href="<?= base_url(get_setting('favicon_path')) ?>">
    <?php elseif (!empty(get_setting('logo_path'))): ?>
    <link rel="icon" type="image/png" href="<?= base_url(get_setting('logo_path')) ?>">
    <?php endif; ?>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=JetBrains+Mono&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Modern CSS -->
    <link rel="stylesheet" href="<?= base_url('assets/css/modern-style.css') ?>">
    <link rel="stylesheet" href="<?= base_url('assets/css/sidebar-fixes.css') ?>">
    <link rel="stylesheet" href="<?= base_url('assets/css/avatar-fixes.css') ?>">
    <link rel="stylesheet" href="<?= base_url('assets/css/hero-slider.css') ?>">
    <link rel="stylesheet" href="<?= base_url('assets/css/mobile-fixes.css') ?>">
    <link rel="stylesheet" href="<?= base_url('assets/css/select2-mobile-fixes.css') ?>">
    <!-- Additional CSS -->
    <?php if (isset($styles)): ?>
        <?= $styles ?>
    <?php endif; ?>
</head>
<body>
    <?php if (is_logged_in() && current_user_role() != 'customer'): ?>
        <!-- Admin/Staff Layout with Sidebar -->
        <div class="admin-layout">
            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Sidebar Header -->
                <div class="sidebar-header">
                    <a href="<?= base_url('dashboard') ?>" class="sidebar-brand">
                        <?php if (!empty(get_setting('logo_path'))): ?>
                            <img src="<?= base_url(get_setting('logo_path')) ?>" alt="<?= get_salon_name() ?>" style="max-height: 40px;">
                        <?php else: ?>
                            <i class="fas fa-spa"></i>
                            <span><?= get_salon_name() ?></span>
                        <?php endif; ?>
                    </a>
                    <button id="sidebar-toggle" class="sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>

                <!-- Sidebar User Info -->
                <div class="sidebar-user">
                    <?php if (!empty($_SESSION['profile_picture'])): ?>
                        <img src="<?= base_url('uploads/profile/' . $_SESSION['profile_picture']) ?>" alt="Profile" class="avatar-img">
                    <?php else: ?>
                        <div class="avatar-circle">
                            <?= strtoupper(substr($_SESSION['user_name'] ?? 'U', 0, 1)) ?>
                        </div>
                    <?php endif; ?>
                    <div class="sidebar-user-info">
                        <span class="sidebar-user-name"><?= $_SESSION['user_name'] ?? 'User' ?></span>
                        <span class="sidebar-user-role"><?= ucfirst(current_user_role()) ?></span>
                    </div>
                </div>

                <!-- Sidebar Menu -->
                <ul class="sidebar-nav">
                    <li class="sidebar-item">
                        <a class="sidebar-link" href="<?= base_url('dashboard') ?>">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>

                    <li class="sidebar-item">
                        <a class="sidebar-link" href="<?= base_url('appointments') ?>">
                            <i class="fas fa-calendar-alt"></i>
                            <span>Appointments</span>
                        </a>
                    </li>

                    <li class="sidebar-item">
                        <a class="sidebar-link" href="<?= base_url('customers') ?>">
                            <i class="fas fa-users"></i>
                            <span>Customers</span>
                        </a>
                    </li>

                    <li class="sidebar-item">
                        <a class="sidebar-link" href="<?= base_url('services') ?>">
                            <i class="fas fa-cut"></i>
                            <span>Services</span>
                        </a>
                    </li>

                    <li class="sidebar-item">
                        <a class="sidebar-link" href="<?= base_url('billing') ?>">
                            <i class="fas fa-file-invoice"></i>
                            <span>Billing</span>
                        </a>
                    </li>

                    <li class="sidebar-item">
                        <a class="sidebar-link" href="<?= base_url('inventory') ?>">
                            <i class="fas fa-box"></i>
                            <span>Inventory</span>
                        </a>
                    </li>

                    <?php if (has_role(['admin', 'manager'])): ?>
                        <li class="sidebar-header">Marketing</li>

                        <li class="sidebar-item">
                            <a class="sidebar-link" href="<?= base_url('gallery/admin') ?>">
                                <i class="fas fa-images"></i>
                                <span>Gallery</span>
                            </a>
                        </li>

                        <li class="sidebar-item">
                            <a class="sidebar-link collapsed" href="#" data-bs-toggle="collapse" data-bs-target="#membership-menu" aria-expanded="false">
                                <i class="fas fa-id-card"></i>
                                <span>Membership</span>
                                <i class="fas fa-chevron-down ms-auto"></i>
                            </a>
                            <div class="collapse" id="membership-menu">
                                <ul class="sidebar-submenu list-unstyled">
                                    <li class="sidebar-item">
                                        <a class="sidebar-link" href="<?= base_url('discounts') ?>">
                                            <i class="fas fa-percentage"></i>
                                            <span>Discounts</span>
                                        </a>
                                    </li>
                                    <li class="sidebar-item">
                                        <a class="sidebar-link" href="<?= base_url('wallet') ?>">
                                            <i class="fas fa-wallet"></i>
                                            <span>Wallet</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <li class="sidebar-item">
                            <a class="sidebar-link" href="<?= base_url('coupons') ?>">
                                <i class="fas fa-ticket-alt"></i>
                                <span>Coupons</span>
                            </a>
                        </li>

                        <li class="sidebar-header">Management</li>

                        <li class="sidebar-item">
                            <a class="sidebar-link" href="<?= base_url('staff') ?>">
                                <i class="fas fa-user-tie"></i>
                                <span>Staff</span>
                            </a>
                        </li>

                        <li class="sidebar-item">
                            <a class="sidebar-link" href="<?= base_url('payroll') ?>">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>Payroll</span>
                            </a>
                        </li>

                        <li class="sidebar-item">
                            <a class="sidebar-link" href="<?= base_url('reports') ?>">
                                <i class="fas fa-chart-bar"></i>
                                <span>Reports</span>
                            </a>
                        </li>

                        <li class="sidebar-item">
                            <a class="sidebar-link" href="<?= base_url('settings') ?>">
                                <i class="fas fa-cog"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>

                <!-- Sidebar Footer -->
                <div class="sidebar-footer">
                    <a href="<?= base_url('profile') ?>" class="sidebar-footer-link">
                        <i class="fas fa-user"></i>
                        <span>Profile</span>
                    </a>
                    <a href="<?= base_url('logout') ?>" class="sidebar-footer-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </div>

            <!-- Main Content Wrapper -->
            <div class="main-content">
                <!-- Top Navbar -->
                <nav class="top-navbar">
                    <div class="top-navbar-left">
                        <button id="mobile-sidebar-toggle" class="mobile-sidebar-toggle" aria-label="Toggle Sidebar">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h4 class="page-title mb-0">Dashboard</h4>
                    </div>
                    <div class="top-navbar-right">
                        <div class="dropdown ms-2">
                            <button class="btn btn-link dropdown-toggle" type="button" id="userMenuDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <?php if (!empty($_SESSION['profile_picture'])): ?>
                                    <img src="<?= base_url('uploads/profile/' . $_SESSION['profile_picture']) ?>" alt="Profile" class="avatar-img">
                                <?php else: ?>
                                    <div class="avatar-circle">
                                        <?= strtoupper(substr($_SESSION['user_name'] ?? 'U', 0, 1)) ?>
                                    </div>
                                <?php endif; ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userMenuDropdown">
                                <li><h6 class="dropdown-header"><?= $_SESSION['user_name'] ?? 'User' ?></h6></li>
                                <li><a class="dropdown-item" href="<?= base_url('profile') ?>"><i class="fas fa-user me-2"></i> Profile</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('settings') ?>"><i class="fas fa-cog me-2"></i> Settings</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= base_url('logout') ?>"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </nav>
    <?php elseif (is_logged_in() && current_user_role() == 'customer'): ?>
        <!-- Customer Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?= base_url() ?>">
                    <?php if (!empty(get_setting('logo_path'))): ?>
                        <img src="<?= base_url(get_setting('logo_path')) ?>" alt="<?= get_salon_name() ?>" style="max-height: 40px;">
                    <?php else: ?>
                        <?= get_salon_name() ?>
                    <?php endif; ?>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url() ?>">
                                <i class="fas fa-home"></i> Home
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('customer') ?>">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('gallery') ?>">
                                <i class="fas fa-images"></i> Gallery
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('booking') ?>">
                                <i class="fas fa-calendar-plus"></i> Book Appointment
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('customer/appointments') ?>">
                                <i class="fas fa-calendar-alt"></i> My Appointments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('customer/invoices') ?>">
                                <i class="fas fa-file-invoice"></i> My Invoices
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('customer/memberships') ?>">
                                <i class="fas fa-id-card"></i> My Membership
                            </a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle"></i> <?= $_SESSION['user_name'] ?? 'User' ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="<?= base_url('profile') ?>">
                                        <i class="fas fa-user"></i> Profile
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="<?= base_url('logout') ?>">
                                        <i class="fas fa-sign-out-alt"></i> Logout
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    <?php else: ?>
        <!-- Public Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?= base_url() ?>">
                    <?php if (!empty(get_setting('logo_path'))): ?>
                        <img src="<?= base_url(get_setting('logo_path')) ?>" alt="<?= get_salon_name() ?>" style="max-height: 40px;">
                    <?php else: ?>
                        <?= get_salon_name() ?>
                    <?php endif; ?>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url() ?>">
                                <i class="fas fa-home"></i> Home
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('services') ?>">
                                <i class="fas fa-cut"></i> Services
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('gallery') ?>">
                                <i class="fas fa-images"></i> Gallery
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('booking') ?>">
                                <i class="fas fa-calendar-plus"></i> Book Appointment
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('contact') ?>">
                                <i class="fas fa-envelope"></i> Contact
                            </a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('login') ?>">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('register') ?>">
                                <i class="fas fa-user-plus"></i> Register
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    <?php endif; ?>

    <?php if (is_logged_in() && current_user_role() != 'customer'): ?>
                <!-- Admin/Staff Content Area -->
                <div class="content-wrapper">
                    <!-- Flash Messages -->
                    <?php if (flash('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle me-2"></i>
                                <div><?= flash('success') ?></div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (flash('error', null, true)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <div><strong>Error:</strong> <?= flash('error') ?></div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (flash('info')): ?>
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle me-2"></i>
                                <div><?= flash('info') ?></div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (flash('warning')): ?>
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <div><strong>Warning:</strong> <?= flash('warning') ?></div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Page Content -->
                    <?= $content ?>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Main Content for non-admin users -->
        <main class="container py-4">
            <!-- Flash Messages -->
            <?php if (flash('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-2"></i>
                        <div><?= flash('success') ?></div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (flash('error', null, true)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <div><strong>Error:</strong> <?= flash('error') ?></div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (flash('info')): ?>
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-2"></i>
                        <div><?= flash('info') ?></div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (flash('warning')): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <div><strong>Warning:</strong> <?= flash('warning') ?></div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Page Content -->
            <?= $content ?>
        </main>
    <?php endif; ?>

    <!-- Footer - Only show for public pages and customer pages -->
    <?php if (!is_logged_in() || current_user_role() == 'customer'): ?>
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <?php if (!empty(get_setting('logo_path'))): ?>
                        <div class="bg-dark rounded p-3 d-inline-block mb-3" style="background-color: #f8f9fa;">
                            <img src="<?= base_url(get_setting('logo_path')) ?>" alt="<?= get_salon_name() ?>" style="max-height: 50px;">
                        </div>
                    <?php else: ?>
                        <h5><?= get_salon_name() ?></h5>
                    <?php endif; ?>
                    <p>Your one-stop solution for all beauty needs.</p>
                </div>
                <div class="col-md-4">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="<?= base_url() ?>" class="text-white">Home</a></li>
                        <li><a href="<?= base_url('services') ?>" class="text-white">Services</a></li>
                        <li><a href="<?= base_url('gallery') ?>" class="text-white">Gallery</a></li>
                        <li><a href="<?= base_url('booking') ?>" class="text-white">Book Appointment</a></li>
                        <li><a href="<?= base_url('contact') ?>" class="text-white">Contact</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Contact Us</h5>
                    <address>
                        <i class="fas fa-map-marker-alt"></i> <?= get_setting('salon_address', get_setting('address', '123 Main Street, City, State, ZIP')) ?><br>
                        <i class="fas fa-phone"></i> <?= get_setting('salon_phone', get_setting('phone', '+1234567890')) ?><br>
                        <i class="fas fa-envelope"></i> <?= get_setting('salon_email', get_setting('email', '<EMAIL>')) ?><br>
                        <?php if (!empty(get_setting('salon_website'))): ?>
                        <i class="fas fa-globe"></i> <a href="<?= get_setting('salon_website') ?>" class="text-white" target="_blank"><?= get_setting('salon_website') ?></a>
                        <?php endif; ?>
                    </address>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; <?= date('Y') ?> <?= get_salon_name() ?>. All rights reserved.</p>
            </div>
        </div>
    </footer>
    <?php endif; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Dynamic Base URL Script -->
    <script>
        // Define the base URL for JavaScript
        const base_url = '<?= BASE_URL ?>';

        // This function helps ensure all AJAX requests use the correct base URL
        function getUrl(path) {
            // Ensure path doesn't start with a slash when concatenating
            return base_url + (path ? path.replace(/^\//, '') : '');
        }

        // Log the base URL for debugging
        console.log('Base URL:', base_url);
    </script>
    <!-- Custom JS -->
    <script src="<?= base_url('assets/js/script.js') ?>"></script>
    <script src="<?= base_url('assets/js/dropdown-fix.js') ?>"></script>
    <script src="<?= base_url('assets/js/currency-fix.js') ?>"></script>
    <?php if (is_logged_in() && current_user_role() != 'customer'): ?>
        <!-- Admin Dashboard JS -->
        <script src="<?= base_url('assets/js/admin.js') ?>"></script>
    <?php endif; ?>
    <!-- Additional JS -->
    <?php if (isset($scripts)): ?>
        <?= $scripts ?>
    <?php endif; ?>
</body>
</html>

