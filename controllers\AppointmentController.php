<?php
/**
 * Appointment Controller
 */
class AppointmentController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            flash('error', 'Please login to access this page');
            header('Location: ' . base_url('login'));
            exit;
        }
        
        // Check if user has admin, manager, or staff role
        if (!has_role(['admin', 'manager', 'staff'])) {
            flash('error', 'You do not have permission to access this page');
            header('Location: ' . base_url());
            exit;
        }
    }
    
    /**
     * Display all appointments
     */
    public function index() {
        // Get filter parameters
        $status = input('status');
        $date = input('date');
        $staff_id = input('staff_id');
        
        // Get appointments
        $appointment_model = new AppointmentModel();
        $appointments = $appointment_model->getAllWithDetails($status, $date, $staff_id);
        
        // Get staff for filter
        $staff_model = new StaffModel();
        $staff = $staff_model->getAllWithUsers();
        
        // Render view
        $this->render('appointments/index', [
            'appointments' => $appointments,
            'staff' => $staff,
            'selected_status' => $status,
            'selected_date' => $date,
            'selected_staff' => $staff_id
        ]);
    }
    
    /**
     * Display appointment creation form
     */
    public function create() {
        // Get customers
        $customer_model = new CustomerModel();
        $customers = $customer_model->getAllWithUsers();
        
        // Get services
        $service_model = new ServiceModel();
        $services = $service_model->getAllActive();
        
        // Get staff
        $staff_model = new StaffModel();
        $staff = $staff_model->getAllWithUsers();
        
        // Render view
        $this->render('appointments/create', [
            'customers' => $customers,
            'services' => $services,
            'staff' => $staff
        ]);
    }
    
    /**
     * Store new appointment
     */
    public function store() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('appointments/create'));
        }
        
        // Get form data
        $customer_id = input('customer_id');
        $service_id = input('service_id');
        $staff_id = input('staff_id');
        $appointment_date = input('appointment_date');
        $start_time = input('start_time');
        $notes = input('notes');
        $status = input('status');
        
        // Validate form data
        if (empty($customer_id) || empty($service_id) || empty($staff_id) || empty($appointment_date) || empty($start_time)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('appointments/create'));
        }
        
        // Get service details to calculate end time
        $service_model = new ServiceModel();
        $service = $service_model->find($service_id);
        
        if (!$service) {
            flash('error', 'Service not found');
            $this->redirect(base_url('appointments/create'));
        }
        
        // Calculate end time
        $end_time = date('H:i:s', strtotime($start_time . ' +' . $service['duration'] . ' minutes'));
        
        // Check if time slot is available
        $appointment_model = new AppointmentModel();
        $is_available = $appointment_model->isTimeSlotAvailable($staff_id, $appointment_date, $start_time, $end_time);
        
        if (!$is_available) {
            flash('error', 'The selected time slot is not available. Please choose another time.');
            $this->redirect(base_url('appointments/create'));
        }
        
        // Create appointment
        $appointment_data = [
            'customer_id' => $customer_id,
            'service_id' => $service_id,
            'staff_id' => $staff_id,
            'appointment_date' => $appointment_date,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'notes' => $notes,
            'status' => $status
        ];
        
        $appointment_id = $appointment_model->create($appointment_data);
        
        if (!$appointment_id) {
            flash('error', 'Failed to create appointment');
            $this->redirect(base_url('appointments/create'));
        }
        
        flash('success', 'Appointment created successfully');
        $this->redirect(base_url('appointments'));
    }
    
    /**
     * Display appointment details
     */
    public function view($id) {
        // Get appointment
        $appointment_model = new AppointmentModel();
        $appointment = $appointment_model->getWithDetails($id);
        
        if (!$appointment) {
            flash('error', 'Appointment not found');
            $this->redirect(base_url('appointments'));
        }
        
        // Render view
        $this->render('appointments/view', [
            'appointment' => $appointment
        ]);
    }
    
    /**
     * Display appointment edit form
     */
    public function edit($id) {
        // Get appointment
        $appointment_model = new AppointmentModel();
        $appointment = $appointment_model->find($id);
        
        if (!$appointment) {
            flash('error', 'Appointment not found');
            $this->redirect(base_url('appointments'));
        }
        
        // Get customers
        $customer_model = new CustomerModel();
        $customers = $customer_model->getAllWithUsers();
        
        // Get services
        $service_model = new ServiceModel();
        $services = $service_model->getAllActive();
        
        // Get staff
        $staff_model = new StaffModel();
        $staff = $staff_model->getAllWithUsers();
        
        // Render view
        $this->render('appointments/edit', [
            'appointment' => $appointment,
            'customers' => $customers,
            'services' => $services,
            'staff' => $staff
        ]);
    }
    
    /**
     * Update appointment
     */
    public function update($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('appointments/edit/' . $id));
        }
        
        // Get appointment
        $appointment_model = new AppointmentModel();
        $appointment = $appointment_model->find($id);
        
        if (!$appointment) {
            flash('error', 'Appointment not found');
            $this->redirect(base_url('appointments'));
        }
        
        // Get form data
        $customer_id = input('customer_id');
        $service_id = input('service_id');
        $staff_id = input('staff_id');
        $appointment_date = input('appointment_date');
        $start_time = input('start_time');
        $notes = input('notes');
        $status = input('status');
        
        // Validate form data
        if (empty($customer_id) || empty($service_id) || empty($staff_id) || empty($appointment_date) || empty($start_time)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('appointments/edit/' . $id));
        }
        
        // Get service details to calculate end time
        $service_model = new ServiceModel();
        $service = $service_model->find($service_id);
        
        if (!$service) {
            flash('error', 'Service not found');
            $this->redirect(base_url('appointments/edit/' . $id));
        }
        
        // Calculate end time
        $end_time = date('H:i:s', strtotime($start_time . ' +' . $service['duration'] . ' minutes'));
        
        // Check if time slot is available (excluding this appointment)
        $is_available = $appointment_model->isTimeSlotAvailable($staff_id, $appointment_date, $start_time, $end_time, $id);
        
        if (!$is_available) {
            flash('error', 'The selected time slot is not available. Please choose another time.');
            $this->redirect(base_url('appointments/edit/' . $id));
        }
        
        // Update appointment
        $appointment_data = [
            'customer_id' => $customer_id,
            'service_id' => $service_id,
            'staff_id' => $staff_id,
            'appointment_date' => $appointment_date,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'notes' => $notes,
            'status' => $status
        ];
        
        $result = $appointment_model->update($id, $appointment_data);
        
        if (!$result) {
            flash('error', 'Failed to update appointment');
            $this->redirect(base_url('appointments/edit/' . $id));
        }
        
        flash('success', 'Appointment updated successfully');
        $this->redirect(base_url('appointments'));
    }
    
    /**
     * Update appointment status
     */
    public function updateStatus($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('appointments'));
        }
        
        // Get appointment
        $appointment_model = new AppointmentModel();
        $appointment = $appointment_model->find($id);
        
        if (!$appointment) {
            flash('error', 'Appointment not found');
            $this->redirect(base_url('appointments'));
        }
        
        // Get form data
        $status = input('status');
        
        // Validate form data
        if (empty($status)) {
            flash('error', 'Please select a status');
            $this->redirect(base_url('appointments'));
        }
        
        // Update appointment status
        $result = $appointment_model->update($id, ['status' => $status]);
        
        if (!$result) {
            flash('error', 'Failed to update appointment status');
            $this->redirect(base_url('appointments'));
        }
        
        flash('success', 'Appointment status updated successfully');
        $this->redirect(base_url('appointments'));
    }
    
    /**
     * Delete appointment
     */
    public function delete($id) {
        // Get appointment
        $appointment_model = new AppointmentModel();
        $appointment = $appointment_model->find($id);
        
        if (!$appointment) {
            flash('error', 'Appointment not found');
            $this->redirect(base_url('appointments'));
        }
        
        // Delete appointment
        $result = $appointment_model->delete($id);
        
        if (!$result) {
            flash('error', 'Failed to delete appointment');
            $this->redirect(base_url('appointments'));
        }
        
        flash('success', 'Appointment deleted successfully');
        $this->redirect(base_url('appointments'));
    }
    
    /**
     * Display calendar view
     */
    public function calendar() {
        // Get staff for filter
        $staff_model = new StaffModel();
        $staff = $staff_model->getAllWithUsers();
        
        // Get selected staff ID
        $staff_id = input('staff_id');
        
        // Get selected month and year
        $month = input('month') ? input('month') : date('m');
        $year = input('year') ? input('year') : date('Y');
        
        // Get appointments for the selected month
        $appointment_model = new AppointmentModel();
        $appointments = $appointment_model->getMonthAppointments($month, $year, $staff_id);
        
        // Prepare calendar data
        $calendar_data = [];
        foreach ($appointments as $appointment) {
            $date = $appointment['appointment_date'];
            if (!isset($calendar_data[$date])) {
                $calendar_data[$date] = [];
            }
            $calendar_data[$date][] = $appointment;
        }
        
        // Render view
        $this->render('appointments/calendar', [
            'staff' => $staff,
            'selected_staff' => $staff_id,
            'selected_month' => $month,
            'selected_year' => $year,
            'calendar_data' => $calendar_data
        ]);
    }
}
