<?php
/**
 * Customer Model
 */
class CustomerModel extends Model {
    protected $table = 'customers';

    /**
     * Get customer with user data
     *
     * @param int $id Customer ID
     * @return array|false Customer data or false if not found
     */
    public function getWithUser($id) {
        $query = "SELECT c.*, u.name, u.email, u.role,
                  COALESCE(u.status, 'active') as status
                  FROM customers c
                  JOIN users u ON c.user_id = u.id
                  WHERE c.id = :id";

        try {
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            // Debug information
            error_log("getWithUser query result for ID $id: " . json_encode($result));

            // Ensure status is set
            if ($result && !isset($result['status'])) {
                $result['status'] = 'active';
                error_log("Status not found in query result, defaulting to 'active'");
            }

            return $result;
        } catch (PDOException $e) {
            error_log("Error in getWithUser: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all customers with user data
     *
     * @return array Customers
     */
    public function getAllWithUsers() {
        $query = "SELECT c.*, u.name, u.email, u.status
                 FROM " . $this->table . " c
                 JOIN users u ON c.user_id = u.id
                 ORDER BY c.id DESC";
        $stmt = $this->db->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Find customer by user ID
     *
     * @param int $user_id User ID
     * @return array|false Customer data or false if not found
     */
    public function findByUserId($user_id) {
        $query = "SELECT * FROM " . $this->table . " WHERE user_id = :user_id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get customer membership
     *
     * @param int $customer_id Customer ID
     * @return array|false Membership data or false if not found
     */
    public function getMembership($customer_id) {
        try {
            // Use the correct table and column names based on the MembershipModel
            $query = "SELECT cm.*, mp.name, mp.description,
                     COALESCE(mp.service_discount, mp.discount_percentage, 0) as service_discount,
                     COALESCE(mp.product_discount, mp.discount_percentage, 0) as product_discount
                     FROM customer_memberships cm
                     JOIN membership_plans mp ON cm.plan_id = mp.id
                     WHERE cm.customer_id = :customer_id AND cm.status = 'active' AND cm.end_date >= CURDATE()
                     ORDER BY cm.end_date DESC
                     LIMIT 1";

            error_log("Membership query: $query");

            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':customer_id', $customer_id);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            // Log the result
            error_log("Membership query result for customer $customer_id: " . ($result ? json_encode($result) : 'No active membership found'));

            return $result;
        } catch (PDOException $e) {
            error_log("Error in getMembership: " . $e->getMessage());

            // Try a more basic query as fallback
            try {
                $query = "SELECT * FROM customer_memberships
                         WHERE customer_id = :customer_id AND status = 'active' AND end_date >= CURDATE()
                         ORDER BY end_date DESC
                         LIMIT 1";

                error_log("Basic membership query: $query");

                $stmt = $this->db->prepare($query);
                $stmt->bindParam(':customer_id', $customer_id);
                $stmt->execute();

                $membership = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($membership && isset($membership['plan_id'])) {
                    // Now get the membership plan details
                    $query = "SELECT * FROM membership_plans WHERE id = :plan_id LIMIT 1";
                    $stmt = $this->db->prepare($query);
                    $stmt->bindParam(':plan_id', $membership['plan_id']);
                    $stmt->execute();

                    $plan = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($plan) {
                        // Merge the plan details with the membership
                        $result = array_merge($membership, [
                            'name' => $plan['name'],
                            'description' => $plan['description'],
                            'service_discount' => $plan['service_discount'] ?? $plan['discount_percentage'] ?? 0,
                            'product_discount' => $plan['product_discount'] ?? $plan['discount_percentage'] ?? 0
                        ]);

                        error_log("Fallback membership query result: " . json_encode($result));
                        return $result;
                    }
                }

                error_log("No membership found in fallback query");
                return false;
            } catch (PDOException $e2) {
                error_log("Error in fallback getMembership query: " . $e2->getMessage());
                return false;
            }
        }
    }

    /**
     * Get customer appointments
     *
     * @param int $customer_id Customer ID
     * @param string $status Appointment status (optional)
     * @return array Appointments
     */
    public function getAppointments($customer_id, $status = null) {
        $query = "SELECT a.*, s.name as service_name, u.name as staff_name
                 FROM appointments a
                 JOIN services s ON a.service_id = s.id
                 JOIN staff st ON a.staff_id = st.id
                 JOIN users u ON st.user_id = u.id
                 WHERE a.customer_id = :customer_id";

        if ($status) {
            $query .= " AND a.status = :status";
        }

        $query .= " ORDER BY a.appointment_date DESC, a.start_time DESC";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':customer_id', $customer_id);

        if ($status) {
            $stmt->bindParam(':status', $status);
        }

        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Search customers
     *
     * @param string $search Search term
     * @return array Customers
     */
    public function search($search) {
        $query = "SELECT c.*, u.name, u.email, u.status
                 FROM " . $this->table . " c
                 JOIN users u ON c.user_id = u.id
                 WHERE u.name LIKE :search OR u.email LIKE :search OR c.phone LIKE :search
                 ORDER BY u.name";
        $stmt = $this->db->prepare($query);
        $search_term = "%$search%";
        $stmt->bindParam(':search', $search_term);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get customer invoices
     *
     * @param int $customer_id Customer ID
     * @return array Invoices
     */
    public function getInvoices($customer_id) {
        $query = "SELECT * FROM invoices WHERE customer_id = :customer_id ORDER BY invoice_date DESC";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':customer_id', $customer_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }



    /**
     * Get recent customers
     *
     * @param int $limit Number of customers to return
     * @return array Customers
     */
    public function getRecentCustomers($limit = 5) {
        $query = "SELECT c.*, u.name, u.email, u.status
                 FROM " . $this->table . " c
                 JOIN users u ON c.user_id = u.id
                 ORDER BY c.id DESC
                 LIMIT :limit";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get customer by ID
     *
     * @param int $id Customer ID
     * @return array|false Customer data or false if not found
     */
    public function getById($id) {
        $query = "SELECT c.*, COALESCE(c.status, 'active') as status
                  FROM customers c
                  WHERE c.id = :id";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Update customer
     *
     * @param int $id Customer ID
     * @param array $data Customer data
     * @return bool True if successful, false otherwise
     */
    public function update($id, $data) {
        // Make sure we have the status field
        if (!isset($data['status'])) {
            $data['status'] = 'active'; // Default to active if not provided
        }

        // Build the SQL query
        $sql = "UPDATE customers SET ";
        $params = [];

        foreach ($data as $key => $value) {
            $sql .= "$key = :$key, ";
            $params[":$key"] = $value;
        }

        $sql = rtrim($sql, ", ");
        $sql .= " WHERE id = :id";
        $params[':id'] = $id;

        // Debug the SQL query
        error_log("Update SQL: $sql");
        error_log("Update params: " . json_encode($params));

        // Execute the query
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Get top most frequent customers based on invoice count
     *
     * @param int $limit Number of customers to return
     * @return array Customers
     */
    public function getTopCustomers($limit = 10) {
        try {
            // Get customers with the most invoices
            $query = "SELECT c.*, u.name, u.email, COUNT(i.id) as invoice_count
                     FROM " . $this->table . " c
                     JOIN users u ON c.user_id = u.id
                     LEFT JOIN invoices i ON c.id = i.customer_id
                     GROUP BY c.id
                     ORDER BY invoice_count DESC, u.name ASC
                     LIMIT :limit";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();

            $customers = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // If no customers found or fewer than limit, get the most recent customers
            if (count($customers) < $limit) {
                $remaining = $limit - count($customers);
                $used_ids = array_column($customers, 'id');

                $exclude_clause = empty($used_ids) ? "" : "AND c.id NOT IN (" . implode(',', $used_ids) . ")";

                $query = "SELECT c.*, u.name, u.email, 0 as invoice_count
                         FROM " . $this->table . " c
                         JOIN users u ON c.user_id = u.id
                         WHERE 1=1 $exclude_clause
                         ORDER BY c.created_at DESC, u.name ASC
                         LIMIT :limit";
                $stmt = $this->db->prepare($query);
                $stmt->bindParam(':limit', $remaining, PDO::PARAM_INT);
                $stmt->execute();

                $recent_customers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $customers = array_merge($customers, $recent_customers);
            }

            return $customers;
        } catch (PDOException $e) {
            // If there's an error, fall back to a simpler query
            error_log('Error in getTopCustomers: ' . $e->getMessage());

            $query = "SELECT c.*, u.name, u.email, 0 as invoice_count
                     FROM " . $this->table . " c
                     JOIN users u ON c.user_id = u.id
                     ORDER BY u.name ASC
                     LIMIT :limit";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }

    /**
     * Search customers by name, email or phone
     *
     * @param string $search Search term
     * @return array Customers
     */
    public function searchCustomers($search) {
        try {
            $search = '%' . $search . '%';
            $query = "SELECT c.*, u.name, u.email
                     FROM " . $this->table . " c
                     JOIN users u ON c.user_id = u.id
                     WHERE u.name LIKE :search OR u.email LIKE :search OR c.phone LIKE :search
                     ORDER BY u.name
                     LIMIT 20"; // Limit results to prevent overwhelming the dropdown
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':search', $search);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            // Log error and return empty array
            error_log('Error searching customers: ' . $e->getMessage());
            return [];
        }
    }
}







