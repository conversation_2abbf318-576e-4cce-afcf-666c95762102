-- Gallery Database Tables for Live Server
-- Run this SQL script to create gallery tables in your live server database

-- Gallery Categories Table
CREATE TABLE IF NOT EXISTS `gallery_categories` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(100) NOT NULL,
    `description` TEXT,
    `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Gallery Images Table
CREATE TABLE IF NOT EXISTS `gallery_images` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `title` VARCHAR(200) NOT NULL,
    `description` TEXT,
    `category` INT,
    `image_path` VARCHAR(500) NOT NULL,
    `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
    `created_by` INT,
    `created_at` TIMES<PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`category`) REFERENCES `gallery_categories`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert Default Gallery Categories
INSERT INTO `gallery_categories` (`name`, `description`, `status`) VALUES
('Hair Styling', 'Hair cuts, styling, and treatments', 'active'),
('Facial Treatments', 'Facial care and skin treatments', 'active'),
('Nail Art', 'Manicure, pedicure, and nail designs', 'active'),
('Makeup', 'Bridal makeup, party makeup, and special occasions', 'active'),
('Spa Treatments', 'Relaxing spa and wellness treatments', 'active'),
('Before & After', 'Transformation photos showing results', 'active');

-- Create indexes for better performance
CREATE INDEX `idx_gallery_images_category` ON `gallery_images`(`category`);
CREATE INDEX `idx_gallery_images_status` ON `gallery_images`(`status`);
CREATE INDEX `idx_gallery_images_created_at` ON `gallery_images`(`created_at`);
CREATE INDEX `idx_gallery_categories_status` ON `gallery_categories`(`status`);
