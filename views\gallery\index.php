<div class="container py-5">
    <h1 class="text-center mb-5 fade-in">Our Gallery</h1>

    <!-- Category Filter -->
    <div class="row mb-4 fade-in" style="animation-delay: 0.1s;">
        <div class="col-12">
            <div class="d-flex flex-wrap justify-content-center gap-2">
                <a href="<?= base_url('gallery') ?>" class="btn <?= !$active_category ? 'btn-primary' : 'btn-outline-primary' ?>">
                    All
                </a>
                <?php if (!empty($categories)): ?>
                    <?php foreach ($categories as $category): ?>
                        <a href="<?= base_url('gallery?category=' . $category['id']) ?>" class="btn <?= $active_category == $category['id'] ? 'btn-primary' : 'btn-outline-primary' ?>">
                            <?= $category['name'] ?>
                        </a>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Gallery Grid -->
    <?php if (empty($images)): ?>
        <div class="alert alert-info text-center fade-in" style="animation-delay: 0.2s;">
            <i class="fas fa-info-circle me-2"></i>
            <?php if (empty($categories)): ?>
                No gallery images have been added yet. Please check back later.
            <?php elseif ($active_category): ?>
                No images found in this category.
            <?php else: ?>
                No gallery images have been added yet. Please check back later.
            <?php endif; ?>
        </div>
    <?php else: ?>
        <div class="row g-4">
            <?php foreach ($images as $index => $image): ?>
                <div class="col-md-4 col-sm-6 fade-in" style="animation-delay: <?= 0.2 + ($index * 0.1) ?>s;">
                    <div class="card gallery-card h-100">
                        <div class="gallery-image">
                            <a href="<?= base_url($image['image_path']) ?>" data-lightbox="gallery" data-title="<?= htmlspecialchars($image['title']) ?>">
                                <img src="<?= base_url($image['image_path']) ?>" class="card-img-top" alt="<?= htmlspecialchars($image['title']) ?>">
                                <div class="gallery-overlay">
                                    <i class="fas fa-search-plus"></i>
                                </div>
                            </a>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title"><?= $image['title'] ?></h5>
                            <?php if (!empty($image['description'])): ?>
                                <p class="card-text"><?= $image['description'] ?></p>
                            <?php endif; ?>
                            <div class="text-muted small">
                                <span class="badge bg-primary"><?= $image['category_name'] ?? 'Uncategorized' ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<!-- Lightbox CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css">

<!-- Lightbox JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>

<style>
    .gallery-card {
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .gallery-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .gallery-image {
        position: relative;
        overflow: hidden;
        height: 250px;
    }

    .gallery-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }

    .gallery-card:hover .gallery-image img {
        transform: scale(1.05);
    }

    .gallery-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .gallery-overlay i {
        color: white;
        font-size: 2rem;
    }

    .gallery-card:hover .gallery-overlay {
        opacity: 1;
    }

    /* Animation */
    .fade-in {
        opacity: 0;
        animation: fadeIn 0.5s ease forwards;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>

<script>
    // Initialize Lightbox
    lightbox.option({
        'resizeDuration': 200,
        'wrapAround': true,
        'albumLabel': "Image %1 of %2"
    });
</script>
