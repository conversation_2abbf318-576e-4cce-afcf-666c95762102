<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-box"></i> Inventory</h1>
        <div>
            <div class="btn-group me-2">
                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-file-import"></i> Import/Export
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a class="dropdown-item" href="<?= base_url('inventory/export') ?>">
                            <i class="fas fa-file-export me-2"></i> Export Products
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="<?= base_url('inventory/download-template') ?>">
                            <i class="fas fa-file-download me-2"></i> Download Import Template
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#importModal">
                            <i class="fas fa-file-upload me-2"></i> Import Products
                        </a>
                    </li>
                </ul>
            </div>
            <a href="<?= base_url('inventory/categories') ?>" class="btn btn-outline-primary me-2">
                <i class="fas fa-tags"></i> Categories
            </a>
            <a href="<?= base_url('inventory/create') ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> New Product
            </a>
        </div>
    </div>

    <!-- Low Stock Alert -->
    <?php if (!empty($low_stock_products)): ?>
        <div class="alert alert-warning mb-4">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                <div>
                    <h5 class="alert-heading mb-1">Low Stock Alert</h5>
                    <p class="mb-0">
                        <?= count($low_stock_products) ?> products are running low on stock.
                        <a href="#low-stock-section" class="alert-link">View details</a>
                    </p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Search and Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('inventory') ?>" method="get" class="row g-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" name="search" placeholder="Search by name or description" value="<?= $search ?>">
                        <button type="submit" class="btn btn-primary">Search</button>
                        <?php if ($search): ?>
                            <a href="<?= base_url('inventory') ?>" class="btn btn-outline-secondary">Clear</a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="category_id">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?= $category['id'] ?>" <?= $selected_category == $category['id'] ? 'selected' : '' ?>>
                                <?= $category['name'] ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="status">
                        <option value="">All Status</option>
                        <option value="active" <?= $selected_status == 'active' ? 'selected' : '' ?>>Active</option>
                        <option value="inactive" <?= $selected_status == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                    </select>
                </div>
            </form>
        </div>
    </div>

    <!-- Products List -->
    <div class="card shadow-sm mb-4">
        <div class="card-body p-0">
            <?php if (empty($products)): ?>
                <div class="p-4 text-center">
                    <img src="<?= base_url('assets/images/inventory.svg') ?>" alt="No Products" style="width: 120px; opacity: 0.5;" class="mb-3">
                    <p class="text-muted mb-0">No products found matching your criteria.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Product</th>
                                <th>Category</th>
                                <th>Price</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($products as $product): ?>
                                <tr>
                                    <td>#<?= $product['id'] ?></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if (!empty($product['image'])): ?>
                                                <img src="<?= base_url('uploads/products/' . $product['image']) ?>" alt="<?= $product['name'] ?>" class="product-thumbnail me-2">
                                            <?php else: ?>
                                                <div class="product-thumbnail bg-light d-flex align-items-center justify-content-center me-2">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="fw-bold"><?= $product['name'] ?></div>
                                                <div class="small text-muted"><?= substr($product['description'], 0, 50) . (strlen($product['description']) > 50 ? '...' : '') ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?= $product['category_name'] ?></td>
                                    <td>
                                        <div class="fw-bold"><?= format_currency($product['selling_price']) ?></div>
                                        <div class="small text-muted">Cost: <?= format_currency($product['cost_price']) ?></div>
                                    </td>
                                    <td>
                                        <?php if ($product['quantity'] <= $product['low_stock_threshold']): ?>
                                            <span class="badge bg-danger"><?= $product['quantity'] ?></span>
                                        <?php elseif ($product['quantity'] <= $product['low_stock_threshold'] * 2): ?>
                                            <span class="badge bg-warning"><?= $product['quantity'] ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-success"><?= $product['quantity'] ?></span>
                                        <?php endif; ?>
                                        <div class="small text-muted">Min: <?= $product['low_stock_threshold'] ?></div>
                                    </td>
                                    <td>
                                        <?php if ($product['status'] == 'active'): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="<?= base_url('inventory/view/' . $product['id']) ?>" class="btn btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('inventory/edit/' . $product['id']) ?>" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?= base_url('inventory/adjust-stock/' . $product['id']) ?>" class="btn btn-outline-info" title="Adjust Stock">
                                                <i class="fas fa-boxes"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" title="Delete"
                                               data-bs-toggle="modal" data-bs-target="#deleteModal<?= $product['id'] ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Low Stock Products -->
    <?php if (!empty($low_stock_products)): ?>
        <div id="low-stock-section" class="card shadow-sm">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Low Stock Products</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Category</th>
                                <th>Current Stock</th>
                                <th>Min Stock</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($low_stock_products as $product): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if (!empty($product['image'])): ?>
                                                <img src="<?= base_url('uploads/products/' . $product['image']) ?>" alt="<?= $product['name'] ?>" class="product-thumbnail me-2">
                                            <?php else: ?>
                                                <div class="product-thumbnail bg-light d-flex align-items-center justify-content-center me-2">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div class="fw-bold"><?= $product['name'] ?></div>
                                        </div>
                                    </td>
                                    <td><?= $product['category_name'] ?></td>
                                    <td><span class="badge bg-danger"><?= $product['quantity'] ?></span></td>
                                    <td><?= $product['low_stock_threshold'] ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="<?= base_url('inventory/view/' . $product['id']) ?>" class="btn btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('inventory/edit/' . $product['id']) ?>" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?= base_url('inventory/adjust-stock/' . $product['id']) ?>" class="btn btn-warning" title="Restock">
                                                <i class="fas fa-plus-circle"></i><span class="ms-1">Restock</span>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.product-thumbnail {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    overflow: hidden;
    object-fit: cover;
}

/* Action buttons styling */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

/* Responsive adjustments for action buttons */
@media (max-width: 768px) {
    .btn-group-sm .btn {
        padding: 0.25rem 0.4rem;
    }

    .btn-group-sm .btn i {
        font-size: 0.8rem;
    }

    .btn-warning i + span {
        display: none; /* Hide "Restock" text on small screens */
    }
}

/* Add tooltip style */
[title] {
    position: relative;
    cursor: pointer;
}
</style>

<!-- Initialize tooltips -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            placement: 'top',
            trigger: 'hover'
        });
    });
});
</script>

<!-- Delete Product Modals -->
<?php foreach ($products as $product): ?>
<div class="modal fade" id="deleteModal<?= $product['id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $product['id'] ?>" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel<?= $product['id'] ?>">
                    <i class="fas fa-exclamation-triangle me-2"></i> Delete Product
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the following product?</p>
                <div class="d-flex align-items-center bg-light p-3 rounded mb-3">
                    <?php if (!empty($product['image'])): ?>
                        <img src="<?= base_url('uploads/products/' . $product['image']) ?>" alt="<?= $product['name'] ?>" class="product-thumbnail me-3">
                    <?php else: ?>
                        <div class="product-thumbnail bg-white d-flex align-items-center justify-content-center me-3">
                            <i class="fas fa-box text-muted"></i>
                        </div>
                    <?php endif; ?>
                    <div>
                        <h5 class="mb-1"><?= $product['name'] ?></h5>
                        <p class="mb-0 text-muted"><?= $product['category_name'] ?> - <?= format_currency($product['selling_price']) ?></p>
                    </div>
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-circle me-2"></i> This action cannot be undone. All data related to this product will be permanently deleted.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="<?= base_url('inventory/delete/' . $product['id']) ?>" class="btn btn-danger">
                    <i class="fas fa-trash me-1"></i> Delete Product
                </a>
            </div>
        </div>
    </div>
</div>
<?php endforeach; ?>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">Import Products</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= base_url('inventory/import') ?>" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="import_file" class="form-label">Select CSV File</label>
                        <input type="file" class="form-control" id="import_file" name="import_file" accept=".csv" required>
                        <div class="form-text">
                            Please upload a CSV file in the correct format.
                            <a href="<?= base_url('inventory/download-template') ?>" target="_blank">Download template</a>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Import Instructions:</h6>
                        <ul class="mb-0">
                            <li>File must be in CSV format</li>
                            <li>First row should contain column headers</li>
                            <li>Required fields: Name</li>
                            <li>New categories will be created automatically if they don't exist</li>
                            <li>Products with the same name or SKU will be updated instead of creating duplicates</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-file-upload me-1"></i> Import Products
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
