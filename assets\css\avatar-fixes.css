/* Avatar Consistency Fixes */

/* Base avatar circle style */
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50% !important;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    background-color: var(--primary);
    color: white;
    overflow: hidden;
}

/* Sidebar user avatar specific styles */
.sidebar-user .avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50% !important;
    margin-right: 0;
    flex-shrink: 0;
}

/* Sidebar user info styles */
.sidebar-user-info {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-left: 10px;
}

/* Sidebar user role badge */
.sidebar-user-role {
    font-size: 0.75rem;
    white-space: nowrap;
    background-color: rgba(99, 102, 241, 0.2) !important;
    color: #6366f1 !important;
    border-radius: 12px;
    padding: 2px 8px;
    display: inline-block;
    margin-top: 2px;
}

/* Fix for sidebar user container */
.sidebar-user {
    padding: 1rem;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Ensure consistent avatar image styling */
.avatar-img {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar-user .avatar-circle,
    .avatar-img {
        width: 36px !important;
        height: 36px !important;
    }
}
