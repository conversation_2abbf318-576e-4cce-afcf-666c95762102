<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-cogs"></i> Edit Slider</h1>
        <a href="<?= base_url('settings/appearance') ?>" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Appearance Settings
        </a>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Edit Slider</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('hero-slider/edit/' . $slider['id']) ?>" method="post" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="title" class="form-label">Title</label>
                                <input type="text" class="form-control" id="title" name="title" value="<?= $slider['title'] ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="subtitle" class="form-label">Subtitle</label>
                                <input type="text" class="form-control" id="subtitle" name="subtitle" value="<?= $slider['subtitle'] ?>">
                            </div>
                            
                            <div class="col-md-12 mb-3">
                                <label class="form-label">Current Image</label>
                                <div class="border rounded p-3 text-center">
                                    <img src="<?= base_url($slider['image_path']) ?>" alt="<?= $slider['title'] ?>" class="img-fluid" style="max-height: 200px;">
                                </div>
                            </div>
                            
                            <div class="col-md-12 mb-3">
                                <label for="image" class="form-label">Change Image</label>
                                <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                <div class="form-text">Recommended size: 1920x600 pixels. Leave empty to keep current image.</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="button_text" class="form-label">Button Text</label>
                                <input type="text" class="form-control" id="button_text" name="button_text" value="<?= $slider['button_text'] ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="button_link" class="form-label">Button Link</label>
                                <input type="text" class="form-control" id="button_link" name="button_link" value="<?= $slider['button_link'] ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?= $slider['status'] == 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="inactive" <?= $slider['status'] == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                </select>
                            </div>
                            
                            <div class="col-md-12 mt-3 text-end">
                                <a href="<?= base_url('settings/appearance') ?>" class="btn btn-secondary me-2">Cancel</a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> Update Slider
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Preview uploaded image
    document.getElementById('image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // Create preview
                let previewContainer = document.getElementById('slider-image-preview');
                if (!previewContainer) {
                    previewContainer = document.createElement('div');
                    previewContainer.id = 'slider-image-preview';
                    previewContainer.className = 'mt-3 border rounded p-2 text-center';
                    document.querySelector('.col-md-12.mb-3:nth-of-type(3)').appendChild(previewContainer);
                }
                
                previewContainer.innerHTML = `<img src="${e.target.result}" alt="Slider Preview" class="img-fluid" style="max-height: 200px;">`;
            }
            reader.readAsDataURL(file);
        }
    });
});
</script>
