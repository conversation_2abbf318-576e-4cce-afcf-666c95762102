<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-user-friends"></i> Customer Report</h1>
    </div>

    <!-- Date Range Filter -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('reports/customers') ?>" method="get" class="row align-items-end">
                <div class="col-md-4 mb-2">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                </div>
                <div class="col-md-4 mb-2">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                </div>
                <div class="col-md-4 mb-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i> Apply Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <!-- Reports Navigation -->
        <div class="col-md-3 mb-4">
            <?php include 'partials/reports_nav.php'; ?>
        </div>
        
        <!-- Reports Content -->
        <div class="col-md-9">
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Customers</h6>
                                    <h4 class="mb-0"><?= $customer_report['totals']['customer_count'] ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-users text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Invoices</h6>
                                    <h4 class="mb-0"><?= $customer_report['totals']['invoice_count'] ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-file-invoice text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Revenue</h6>
                                    <h4 class="mb-0"><?= format_currency($customer_report['totals']['total_spent']) ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-dollar-sign text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Customer Data Table -->
            <div class="card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Customer Data</h5>
                    <a href="<?= base_url('reports/export?type=customers&start_date=' . $start_date . '&end_date=' . $end_date) ?>" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-download me-1"></i> Export CSV
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($customer_report['data'])): ?>
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No customer data available for the selected period.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Contact</th>
                                        <th class="text-end">Invoices</th>
                                        <th class="text-end">Total Spent</th>
                                        <th>Last Visit</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($customer_report['data'] as $row): ?>
                                        <tr>
                                            <td><?= $row['customer_name'] ?></td>
                                            <td>
                                                <div><?= $row['customer_email'] ?></div>
                                                <div class="small text-muted"><?= $row['phone'] ?></div>
                                            </td>
                                            <td class="text-end"><?= $row['invoice_count'] ?></td>
                                            <td class="text-end"><?= format_currency($row['total_spent']) ?></td>
                                            <td><?= $row['last_visit'] ? format_date($row['last_visit']) : 'N/A' ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="2">Total</th>
                                        <th class="text-end"><?= $customer_report['totals']['invoice_count'] ?></th>
                                        <th class="text-end"><?= format_currency($customer_report['totals']['total_spent']) ?></th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Top Customers Chart -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Top Customers by Spending</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($customer_report['data'])): ?>
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No customer data available for the selected period.</p>
                        </div>
                    <?php else: ?>
                        <canvas id="customerChart" height="300"></canvas>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Customer Chart
    const customerData = <?= json_encode($customer_report['data']) ?>;
    
    if (customerData.length > 0) {
        // Sort data by total spent in descending order
        customerData.sort((a, b) => b.total_spent - a.total_spent);
        
        // Take top 10 customers
        const topCustomers = customerData.slice(0, 10);
        
        const customerLabels = topCustomers.map(item => item.customer_name);
        const spendingData = topCustomers.map(item => item.total_spent);
        const invoiceData = topCustomers.map(item => item.invoice_count);
        
        const customerCtx = document.getElementById('customerChart').getContext('2d');
        new Chart(customerCtx, {
            type: 'bar',
            data: {
                labels: customerLabels,
                datasets: [
                    {
                        label: 'Total Spent',
                        data: spendingData,
                        backgroundColor: 'rgba(52, 152, 219, 0.7)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 1,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Invoices',
                        data: invoiceData,
                        backgroundColor: 'rgba(46, 204, 113, 0.7)',
                        borderColor: 'rgba(46, 204, 113, 1)',
                        borderWidth: 1,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Total Spent'
                        },
                        ticks: {
                            callback: function(value) {
                                return '<?= CURRENCY_SYMBOL ?>' + value;
                            }
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Invoices'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                if (context.dataset.label === 'Total Spent') {
                                    return context.dataset.label + ': <?= CURRENCY_SYMBOL ?>' + context.raw;
                                } else {
                                    return context.dataset.label + ': ' + context.raw;
                                }
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>
