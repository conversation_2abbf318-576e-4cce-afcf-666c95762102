<?php
/**
 * Staff Controller
 */
class StaffController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            $this->redirect(base_url('login'));
        }

        // Check if user has permission
        if (!has_role(['admin', 'manager'])) {
            flash('error', 'You do not have permission to access staff management');
            $this->redirect(base_url('dashboard'));
        }
    }

    /**
     * Display all staff members
     */
    public function index() {
        // Get all staff members
        $staff_model = new StaffModel();
        $staff = $staff_model->getAllWithUsers();

        // Render view
        $this->render('staff/index', [
            'staff' => $staff
        ]);
    }

    /**
     * Display create staff form
     */
    public function create() {
        // Get services
        $service_model = new ServiceModel();
        $services = $service_model->all();

        // Render view
        $this->render('staff/create', [
            'services' => $services
        ]);
    }

    /**
     * Store new staff member
     */
    public function store() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('staff'));
        }

        // Get form data
        $name = input('name');
        $email = input('email');
        $password = input('password');
        $confirm_password = input('confirm_password');
        $role = input('role');
        $phone = input('phone');
        $address = input('address');
        $position = input('position');
        $salary = input('salary');
        $commission_rate = input('commission_rate');
        $joining_date = input('joining_date');
        $services = input('services');

        // Validate data
        if (empty($name) || empty($email) || empty($password) || empty($role)) {
            flash('error', 'Please fill in all required fields');
            $this->redirect(base_url('staff/create'));
        }

        if ($password != $confirm_password) {
            flash('error', 'Passwords do not match');
            $this->redirect(base_url('staff/create'));
        }

        // Check if email already exists
        $user_model = new UserModel();
        if ($user_model->emailExists($email)) {
            flash('error', 'Email already exists');
            $this->redirect(base_url('staff/create'));
        }

        // Create user
        $user_data = [
            'name' => $name,
            'email' => $email,
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'role' => $role,
            'status' => 'active'
        ];

        $user_id = $user_model->create($user_data);

        if (!$user_id) {
            flash('error', 'Failed to create user');
            $this->redirect(base_url('staff/create'));
        }

        // Create staff
        $staff_model = new StaffModel();
        $staff_data = [
            'user_id' => $user_id,
            'phone' => $phone,
            'address' => $address,
            'position' => $position,
            'salary' => $salary,
            'commission_rate' => $commission_rate,
            'joining_date' => $joining_date
        ];

        $staff_id = $staff_model->create($staff_data);

        if (!$staff_id) {
            // Delete user if staff creation fails
            $user_model->delete($user_id);
            flash('error', 'Failed to create staff member');
            $this->redirect(base_url('staff/create'));
        }

        // Add services
        if (!empty($services)) {
            foreach ($services as $service_id) {
                $staff_model->addService($staff_id, $service_id);
            }
        }

        flash('success', 'Staff member created successfully');
        $this->redirect(base_url('staff'));
    }

    /**
     * Display edit staff form
     */
    public function edit($id) {
        // Get staff member
        $staff_model = new StaffModel();
        $staff = $staff_model->getWithUser($id);

        if (!$staff) {
            flash('error', 'Staff member not found');
            $this->redirect(base_url('staff'));
        }

        // Get services
        $service_model = new ServiceModel();
        $services = $service_model->all();

        // Get staff services
        $staff_services = $staff_model->getServices($id);
        $staff_service_ids = array_column($staff_services, 'service_id');

        // Render view
        $this->render('staff/edit', [
            'staff' => $staff,
            'services' => $services,
            'staff_service_ids' => $staff_service_ids
        ]);
    }

    /**
     * Update staff member
     */
    public function update($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('staff'));
        }

        // Get staff member
        $staff_model = new StaffModel();
        $staff = $staff_model->getWithUser($id);

        if (!$staff) {
            flash('error', 'Staff member not found');
            $this->redirect(base_url('staff'));
        }

        // Get form data
        $name = input('name');
        $email = input('email');
        $password = input('password');
        $confirm_password = input('confirm_password');
        $role = input('role');
        $status = input('status');
        $phone = input('phone');
        $address = input('address');
        $position = input('position');
        $salary = input('salary');
        $commission_rate = input('commission_rate');
        $joining_date = input('joining_date');
        $services = input('services');

        // Validate data
        if (empty($name) || empty($email) || empty($role)) {
            flash('error', 'Please fill in all required fields');
            $this->redirect(base_url('staff/edit/' . $id));
        }

        // Check if email already exists (excluding current user)
        $user_model = new UserModel();
        if ($email != $staff['email'] && $user_model->emailExists($email)) {
            flash('error', 'Email already exists');
            $this->redirect(base_url('staff/edit/' . $id));
        }

        // Update user
        $user_data = [
            'name' => $name,
            'email' => $email,
            'role' => $role,
            'status' => $status
        ];

        // Update password if provided
        if (!empty($password)) {
            if ($password != $confirm_password) {
                flash('error', 'Passwords do not match');
                $this->redirect(base_url('staff/edit/' . $id));
            }

            $user_data['password'] = password_hash($password, PASSWORD_DEFAULT);
        }

        $user_model->update($staff['user_id'], $user_data);

        // Update staff
        $staff_data = [
            'phone' => $phone,
            'address' => $address,
            'position' => $position,
            'salary' => $salary,
            'commission_rate' => $commission_rate,
            'joining_date' => $joining_date
        ];

        $staff_model->update($id, $staff_data);

        // Update services
        // First, get current services
        $current_services = $staff_model->getServices($id);
        $current_service_ids = array_column($current_services, 'service_id');

        // Add new services
        if (!empty($services)) {
            foreach ($services as $service_id) {
                if (!in_array($service_id, $current_service_ids)) {
                    $staff_model->addService($id, $service_id);
                }
            }
        } else {
            $services = [];
        }

        // Remove services that are no longer selected
        foreach ($current_service_ids as $service_id) {
            if (!in_array($service_id, $services)) {
                $staff_model->removeService($id, $service_id);
            }
        }

        flash('success', 'Staff member updated successfully');
        $this->redirect(base_url('staff'));
    }

    /**
     * Delete staff member
     */
    public function delete($id) {
        // Get staff member
        $staff_model = new StaffModel();
        $staff = $staff_model->getWithUser($id);

        if (!$staff) {
            flash('error', 'Staff member not found');
            $this->redirect(base_url('staff'));
        }

        // Prevent deletion of default admin user (ID 1)
        if ($staff['user_id'] == 1) {
            flash('error', 'The default administrator account cannot be deleted');
            $this->redirect(base_url('staff'));
        }

        // Delete staff
        $staff_model->delete($id);

        // Delete user
        $user_model = new UserModel();
        $user_model->delete($staff['user_id']);

        flash('success', 'Staff member deleted successfully');
        $this->redirect(base_url('staff'));
    }

    /**
     * View staff member
     */
    public function view($id) {
        // Get staff member
        $staff_model = new StaffModel();
        $staff = $staff_model->getWithUser($id);

        if (!$staff) {
            flash('error', 'Staff member not found');
            $this->redirect(base_url('staff'));
        }

        // Get staff services
        $staff_services = $staff_model->getServices($id);

        // Get payroll history
        $payroll_model = new PayrollModel();
        $payroll_history = $payroll_model->getByStaffId($id);

        // Render view
        $this->render('staff/view', [
            'staff' => $staff,
            'services' => $staff_services,
            'payroll_history' => $payroll_history
        ]);
    }
}
