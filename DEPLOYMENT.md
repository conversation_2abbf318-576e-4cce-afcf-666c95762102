# Salon Management System Deployment Guide

This guide will help you deploy the Salon Management System on both local and live servers.

## Local Development Environment

### Requirements
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache web server with mod_rewrite enabled

### Setup Steps
1. Clone or download the repository to your local web server directory
2. Create a MySQL database named `salon_db`
3. Import the database schema from `database/salon_db.sql`
4. Make sure the application can write to the `uploads` directory
5. Access the application at `http://localhost/path-to-your-app`
6. Login with the default admin credentials:
   - Email: <EMAIL>
   - Password: admin123

## Live Server Deployment

### Requirements
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache web server with mod_rewrite enabled
- SSH or FTP access to your server

### Setup Steps

1. **Prepare Your Database**
   - Create a new MySQL database on your server
   - Create a database user with all privileges on this database
   - Note down the database name, username, and password

2. **Upload Files**
   - Upload all files to your web server using FTP or SSH
   - Make sure to maintain the directory structure

3. **Configure Database Connection**
   - Open `config/config.php`
   - Update the database connection settings in the production section:
     ```php
     // Live server database configuration
     define('DB_HOST', 'localhost');
     define('DB_NAME', 'your_live_db_name'); // Change this
     define('DB_USER', 'your_live_db_user'); // Change this
     define('DB_PASS', 'your_live_db_password'); // Change this
     ```

4. **Import Database**
   - Import the database schema using phpMyAdmin or MySQL command line:
     ```
     mysql -u your_username -p your_database < database/salon_db.sql
     ```

5. **Set File Permissions**
   - Make sure the `uploads` directory is writable:
     ```
     chmod -R 755 uploads
     ```

6. **Configure .htaccess**
   - If your application is in the root directory, no changes are needed
   - If your application is in a subdirectory, uncomment and update the RewriteBase:
     ```
     RewriteBase /your-subdirectory/
     ```

7. **Test the Application**
   - Access your application at your domain (e.g., https://yourdomain.com)
   - Login with the default admin credentials:
     - Email: <EMAIL>
     - Password: admin123
   - Change the default password immediately after login

## Troubleshooting

### URL Rewriting Issues
If you're experiencing URL rewriting issues:
1. Make sure mod_rewrite is enabled on your server
2. Check that .htaccess files are allowed (AllowOverride All)
3. Verify the RewriteBase is set correctly in .htaccess

### Database Connection Issues
If you're having trouble connecting to the database:
1. Verify your database credentials
2. Check if your database server allows connections from your web server
3. Make sure the database user has the necessary privileges

### File Permission Issues
If you're experiencing file upload or write issues:
1. Check the permissions on the uploads directory
2. Make sure the web server user has write access
3. Try setting permissions to 755 for directories and 644 for files

## Security Recommendations

1. **Change Default Credentials**
   - Change the default admin password immediately after installation
   - Create a new admin account with a different email

2. **Secure Configuration Files**
   - Make sure your configuration files are not accessible from the web
   - The .htaccess file already includes rules to protect sensitive directories

3. **Enable HTTPS**
   - Use HTTPS for your live site
   - Obtain an SSL certificate (Let's Encrypt offers free certificates)
   - Update your site configuration to use HTTPS

4. **Regular Backups**
   - Set up regular backups of your database and files
   - Test your backup restoration process

## Updating the Application

When updating the application:
1. Back up your database and files
2. Upload the new files
3. Run any necessary database migrations
4. Test the application thoroughly

## Support

If you need help with deployment, please contact <NAME_EMAIL>.
