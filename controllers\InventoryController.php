<?php
/**
 * Inventory Controller
 */
class InventoryController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            flash('error', 'Please login to access this page');
            header('Location: ' . base_url('login'));
            exit;
        }

        // Check if user has admin, manager, or staff role
        if (!has_role(['admin', 'manager', 'staff'])) {
            flash('error', 'You do not have permission to access this page');
            header('Location: ' . base_url());
            exit;
        }
    }


    /**
     * Display all products
     */
    public function index() {
        // Get filter parameters
        $category_id = input('category_id');
        $status = input('status');
        $search = input('search');

        // Get products
        $product_model = new ProductModel();
        if ($search) {
            $products = $product_model->search($search);
        } else {
            $products = $product_model->getAllWithCategories($category_id, $status);
        }

        // Get product categories
        $category_model = new ProductCategoryModel();
        $categories = $category_model->all();

        // Get low stock products
        $low_stock_products = $product_model->getLowStockProducts();

        // Render view
        $this->render('inventory/index', [
            'products' => $products,
            'categories' => $categories,
            'low_stock_products' => $low_stock_products,
            'selected_category' => $category_id,
            'selected_status' => $status,
            'search' => $search
        ]);
    }

    /**
     * Display product creation form
     */
    public function create() {
        // Get product categories
        $category_model = new ProductCategoryModel();
        $categories = $category_model->all();

        // Render view
        $this->render('inventory/create', [
            'categories' => $categories
        ]);
    }

    /**
     * Store new product
     */
    public function store() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('inventory/create'));
        }

        // Get form data
        $category_id = input('category_id');
        $name = input('name');
        $sku = input('sku');
        $brand = input('brand');
        $description = input('description');
        $price = input('price');
        $purchase_price = input('purchase_price');
        $quantity = input('quantity');
        $low_stock_threshold = input('low_stock_threshold');
        $tax_rate = input('tax_rate');
        $supplier = input('supplier');
        $expiry_date = input('expiry_date');
        $status = input('status');

        // Validate form data
        if (empty($name) || empty($price) || empty($quantity)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('inventory/create'));
        }

        // Create product
        $product_model = new ProductModel();
        $product_data = [
            'category_id' => $category_id ?: 1, // Default to first category if none selected
            'name' => $name,
            'sku' => $sku,
            'brand' => $brand,
            'description' => $description,
            'selling_price' => $price,
            'cost_price' => $purchase_price,
            'quantity' => $quantity,
            'low_stock_threshold' => $low_stock_threshold,
            'tax_rate' => $tax_rate,
            'supplier' => $supplier,
            'expiry_date' => $expiry_date ? $expiry_date : null,
            'status' => $status,
            'created_by' => $_SESSION['user_id'] ?? null
        ];

        $product_id = $product_model->create($product_data);

        if (!$product_id) {
            flash('error', 'Failed to create product');
            $this->redirect(base_url('inventory/create'));
        }

        // Handle image upload
        if (isset($_FILES['image']) && $_FILES['image']['error'] == UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/products/';

            // Create directory if it doesn't exist
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            $file_name = $product_id . '_' . time() . '_' . $_FILES['image']['name'];
            $upload_path = $upload_dir . $file_name;

            if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                // Update product with image filename only
                $product_model->update($product_id, ['image' => $file_name]);
            }
        }

        flash('success', 'Product created successfully');
        $this->redirect(base_url('inventory'));
    }

    /**
     * Display product details
     */
    public function view($id) {
        // Get product
        $product_model = new ProductModel();
        $product = $product_model->getWithCategory($id);

        if (!$product) {
            flash('error', 'Product not found');
            $this->redirect(base_url('inventory'));
        }

        // Get product stock history
        $stock_model = new StockModel();
        $stock_history = $stock_model->getByProductId($id);

        // Render view
        $this->render('inventory/view', [
            'product' => $product,
            'stock_history' => $stock_history
        ]);
    }

    /**
     * Display product edit form
     */
    public function edit($id) {
        // Get product
        $product_model = new ProductModel();
        $product = $product_model->find($id);

        if (!$product) {
            flash('error', 'Product not found');
            $this->redirect(base_url('inventory'));
        }

        // Get product categories
        $category_model = new ProductCategoryModel();
        $categories = $category_model->all();

        // Render view
        $this->render('inventory/edit', [
            'product' => $product,
            'categories' => $categories
        ]);
    }

    /**
     * Update product
     */
    public function update($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('inventory/edit/' . $id));
        }

        // Get product
        $product_model = new ProductModel();
        $product = $product_model->find($id);

        if (!$product) {
            flash('error', 'Product not found');
            $this->redirect(base_url('inventory'));
        }

        // Get form data
        $category_id = input('category_id');
        $name = input('name');
        $sku = input('sku');
        $brand = input('brand');
        $description = input('description');
        $price = input('price');
        $purchase_price = input('purchase_price');
        $quantity = input('quantity');
        $low_stock_threshold = input('low_stock_threshold');
        $tax_rate = input('tax_rate');
        $supplier = input('supplier');
        $expiry_date = input('expiry_date');
        $status = input('status');

        // Validate form data
        if (empty($name) || empty($price) || empty($quantity)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('inventory/edit/' . $id));
        }

        // Check if quantity has changed
        $old_quantity = $product['quantity'];
        $quantity_change = $quantity - $old_quantity;

        // Update product
        $product_data = [
            'category_id' => $category_id ?: 1, // Default to first category if none selected
            'name' => $name,
            'sku' => $sku,
            'brand' => $brand,
            'description' => $description,
            'selling_price' => $price,
            'cost_price' => $purchase_price,
            'quantity' => $quantity,
            'low_stock_threshold' => $low_stock_threshold,
            'tax_rate' => $tax_rate,
            'supplier' => $supplier,
            'expiry_date' => $expiry_date ? $expiry_date : null,
            'status' => $status
        ];

        $result = $product_model->update($id, $product_data);

        if (!$result) {
            flash('error', 'Failed to update product');
            $this->redirect(base_url('inventory/edit/' . $id));
        }

        // Handle image upload
        if (isset($_FILES['image']) && $_FILES['image']['error'] == UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/products/';

            // Create directory if it doesn't exist
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            $file_name = $id . '_' . time() . '_' . $_FILES['image']['name'];
            $upload_path = $upload_dir . $file_name;

            if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                // Delete old image if exists
                if (!empty($product['image']) && file_exists('uploads/products/' . $product['image'])) {
                    unlink('uploads/products/' . $product['image']);
                }

                // Update product with image filename only
                $product_model->update($id, ['image' => $file_name]);
            }
        }

        // Record stock change if quantity has changed
        if ($quantity_change != 0) {
            $stock_model = new StockModel();
            $stock_data = [
                'product_id' => $id,
                'quantity_change' => $quantity_change,
                'reason' => 'Manual adjustment',
                'user_id' => $_SESSION['user_id']
            ];

            $stock_model->create($stock_data);
        }

        flash('success', 'Product updated successfully');
        $this->redirect(base_url('inventory'));
    }

    /**
     * Delete product
     */
    public function delete($id) {
        // Get product
        $product_model = new ProductModel();
        $product = $product_model->find($id);

        if (!$product) {
            flash('error', 'Product not found');
            $this->redirect(base_url('inventory'));
        }

        // Check if product is used in invoices
        $invoice_product_model = new InvoiceProductModel();
        $invoice_products = $invoice_product_model->findByProductId($id);

        if (count($invoice_products) > 0) {
            flash('error', 'Cannot delete product that is used in invoices');
            $this->redirect(base_url('inventory'));
        }

        // Delete product image if exists
        if (!empty($product['image']) && file_exists($product['image'])) {
            unlink($product['image']);
        }

        // Delete product
        $result = $product_model->delete($id);

        if (!$result) {
            flash('error', 'Failed to delete product');
            $this->redirect(base_url('inventory'));
        }

        flash('success', 'Product deleted successfully');
        $this->redirect(base_url('inventory'));
    }

    /**
     * Display stock adjustment form
     */
    public function adjustStock($id) {
        // Get product
        $product_model = new ProductModel();
        $product = $product_model->getWithCategory($id);

        if (!$product) {
            flash('error', 'Product not found');
            $this->redirect(base_url('inventory'));
        }

        // Render view
        $this->render('inventory/adjust_stock', [
            'product' => $product
        ]);
    }

    /**
     * Process stock adjustment
     */
    public function updateStock($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('inventory/adjust-stock/' . $id));
        }

        // Get product
        $product_model = new ProductModel();
        $product = $product_model->find($id);

        if (!$product) {
            flash('error', 'Product not found');
            $this->redirect(base_url('inventory'));
        }

        // Get form data
        $adjustment_type = input('adjustment_type');
        $quantity = input('quantity');
        $reason = input('reason');

        // Validate form data
        if (empty($adjustment_type) || empty($quantity) || empty($reason)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('inventory/adjust-stock/' . $id));
        }

        // Calculate new quantity
        $old_quantity = $product['quantity'];
        $new_quantity = $old_quantity;
        $quantity_change = 0;

        if ($adjustment_type == 'add') {
            $new_quantity = $old_quantity + $quantity;
            $quantity_change = $quantity;
        } else if ($adjustment_type == 'subtract') {
            if ($quantity > $old_quantity) {
                flash('error', 'Cannot subtract more than current quantity');
                $this->redirect(base_url('inventory/adjust-stock/' . $id));
            }

            $new_quantity = $old_quantity - $quantity;
            $quantity_change = -$quantity;
        } else if ($adjustment_type == 'set') {
            $new_quantity = $quantity;
            $quantity_change = $quantity - $old_quantity;
        }

        // Update product quantity
        $result = $product_model->updateQuantity($id, $new_quantity);

        if (!$result) {
            flash('error', 'Failed to update product quantity');
            $this->redirect(base_url('inventory/adjust-stock/' . $id));
        }

        // Record stock change
        $stock_model = new StockModel();
        $stock_data = [
            'product_id' => $id,
            'quantity_change' => $quantity_change,
            'reason' => $reason,
            'user_id' => $_SESSION['user_id']
        ];

        $stock_model->create($stock_data);

        flash('success', 'Product stock updated successfully');
        $this->redirect(base_url('inventory/view/' . $id));
    }

    /**
     * Display product categories
     */
    public function categories() {
        // Get categories
        $category_model = new ProductCategoryModel();
        $categories = $category_model->all();

        // Get product counts for each category
        $product_model = new ProductModel();
        foreach ($categories as &$category) {
            $category['product_count'] = $product_model->countByCategoryId($category['id']);
        }

        // Render view
        $this->render('inventory/categories', [
            'categories' => $categories
        ]);
    }

    /**
     * Store new product category
     */
    public function storeCategory() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('inventory/categories'));
        }

        // Get form data
        $name = input('name');
        $description = input('description');

        // Validate form data
        if (empty($name)) {
            flash('error', 'Please enter category name');
            $this->redirect(base_url('inventory/categories'));
        }

        // Create category
        $category_model = new ProductCategoryModel();
        $category_data = [
            'name' => $name,
            'description' => $description
        ];

        $category_id = $category_model->create($category_data);

        if (!$category_id) {
            flash('error', 'Failed to create category');
            $this->redirect(base_url('inventory/categories'));
        }

        flash('success', 'Category created successfully');
        $this->redirect(base_url('inventory/categories'));
    }

    /**
     * Store new product category via AJAX
     */
    public function storeCategoryAjax() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            exit;
        }

        // Get form data
        $name = input('name');
        $description = input('description');

        // Validate data
        if (empty($name)) {
            echo json_encode(['success' => false, 'message' => 'Category name is required']);
            exit;
        }

        // Create category
        $category_model = new ProductCategoryModel();
        $category_data = [
            'name' => $name,
            'description' => $description
        ];

        $category_id = $category_model->create($category_data);

        if (!$category_id) {
            echo json_encode(['success' => false, 'message' => 'Failed to create category']);
            exit;
        }

        // Get the newly created category
        $category = $category_model->find($category_id);

        echo json_encode([
            'success' => true,
            'message' => 'Category created successfully',
            'category' => $category
        ]);
        exit;
    }

    /**
     * Export products to CSV
     */
    public function export() {
        // Get all products with category names
        $product_model = new ProductModel();
        $products = $product_model->getAllWithCategories();

        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="products_export_' . date('Y-m-d') . '.csv"');

        // Create a file pointer
        $output = fopen('php://output', 'w');

        // Set column headers
        fputcsv($output, [
            'ID', 'Name', 'SKU', 'Category', 'Brand', 'Description',
            'Purchase Price', 'Selling Price', 'Quantity', 'Low Stock Threshold',
            'Tax Rate', 'Supplier', 'Expiry Date', 'Status'
        ]);

        // Output each product as a row
        foreach ($products as $product) {
            fputcsv($output, [
                $product['id'],
                $product['name'],
                $product['sku'],
                $product['category_name'],
                $product['brand'],
                $product['description'],
                $product['cost_price'],
                $product['selling_price'],
                $product['quantity'],
                $product['low_stock_threshold'],
                $product['tax_rate'],
                $product['supplier'],
                $product['expiry_date'],
                $product['status']
            ]);
        }

        // Close the file pointer
        fclose($output);
        exit;
    }

    /**
     * Download CSV template for product import
     */
    public function downloadTemplate() {
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="products_import_template.csv"');

        // Create a file pointer
        $output = fopen('php://output', 'w');

        // Set column headers
        fputcsv($output, [
            'Name', 'SKU', 'Category', 'Brand', 'Description',
            'Purchase Price', 'Selling Price', 'Quantity', 'Low Stock Threshold',
            'Tax Rate', 'Supplier', 'Expiry Date', 'Status'
        ]);

        // Add a sample row
        fputcsv($output, [
            'Sample Product', 'SKU-123', 'Hair Care', 'Brand Name', 'Product description',
            '80.00', '120.00', '10', '5', '18', 'Supplier Name', date('Y-m-d', strtotime('+1 year')), 'active'
        ]);

        // Close the file pointer
        fclose($output);
        exit;
    }

    /**
     * Import products from CSV
     */
    public function import() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('inventory'));
        }

        // Check if file is uploaded
        if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] != 0) {
            flash('error', 'Please select a valid CSV file');
            $this->redirect(base_url('inventory'));
        }

        // Check file type
        $file_info = pathinfo($_FILES['import_file']['name']);
        if ($file_info['extension'] != 'csv') {
            flash('error', 'Please upload a CSV file');
            $this->redirect(base_url('inventory'));
        }

        // Open the file
        $file = fopen($_FILES['import_file']['tmp_name'], 'r');

        // Read the header row
        $header = fgetcsv($file);

        // Initialize counters
        $imported = 0;
        $updated = 0;
        $skipped = 0;
        $errors = [];

        // Get category model
        $category_model = new ProductCategoryModel();
        $product_model = new ProductModel();

        // Process each row
        while (($row = fgetcsv($file)) !== false) {
            // Skip empty rows
            if (empty($row[0])) {
                continue;
            }

            // Map CSV columns to product data
            $product_data = [
                'name' => trim($row[0]),
                'sku' => !empty($row[1]) ? trim($row[1]) : 'PRD-' . rand(1000, 9999) . substr(time(), -4),
                'brand' => isset($row[3]) ? trim($row[3]) : '',
                'description' => isset($row[4]) ? trim($row[4]) : '',
                'cost_price' => !empty($row[5]) ? trim($row[5]) : 0,
                'selling_price' => !empty($row[6]) ? trim($row[6]) : 0,
                'quantity' => !empty($row[7]) ? trim($row[7]) : 0,
                'min_quantity' => !empty($row[8]) ? trim($row[8]) : 5,
                'tax_rate' => !empty($row[9]) ? trim($row[9]) : 0,
                'supplier' => isset($row[10]) ? trim($row[10]) : '',
                'expiry_date' => !empty($row[11]) ? trim($row[11]) : null,
                'status' => !empty($row[12]) ? trim($row[12]) : 'active'
            ];

            // Handle category
            if (!empty($row[2])) {
                $category_name = trim($row[2]);
                // Check if category exists
                $category = $category_model->findByName($category_name);

                if ($category) {
                    $product_data['category_id'] = $category['id'];
                } else {
                    // Create new category
                    $category_id = $category_model->create([
                        'name' => $category_name,
                        'description' => ''
                    ]);

                    if ($category_id) {
                        $product_data['category_id'] = $category_id;
                    }
                }
            }

            // Validate required fields
            if (empty($product_data['name'])) {
                $skipped++;
                $errors[] = 'Row skipped: Product name is required';
                continue;
            }

            // Check if product with the same name or SKU already exists
            $existing_product = null;

            // First check by SKU if it's provided
            if (!empty($product_data['sku'])) {
                $existing_product = $product_model->findBySku($product_data['sku']);
            }

            // If not found by SKU, check by name
            if (!$existing_product) {
                $existing_product = $product_model->findByName($product_data['name']);
            }

            if ($existing_product) {
                // Update existing product
                $result = $product_model->update($existing_product['id'], $product_data);

                if ($result) {
                    $updated++;
                } else {
                    $skipped++;
                    $errors[] = 'Row skipped: Failed to update product "' . $product_data['name'] . '"';
                }
            } else {
                // Create new product
                $result = $product_model->create($product_data);

                if ($result) {
                    $imported++;
                } else {
                    $skipped++;
                    $errors[] = 'Row skipped: Failed to import product "' . $product_data['name'] . '"';
                }
            }
        }

        // Close the file
        fclose($file);

        // Set flash message
        if ($imported > 0 || $updated > 0) {
            flash('success', $imported . ' products imported, ' . $updated . ' products updated' . ($skipped > 0 ? ', ' . $skipped . ' skipped' : ''));
        } else {
            flash('error', 'No products were imported or updated' . ($skipped > 0 ? ', ' . $skipped . ' skipped' : ''));
        }

        // Log errors
        if (!empty($errors)) {
            error_log('Product import errors: ' . implode(', ', $errors));
        }

        $this->redirect(base_url('inventory'));
    }

    /**
     * Update product category
     */
    public function updateCategory($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('inventory/categories'));
        }

        // Get category
        $category_model = new ProductCategoryModel();
        $category = $category_model->find($id);

        if (!$category) {
            flash('error', 'Category not found');
            $this->redirect(base_url('inventory/categories'));
        }

        // Get form data
        $name = input('name');
        $description = input('description');

        // Validate form data
        if (empty($name)) {
            flash('error', 'Please enter category name');
            $this->redirect(base_url('inventory/categories'));
        }

        // Update category
        $category_data = [
            'name' => $name,
            'description' => $description
        ];

        $result = $category_model->update($id, $category_data);

        if (!$result) {
            flash('error', 'Failed to update category');
            $this->redirect(base_url('inventory/categories'));
        }

        flash('success', 'Category updated successfully');
        $this->redirect(base_url('inventory/categories'));
    }

    /**
     * Delete product category
     */
    public function deleteCategory($id) {
        // Get category
        $category_model = new ProductCategoryModel();
        $category = $category_model->find($id);

        if (!$category) {
            flash('error', 'Category not found');
            $this->redirect(base_url('inventory/categories'));
        }

        // Check if category has products
        $product_model = new ProductModel();
        $products = $product_model->findBy('category_id', $id);

        if (count($products) > 0) {
            flash('error', 'Cannot delete category with products');
            $this->redirect(base_url('inventory/categories'));
        }

        // Delete category
        $result = $category_model->delete($id);

        if (!$result) {
            flash('error', 'Failed to delete category');
            $this->redirect(base_url('inventory/categories'));
        }

        flash('success', 'Category deleted successfully');
        $this->redirect(base_url('inventory/categories'));
    }
}
