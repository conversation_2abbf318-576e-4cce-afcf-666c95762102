<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-cogs"></i> Settings</h1>
    </div>

    <div class="row">
        <!-- Settings Navigation -->
        <div class="col-md-3 mb-4">
            <?php include 'partials/settings_nav.php'; ?>
        </div>

        <!-- Settings Content -->
        <div class="col-md-9">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Salon's Settings</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('settings/update-general') ?>" method="post" enctype="multipart/form-data">
                        <div class="row">
                            <!-- Salon Name -->
                            <div class="col-md-6 mb-3">
                                <label for="salon_name" class="form-label">Salon Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="salon_name" name="salon_name" value="<?= $settings['salon_name'] ?? '' ?>" required>
                                <div class="form-text">This name will appear in the top-left corner of the application and in the browser title.</div>
                            </div>

                            <!-- Email -->
                            <div class="col-md-6 mb-3">
                                <label for="salon_email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="salon_email" name="salon_email" value="<?= $settings['salon_email'] ?? $settings['email'] ?? '' ?>">
                            </div>

                            <!-- Phone -->
                            <div class="col-md-6 mb-3">
                                <label for="salon_phone" class="form-label">Phone</label>
                                <input type="text" class="form-control" id="salon_phone" name="salon_phone" value="<?= $settings['salon_phone'] ?? $settings['phone'] ?? '' ?>">
                            </div>

                            <!-- Address -->
                            <div class="col-md-6 mb-3">
                                <label for="salon_address" class="form-label">Address</label>
                                <textarea class="form-control" id="salon_address" name="salon_address" rows="3"><?= $settings['salon_address'] ?? $settings['address'] ?? '' ?></textarea>
                            </div>

                            <!-- Website -->
                            <div class="col-md-6 mb-3">
                                <label for="salon_website" class="form-label">Website</label>
                                <input type="text" class="form-control" id="salon_website" name="salon_website" value="<?= $settings['salon_website'] ?? '' ?>">
                            </div>

                            <!-- Tax Rate -->
                            <div class="col-md-4 mb-3">
                                <label for="tax_rate" class="form-label">Tax Rate (%)</label>
                                <input type="number" class="form-control" id="tax_rate" name="tax_rate" min="0" max="100" step="0.01" value="<?= $settings['tax_rate'] ?? '0' ?>">
                            </div>

                            <!-- Currency -->
                            <div class="col-md-4 mb-3">
                                <label for="currency" class="form-label">Currency</label>
                                <select class="form-select" id="currency" name="currency">
                                    <option value="USD" <?= ($settings['currency'] ?? '') == 'USD' ? 'selected' : '' ?>>US Dollar (USD)</option>
                                    <option value="EUR" <?= ($settings['currency'] ?? '') == 'EUR' ? 'selected' : '' ?>>Euro (EUR)</option>
                                    <option value="GBP" <?= ($settings['currency'] ?? '') == 'GBP' ? 'selected' : '' ?>>British Pound (GBP)</option>
                                    <option value="INR" <?= ($settings['currency'] ?? '') == 'INR' ? 'selected' : '' ?>>Indian Rupee (INR)</option>
                                    <option value="AUD" <?= ($settings['currency'] ?? '') == 'AUD' ? 'selected' : '' ?>>Australian Dollar (AUD)</option>
                                    <option value="CAD" <?= ($settings['currency'] ?? '') == 'CAD' ? 'selected' : '' ?>>Canadian Dollar (CAD)</option>
                                    <option value="SGD" <?= ($settings['currency'] ?? '') == 'SGD' ? 'selected' : '' ?>>Singapore Dollar (SGD)</option>
                                    <option value="JPY" <?= ($settings['currency'] ?? '') == 'JPY' ? 'selected' : '' ?>>Japanese Yen (JPY)</option>
                                    <option value="CNY" <?= ($settings['currency'] ?? '') == 'CNY' ? 'selected' : '' ?>>Chinese Yuan (CNY)</option>
                                </select>
                            </div>

                            <!-- Currency Symbol -->
                            <div class="col-md-4 mb-3">
                                <label for="currency_symbol" class="form-label">Currency Symbol</label>
                                <input type="text" class="form-control" id="currency_symbol" name="currency_symbol" value="<?= $settings['currency_symbol'] ?? '$' ?>">
                            </div>

                            <!-- Logo -->
                            <div class="col-md-12 mb-3">
                                <label for="logo" class="form-label">Salon Logo</label>
                                <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                <div class="form-text">Recommended size: 200x80 pixels. Leave empty to keep the current logo.</div>
                                <?php if (!empty($settings['logo_path'])): ?>
                                <div class="mt-2">
                                    <label>Current Logo:</label>
                                    <div>
                                        <img src="<?= base_url($settings['logo_path']) ?>" alt="Current Logo" style="max-height: 60px;">
                                        <input type="hidden" name="current_logo" value="<?= $settings['logo_path'] ?>">
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>

                            <!-- Submit Button -->
                            <div class="col-md-12 mt-3 text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> Save Changes
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update currency symbol when currency changes
    document.getElementById('currency').addEventListener('change', function() {
        const currency = this.value;
        let symbol = '$';

        switch (currency) {
            case 'USD': symbol = '$'; break;
            case 'EUR': symbol = '€'; break;
            case 'GBP': symbol = '£'; break;
            case 'INR': symbol = '₹'; break;
            case 'AUD': symbol = 'A$'; break;
            case 'CAD': symbol = 'C$'; break;
            case 'SGD': symbol = 'S$'; break;
            case 'JPY': symbol = '¥'; break;
            case 'CNY': symbol = '¥'; break;
        }

        document.getElementById('currency_symbol').value = symbol;
    });
});
</script>
