<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-boxes"></i> Adjust Stock</h1>
        <div>
            <a href="<?= base_url('inventory/view/' . $product['id']) ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Product
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Stock Adjustment for <?= $product['name'] ?></h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-3 text-center mb-3 mb-md-0">
                            <?php if (!empty($product['image'])): ?>
                                <img src="<?= base_url('uploads/products/' . $product['image']) ?>" alt="<?= $product['name'] ?>" class="img-fluid rounded" style="max-height: 100px;">
                            <?php else: ?>
                                <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 100px; width: 100px; margin: 0 auto;">
                                    <i class="fas fa-box fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-9">
                            <h5><?= $product['name'] ?></h5>
                            <p class="mb-1"><strong>Category:</strong> <?= $product['category_name'] ?></p>
                            <p class="mb-1"><strong>Current Stock:</strong>
                                <?php if ($product['quantity'] <= 0): ?>
                                    <span class="badge bg-danger">Out of Stock</span>
                                <?php elseif ($product['quantity'] <= $product['low_stock_threshold']): ?>
                                    <span class="badge bg-warning"><?= $product['quantity'] ?> Left</span>
                                <?php else: ?>
                                    <span class="badge bg-success"><?= $product['quantity'] ?> In Stock</span>
                                <?php endif; ?>
                            </p>
                            <p class="mb-0"><strong>Low Stock Threshold:</strong> <?= $product['low_stock_threshold'] ?></p>
                        </div>
                    </div>

                    <form action="<?= base_url('inventory/update-stock/' . $product['id']) ?>" method="post">
                        <div class="mb-3">
                            <label for="adjustment_type" class="form-label">Adjustment Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="adjustment_type" name="adjustment_type" required>
                                <option value="add">Add Stock</option>
                                <option value="subtract">Remove Stock</option>
                                <option value="set">Set Exact Stock</option>
                            </select>
                            <div class="form-text">
                                <ul class="mb-0 ps-3">
                                    <li><strong>Add Stock:</strong> Increases the current stock level</li>
                                    <li><strong>Remove Stock:</strong> Decreases the current stock level</li>
                                    <li><strong>Set Exact Stock:</strong> Sets the stock to a specific value</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="quantity" class="form-label">Quantity <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="quantity" name="quantity" min="0" required>
                            <div class="form-text" id="quantity_help">Enter the quantity to add, remove, or set.</div>
                        </div>

                        <div class="mb-3">
                            <label for="reason" class="form-label">Reason <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="reason" name="reason" rows="3" required></textarea>
                            <div class="form-text">Provide a reason for this stock adjustment (e.g., "New shipment received", "Damaged items", etc.)</div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?= base_url('inventory/view/' . $product['id']) ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const adjustmentTypeSelect = document.getElementById('adjustment_type');
    const quantityInput = document.getElementById('quantity');
    const quantityHelp = document.getElementById('quantity_help');
    const currentStock = <?= $product['quantity'] ?>;

    adjustmentTypeSelect.addEventListener('change', function() {
        updateQuantityHelp();
    });

    function updateQuantityHelp() {
        const adjustmentType = adjustmentTypeSelect.value;

        if (adjustmentType === 'add') {
            quantityHelp.textContent = 'Enter the quantity to add to the current stock.';
            quantityInput.min = 1;
            quantityInput.max = '';
        } else if (adjustmentType === 'subtract') {
            quantityHelp.textContent = `Enter the quantity to remove from the current stock (max: ${currentStock}).`;
            quantityInput.min = 1;
            quantityInput.max = currentStock;
        } else if (adjustmentType === 'set') {
            quantityHelp.textContent = 'Enter the exact quantity to set as the new stock level.';
            quantityInput.min = 0;
            quantityInput.max = '';
        }
    }

    // Initialize help text
    updateQuantityHelp();
});
</script>
