RewriteEngine On

# Set a simple RewriteBase - this should match your installation directory
RewriteBase /projects/SALON5/

# Redirect to index.php if not a file or directory
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Prevent direct access to sensitive files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

<FilesMatch "^(database|config|core|models|controllers|views)/">
    Order allow,deny
    Deny from all
</FilesMatch>

# PHP settings
# For development environment (local server)
<IfDefine DEVELOPMENT>
    php_flag display_errors On
    php_value error_reporting E_ALL
</IfDefine>

# For production environment (live server)
<IfDefine !DEVELOPMENT>
    php_flag display_errors Off
    php_value error_reporting 0
</IfDefine>

# Common settings for both environments
php_value max_execution_time 300
php_value max_input_time 300
php_value upload_max_filesize 10M
php_value post_max_size 10M

# Add these lines to ensure proper character encoding
AddDefaultCharset UTF-8
<IfModule mod_php7.c>
    php_value default_charset "UTF-8"
</IfModule>
<IfModule mod_php8.c>
    php_value default_charset "UTF-8"
</IfModule>

# Prevent redirect loops
RewriteCond %{ENV:REDIRECT_STATUS} 200
RewriteRule .* - [L]

