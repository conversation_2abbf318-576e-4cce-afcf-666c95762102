<?php
// Define a helper function to get customer status safely
function getCustomerStatus($customer) {
    return isset($customer['status']) ? $customer['status'] : 'active';
}

// Use this function throughout the file
$customerStatus = getCustomerStatus($customer);
?>

<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-user"></i> Customer Profile</h1>
        <div>
            <a href="<?= base_url('customers') ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Customers
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Customer Details -->
        <div class="col-md-4 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Personal Information</h5>
                    <div>
                        <?php if ($customerStatus == 'active'): ?>
                            <span class="badge bg-success">Active</span>
                        <?php elseif ($customerStatus == 'inactive'): ?>
                            <span class="badge bg-danger">Inactive</span>
                        <?php else: ?>
                            <span class="badge bg-secondary">Unknown</span>
                        <?php endif; ?>
                        
                        <?php
                        // Check if customer has an active membership
                        $membership_model = new MembershipModel();
                        $membership = $membership_model->getActiveMembershipByCustomerId($customer['id']);
                        if ($membership): ?>
                            <span class="badge bg-warning ms-1" title="<?= $membership['membership_name'] ?>">
                                <i class="fas fa-crown"></i> <?= $membership['membership_name'] ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar-circle bg-primary text-white mx-auto mb-3" style="width: 100px; height: 100px; font-size: 2.5rem;">
                            <?= strtoupper(substr($customer['name'], 0, 1)) ?>
                        </div>
                        <h4>
                            <?= $customer['name'] ?>
                            <?php if ($membership): ?>
                                <i class="fas fa-crown text-warning" title="<?= $membership['membership_name'] ?>"></i>
                            <?php endif; ?>
                        </h4>
                        <p class="text-muted">Customer since <?= date('M Y', strtotime($customer['created_at'])) ?></p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Contact Information</h6>
                        <div class="d-flex align-items-center mb-2">
                            <div class="icon-circle bg-primary-light text-primary me-3">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <div class="text-muted small">Email</div>
                                <div><?= $customer['email'] ?></div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="icon-circle bg-primary-light text-primary me-3">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div>
                                <div class="text-muted small">Phone</div>
                                <div><?= $customer['phone'] ?></div>
                            </div>
                        </div>
                        <?php if (!empty($customer['address'])): ?>
                            <div class="d-flex align-items-center mb-2">
                                <div class="icon-circle bg-primary-light text-primary me-3">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">Address</div>
                                    <div><?= $customer['address'] ?></div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Personal Details</h6>
                        <div class="d-flex align-items-center mb-2">
                            <div class="icon-circle bg-primary-light text-primary me-3">
                                <i class="fas fa-venus-mars"></i>
                            </div>
                            <div>
                                <div class="text-muted small">Gender</div>
                                <div><?= ucfirst($customer['gender']) ?></div>
                            </div>
                        </div>
                        <?php if (!empty($customer['date_of_birth'])): ?>
                            <div class="d-flex align-items-center mb-2">
                                <div class="icon-circle bg-primary-light text-primary me-3">
                                    <i class="fas fa-birthday-cake"></i>
                                </div>
                                <div>
                                    <div class="text-muted small">Date of Birth</div>
                                    <div><?= format_date($customer['date_of_birth']) ?></div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('customers/edit/' . $customer['id']) ?>" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit Profile
                        </a>
                        <?php if ($customerStatus == 'active'): ?>
                            <a href="<?= base_url('appointments/create?customer_id=' . $customer['id']) ?>" class="btn btn-outline-primary">
                                <i class="fas fa-calendar-plus"></i> New Appointment
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Membership Information -->
            <?php if ($membership): ?>
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-crown"></i> Membership</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-circle bg-success-light text-success me-3">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <div>
                            <div class="fw-bold"><?= $membership['name'] ?></div>
                            <div class="text-muted small"><?= $membership['description'] ?></div>
                        </div>
                    </div>

                    <div class="row g-3">
                        <div class="col-6">
                            <div class="card bg-light">
                                <div class="card-body p-2 text-center">
                                    <div class="text-muted small">Service Discount</div>
                                    <div class="fw-bold"><?= $membership['service_discount'] ?>%</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card bg-light">
                                <div class="card-body p-2 text-center">
                                    <div class="text-muted small">Product Discount</div>
                                    <div class="fw-bold"><?= $membership['product_discount'] ?>%</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <div class="text-muted small">Valid Until</div>
                        <div class="fw-bold"><?= format_date($membership['end_date']) ?></div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Appointments -->
        <div class="col-md-8 mb-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Appointment History</h5>
                    <a href="<?= base_url('appointments/create?customer_id=' . $customer['id']) ?>" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> New Appointment
                    </a>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($appointments)): ?>
                        <div class="p-4 text-center">
                            <img src="<?= base_url('assets/images/empty-calendar.svg') ?>" alt="No Appointments" style="width: 120px; opacity: 0.5;" class="mb-3">
                            <p class="text-muted mb-0">No appointments found for this customer.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Date & Time</th>
                                        <th>Service</th>
                                        <th>Staff</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($appointments as $appointment): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?= format_date($appointment['appointment_date']) ?></div>
                                                <div class="small text-muted">
                                                    <?= date('h:i A', strtotime($appointment['start_time'])) ?> -
                                                    <?= date('h:i A', strtotime($appointment['end_time'])) ?>
                                                </div>
                                            </td>
                                            <td><?= $appointment['service_name'] ?></td>
                                            <td><?= $appointment['staff_name'] ?></td>
                                            <td>
                                                <?php if ($appointment['status'] == 'pending'): ?>
                                                    <span class="badge bg-warning">Pending</span>
                                                <?php elseif ($appointment['status'] == 'confirmed'): ?>
                                                    <span class="badge bg-primary">Confirmed</span>
                                                <?php elseif ($appointment['status'] == 'completed'): ?>
                                                    <span class="badge bg-success">Completed</span>
                                                <?php elseif ($appointment['status'] == 'cancelled'): ?>
                                                    <span class="badge bg-danger">Cancelled</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('appointments/view/' . $appointment['id']) ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Invoices -->
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Billing History</h5>
                    <a href="<?= base_url('billing/create?customer_id=' . $customer['id']) ?>" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> New Invoice
                    </a>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($invoices)): ?>
                        <div class="p-4 text-center">
                            <img src="<?= base_url('assets/images/invoice.svg') ?>" alt="No Invoices" style="width: 120px; opacity: 0.5;" class="mb-3">
                            <p class="text-muted mb-0">No invoices found for this customer.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Invoice #</th>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($invoices as $invoice): ?>
                                        <tr>
                                            <td>
                                                <a href="<?= base_url('billing/view/' . $invoice['id']) ?>" class="fw-bold text-primary">
                                                    #<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?>
                                                </a>
                                            </td>
                                            <td><?= format_date($invoice['invoice_date']) ?></td>
                                            <td><?= format_currency($invoice['total_amount']) ?></td>
                                            <td>
                                                <?php if ($invoice['payment_status'] == 'unpaid'): ?>
                                                    <span class="badge bg-warning">Unpaid</span>
                                                <?php elseif ($invoice['payment_status'] == 'partial'): ?>
                                                    <span class="badge bg-info">Partial</span>
                                                <?php elseif ($invoice['payment_status'] == 'paid'): ?>
                                                    <span class="badge bg-success">Paid</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('billing/view/' . $invoice['id']) ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.icon-circle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}
</style>












