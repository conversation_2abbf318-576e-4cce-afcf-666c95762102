<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-calendar"></i> Appointment Calendar</h1>
        <div>
            <a href="<?= base_url('appointments') ?>" class="btn btn-outline-primary me-2">
                <i class="fas fa-list"></i> List View
            </a>
            <a href="<?= base_url('appointments/create') ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> New Appointment
            </a>
        </div>
    </div>

    <!-- Calendar Controls -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('appointments/calendar') ?>" method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="month" class="form-label">Month</label>
                    <select class="form-select" id="month" name="month" onchange="this.form.submit()">
                        <?php for ($i = 1; $i <= 12; $i++): ?>
                            <option value="<?= sprintf('%02d', $i) ?>" <?= $selected_month == sprintf('%02d', $i) ? 'selected' : '' ?>>
                                <?= date('F', mktime(0, 0, 0, $i, 1, date('Y'))) ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="year" class="form-label">Year</label>
                    <select class="form-select" id="year" name="year" onchange="this.form.submit()">
                        <?php for ($i = date('Y') - 1; $i <= date('Y') + 2; $i++): ?>
                            <option value="<?= $i ?>" <?= $selected_year == $i ? 'selected' : '' ?>>
                                <?= $i ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="staff_id" class="form-label">Staff</label>
                    <select class="form-select" id="staff_id" name="staff_id" onchange="this.form.submit()">
                        <option value="">All Staff</option>
                        <?php foreach ($staff as $staff_member): ?>
                            <option value="<?= $staff_member['id'] ?>" <?= $selected_staff == $staff_member['id'] ? 'selected' : '' ?>>
                                <?= $staff_member['name'] ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </form>
        </div>
    </div>

    <!-- Calendar View -->
    <div class="card shadow-sm">
        <div class="card-body">
            <?php
            // Get the first day of the month
            $first_day = strtotime("$selected_year-$selected_month-01");

            // Get the last day of the month
            $last_day = strtotime(date('Y-m-t', $first_day));

            // Get the day of the week for the first day (0 = Sunday, 6 = Saturday)
            $first_day_of_week = date('w', $first_day);

            // Get the number of days in the month
            $days_in_month = date('t', $first_day);

            // Get the month name
            $month_name = date('F', $first_day);
            ?>

            <h4 class="text-center mb-4"><?= $month_name ?> <?= $selected_year ?></h4>

            <div class="table-responsive">
                <table class="table table-bordered calendar-table">
                    <thead>
                        <tr>
                            <th>Sunday</th>
                            <th>Monday</th>
                            <th>Tuesday</th>
                            <th>Wednesday</th>
                            <th>Thursday</th>
                            <th>Friday</th>
                            <th>Saturday</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <?php
                            // Add empty cells for days before the first day of the month
                            for ($i = 0; $i < $first_day_of_week; $i++) {
                                echo '<td class="calendar-day empty"></td>';
                            }

                            // Current day counter
                            $current_day = 1;

                            // Current day of week
                            $current_day_of_week = $first_day_of_week;

                            // Loop through days of the month
                            while ($current_day <= $days_in_month) {
                                // If we've reached the end of the week, start a new row
                                if ($current_day_of_week == 7) {
                                    echo '</tr><tr>';
                                    $current_day_of_week = 0;
                                }

                                // Format the date
                                $date = sprintf('%s-%s-%02d', $selected_year, $selected_month, $current_day);

                                // Check if the date is today
                                $is_today = (date('Y-m-d') == $date);

                                // Get appointments for this day
                                $day_appointments = isset($calendar_data[$date]) ? $calendar_data[$date] : [];

                                // Day class
                                $day_class = $is_today ? 'today' : '';

                                // Start the day cell
                                echo '<td class="calendar-day ' . $day_class . '">';

                                // Day number
                                echo '<div class="day-number">' . $current_day . '</div>';

                                // Appointments
                                if (!empty($day_appointments)) {
                                    echo '<div class="day-appointments">';
                                    foreach ($day_appointments as $appointment) {
                                        $status_class = '';
                                        switch ($appointment['status']) {
                                            case 'pending':
                                                $status_class = 'bg-warning';
                                                break;
                                            case 'confirmed':
                                                $status_class = 'bg-primary';
                                                break;
                                            case 'completed':
                                                $status_class = 'bg-success';
                                                break;
                                            case 'cancelled':
                                                $status_class = 'bg-danger';
                                                break;
                                        }

                                        echo '<a href="' . base_url('appointments/view/' . $appointment['id']) . '" class="appointment-item ' . $status_class . '">';
                                        echo '<div class="appointment-time">' . date('h:i A', strtotime($appointment['start_time'])) . '</div>';
                                        echo '<div class="appointment-details">';
                                        echo '<div class="appointment-customer">' . $appointment['customer_name'] . '</div>';
                                        echo '<div class="appointment-service">' . $appointment['service_name'] . '</div>';
                                        echo '</div>';
                                        echo '</a>';
                                    }
                                    echo '</div>';
                                }

                                // End the day cell
                                echo '</td>';

                                // Increment day counters
                                $current_day++;
                                $current_day_of_week++;
                            }

                            // Add empty cells for days after the last day of the month
                            while ($current_day_of_week < 7) {
                                echo '<td class="calendar-day empty"></td>';
                                $current_day_of_week++;
                            }
                            ?>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.calendar-table {
    table-layout: fixed;
}

.calendar-day {
    height: 150px;
    vertical-align: top;
    padding: 8px;
    position: relative;
}

.calendar-day.empty {
    background-color: #f9f9f9;
}

.calendar-day.today {
    background-color: rgba(99, 102, 241, 0.05);
    border: 2px solid var(--primary);
}

.day-number {
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 8px;
    position: absolute;
    top: 5px;
    right: 8px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.today .day-number {
    background-color: var(--primary);
    color: white;
}

.day-appointments {
    margin-top: 30px;
    max-height: 110px;
    overflow-y: auto;
}

.appointment-item {
    display: flex;
    padding: 5px;
    border-radius: 4px;
    margin-bottom: 5px;
    text-decoration: none;
    color: white;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.appointment-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    color: white;
}

.appointment-time {
    font-weight: bold;
    margin-right: 5px;
    white-space: nowrap;
}

.appointment-details {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.appointment-customer {
    font-weight: 500;
}

.appointment-service {
    opacity: 0.8;
    font-size: 0.75rem;
}
</style>
