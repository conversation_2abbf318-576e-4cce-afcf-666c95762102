<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-wallet"></i> <?= $customer['name'] ?>'s Wallet</h1>
        <div>
            <a href="<?= base_url('wallet/add-money/' . $customer['id']) ?>" class="btn btn-success">
                <i class="fas fa-plus"></i> Add Money
            </a>
            <a href="<?= base_url('wallet/adjust-balance/' . $customer['id']) ?>" class="btn btn-warning">
                <i class="fas fa-edit"></i> Adjust Balance
            </a>
            <a href="<?= base_url('wallet') ?>" class="btn btn-outline-primary ms-2">
                <i class="fas fa-arrow-left"></i> Back to Wallets
            </a>
        </div>
    </div>

    <!-- Customer Info & Wallet Balance -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-user"></i> Customer Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-4"><strong>Name:</strong></div>
                        <div class="col-sm-8"><?= $customer['name'] ?></div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-4"><strong>Email:</strong></div>
                        <div class="col-sm-8"><?= $customer['email'] ?></div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-4"><strong>Phone:</strong></div>
                        <div class="col-sm-8"><?= $customer['phone'] ?></div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-4"><strong>Customer ID:</strong></div>
                        <div class="col-sm-8">#<?= $customer['id'] ?></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-wallet"></i> Wallet Balance</h5>
                </div>
                <div class="card-body text-center">
                    <h1 class="display-4 text-<?= $wallet && $wallet['balance'] > 0 ? 'success' : 'muted' ?>">
                        <?= format_currency($wallet ? $wallet['balance'] : 0) ?>
                    </h1>
                    <p class="text-muted">Current Balance</p>
                    <div class="mt-3">
                        <?php if ($wallet && $wallet['balance'] > 0): ?>
                            <span class="badge bg-success fs-6">Active Wallet</span>
                        <?php else: ?>
                            <span class="badge bg-secondary fs-6">Empty Wallet</span>
                        <?php endif; ?>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            Last updated: <?= $wallet ? format_datetime($wallet['updated_at']) : 'Never' ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 mb-2">
                    <a href="<?= base_url('wallet/add-money/' . $customer['id']) ?>" class="btn btn-success w-100">
                        <i class="fas fa-plus"></i> Add Money
                    </a>
                </div>
                <div class="col-md-3 mb-2">
                    <a href="<?= base_url('wallet/transactions/' . $customer['id']) ?>" class="btn btn-info w-100">
                        <i class="fas fa-history"></i> Full History
                    </a>
                </div>
                <div class="col-md-3 mb-2">
                    <a href="<?= base_url('customers/view/' . $customer['id']) ?>" class="btn btn-outline-primary w-100">
                        <i class="fas fa-user"></i> View Customer
                    </a>
                </div>
                <div class="col-md-3 mb-2">
                    <a href="<?= base_url('billing/create?customer_id=' . $customer['id']) ?>" class="btn btn-outline-success w-100">
                        <i class="fas fa-receipt"></i> Create Bill
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="card shadow-sm">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-history"></i> Recent Transactions</h5>
            <a href="<?= base_url('wallet/transactions/' . $customer['id']) ?>" class="btn btn-sm btn-outline-primary">
                View All
            </a>
        </div>
        <div class="card-body">
            <?php if (empty($transactions)): ?>
                <div class="text-center py-5">
                    <img src="<?= base_url('assets/images/empty-transactions.svg') ?>" alt="No Transactions" style="width: 120px; opacity: 0.5;" class="mb-3">
                    <h5>No Transactions Yet</h5>
                    <p class="text-muted">This customer hasn't made any wallet transactions yet.</p>
                    <a href="<?= base_url('wallet/add-money/' . $customer['id']) ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Transaction
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Balance After</th>
                                <th>Description</th>
                                <th>By</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($transactions, 0, 10) as $transaction): ?>
                                <tr>
                                    <td><?= format_datetime($transaction['created_at']) ?></td>
                                    <td>
                                        <?php if ($transaction['transaction_type'] == 'credit'): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-plus"></i> Credit
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">
                                                <i class="fas fa-minus"></i> Debit
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="text-<?= $transaction['transaction_type'] == 'credit' ? 'success' : 'danger' ?>">
                                            <?= $transaction['transaction_type'] == 'credit' ? '+' : '-' ?><?= format_currency($transaction['amount']) ?>
                                        </span>
                                    </td>
                                    <td><?= format_currency($transaction['balance_after']) ?></td>
                                    <td>
                                        <span class="text-muted"><?= $transaction['description'] ?: 'No description' ?></span>
                                        <?php if ($transaction['reference_type'] == 'invoice_payment' && $transaction['reference_id']): ?>
                                            <br><small class="text-info">
                                                <i class="fas fa-receipt"></i> 
                                                <a href="<?= base_url('billing/view/' . $transaction['reference_id']) ?>">Invoice #<?= $transaction['reference_id'] ?></a>
                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?= $transaction['created_by_name'] ?: 'System' ?></small>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php if (count($transactions) > 10): ?>
                    <div class="text-center mt-3">
                        <a href="<?= base_url('wallet/transactions/' . $customer['id']) ?>" class="btn btn-outline-primary">
                            <i class="fas fa-eye"></i> View All <?= count($transactions) ?> Transactions
                        </a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.display-4 {
    font-weight: 700;
}

.badge.fs-6 {
    font-size: 0.9rem !important;
}

.btn {
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}
</style>
