/* Custom CSS for Salon Management System */

/* Background colors for card footers */
.bg-primary-dark {
    background-color: #0d47a1;
}

.bg-success-dark {
    background-color: #1b5e20;
}

.bg-warning-dark {
    background-color: #e65100;
}

.bg-info-dark {
    background-color: #01579b;
}

.bg-danger-dark {
    background-color: #b71c1c;
}

/* Main content area */
body {
    background-color: #f8f9fa;
    overflow-x: hidden; /* Prevent horizontal scrolling */
    margin: 0;
    padding: 0;
    width: 100%;
}

/* Fix for hero slider positioning */
.navbar {
    margin-bottom: 0 !important;
}

/* Remove any margins that might cause gaps */
.container-fluid,
.row,
main,
.container {
    max-width: 100% !important;
}

/* Specific fix for navbar container */
.navbar .container-fluid {
    padding: 0 1rem !important;
}

/* Sidebar styles */
.sidebar {
    min-height: calc(100vh - 56px);
    background-color: #343a40;
}

/* Card styles */
.card {
    margin-bottom: 20px;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    font-weight: 500;
}

/* Table styles */
.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Form styles */
.form-label {
    font-weight: 500;
}

/* Button styles */
.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

/* Login and register pages */
.auth-card {
    max-width: 500px;
    margin: 2rem auto;
}

/* Dashboard cards */
.dashboard-card {
    transition: transform 0.3s;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

/* Calendar styles */
.calendar-day {
    height: 120px;
    overflow-y: auto;
}

.calendar-event {
    padding: 2px 5px;
    margin-bottom: 2px;
    border-radius: 3px;
    font-size: 0.8rem;
    cursor: pointer;
}

/* Appointment status colors */
.status-pending {
    background-color: #ffeeba;
    color: #856404;
}

.status-confirmed {
    background-color: #b8daff;
    color: #004085;
}

.status-completed {
    background-color: #c3e6cb;
    color: #155724;
}

.status-cancelled {
    background-color: #f5c6cb;
    color: #721c24;
}

/* Invoice styles */
.invoice-header {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.invoice-footer {
    border-top: 1px solid #dee2e6;
    padding-top: 20px;
    margin-top: 20px;
}

/* Frontend booking styles */
.service-card {
    cursor: pointer;
    transition: all 0.3s;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.service-card.selected {
    border: 2px solid #4e73df;
}

.staff-card {
    cursor: pointer;
    transition: all 0.3s;
}

.staff-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.staff-card.selected {
    border: 2px solid #4e73df;
}

/* Time slot styles */
.time-slot {
    display: inline-block;
    padding: 10px 15px;
    margin: 5px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
}

.time-slot:hover {
    background-color: #e9ecef;
}

.time-slot.selected {
    background-color: #4e73df;
    color: white;
    border-color: #4e73df;
}

.time-slot.disabled {
    background-color: #f8f9fa;
    color: #adb5bd;
    cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .calendar-day {
        height: 80px;
    }

    .hide-on-mobile {
        display: none;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none;
    }

    .print-only {
        display: block;
    }

    body {
        background-color: white;
    }

    .container {
        width: 100%;
        max-width: 100%;
    }
}
