<?php
/**
 * Payroll Model
 */
class PayrollModel extends Model {
    protected $table = 'payroll';

    /**
     * Get all payroll records with staff and user details
     *
     * @return array Payroll records
     */
    public function all() {
        $query = "SELECT p.*, s.id as staff_id, u.name as staff_name
                 FROM " . $this->table . " p
                 JOIN staff s ON p.staff_id = s.id
                 JOIN users u ON s.user_id = u.id
                 ORDER BY p.payment_date DESC";
        $stmt = $this->db->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Find payroll record by ID with staff and user details
     *
     * @param int $id Payroll ID
     * @return array|false Payroll record data or false if not found
     */
    public function find($id) {
        $query = "SELECT p.*, s.id as staff_id, u.name as staff_name, u.email as staff_email
                 FROM " . $this->table . " p
                 JOIN staff s ON p.staff_id = s.id
                 JOIN users u ON s.user_id = u.id
                 WHERE p.id = :id
                 LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get payroll records by staff ID
     *
     * @param int $staff_id Staff ID
     * @return array Payroll records
     */
    public function getByStaffId($staff_id) {
        $query = "SELECT p.*, s.id as staff_id, u.name as staff_name
                 FROM " . $this->table . " p
                 JOIN staff s ON p.staff_id = s.id
                 JOIN users u ON s.user_id = u.id
                 WHERE p.staff_id = :staff_id
                 ORDER BY p.payment_date DESC";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':staff_id', $staff_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Create a new payroll record
     *
     * @param array $data Payroll data
     * @return int|false Payroll ID or false on failure
     */
    public function create($data) {
        $query = "INSERT INTO " . $this->table . " (staff_id, pay_period_start, pay_period_end, basic_salary, commission_amount, bonus, deductions, net_salary, payment_date, payment_method, status, notes)
                 VALUES (:staff_id, :pay_period_start, :pay_period_end, :basic_salary, :commission_amount, :bonus, :deductions, :net_salary, :payment_date, :payment_method, :status, :notes)";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':staff_id', $data['staff_id']);
        $stmt->bindParam(':pay_period_start', $data['pay_period_start']);
        $stmt->bindParam(':pay_period_end', $data['pay_period_end']);
        $stmt->bindParam(':basic_salary', $data['basic_salary']);
        $stmt->bindParam(':commission_amount', $data['commission_amount']);
        $stmt->bindParam(':bonus', $data['bonus']);
        $stmt->bindParam(':deductions', $data['deductions']);
        $stmt->bindParam(':net_salary', $data['net_salary']);
        $stmt->bindParam(':payment_date', $data['payment_date']);
        $stmt->bindParam(':payment_method', $data['payment_method']);
        $stmt->bindParam(':status', $data['status']);
        $stmt->bindParam(':notes', $data['notes']);

        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }

        return false;
    }

    /**
     * Update a payroll record
     *
     * @param int $id Payroll ID
     * @param array $data Payroll data
     * @return bool Success or failure
     */
    public function update($id, $data) {
        $query = "UPDATE " . $this->table . " SET
                 staff_id = :staff_id,
                 pay_period_start = :pay_period_start,
                 pay_period_end = :pay_period_end,
                 basic_salary = :basic_salary,
                 commission_amount = :commission_amount,
                 bonus = :bonus,
                 deductions = :deductions,
                 net_salary = :net_salary,
                 payment_date = :payment_date,
                 payment_method = :payment_method,
                 status = :status,
                 notes = :notes
                 WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':staff_id', $data['staff_id']);
        $stmt->bindParam(':pay_period_start', $data['pay_period_start']);
        $stmt->bindParam(':pay_period_end', $data['pay_period_end']);
        $stmt->bindParam(':basic_salary', $data['basic_salary']);
        $stmt->bindParam(':commission_amount', $data['commission_amount']);
        $stmt->bindParam(':bonus', $data['bonus']);
        $stmt->bindParam(':deductions', $data['deductions']);
        $stmt->bindParam(':net_salary', $data['net_salary']);
        $stmt->bindParam(':payment_date', $data['payment_date']);
        $stmt->bindParam(':payment_method', $data['payment_method']);
        $stmt->bindParam(':status', $data['status']);
        $stmt->bindParam(':notes', $data['notes']);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }

    /**
     * Delete a payroll record
     *
     * @param int $id Payroll ID
     * @return bool Success or failure
     */
    public function delete($id) {
        $query = "DELETE FROM " . $this->table . " WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }

    /**
     * Get commissions for a payroll record
     *
     * @param int $payroll_id Payroll ID
     * @return array Commissions
     */
    public function getCommissions($payroll_id) {
        $query = "SELECT pc.*, s.name as service_name
                 FROM payroll_commissions pc
                 JOIN invoice_services inv_serv ON pc.invoice_service_id = inv_serv.id
                 JOIN services s ON inv_serv.service_id = s.id
                 WHERE pc.payroll_id = :payroll_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':payroll_id', $payroll_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Add commission to payroll
     *
     * @param array $data Commission data
     * @return int|false Commission ID or false on failure
     */
    public function addCommission($data) {
        $query = "INSERT INTO payroll_commissions (payroll_id, invoice_service_id, service_amount, commission_rate, commission_amount)
                 VALUES (:payroll_id, :invoice_service_id, :service_amount, :commission_rate, :commission_amount)";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':payroll_id', $data['payroll_id']);
        $stmt->bindParam(':invoice_service_id', $data['invoice_service_id']);
        $stmt->bindParam(':service_amount', $data['service_amount']);
        $stmt->bindParam(':commission_rate', $data['commission_rate']);
        $stmt->bindParam(':commission_amount', $data['commission_amount']);

        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }

        return false;
    }

    /**
     * Calculate commissions for a staff member in a date range
     *
     * @param int $staff_id Staff ID
     * @param string $start_date Start date
     * @param string $end_date End date
     * @return array Commission data
     */
    public function calculateCommissions($staff_id, $start_date, $end_date) {
        // Check if invoice_services table exists and has the expected structure
        try {
            $checkQuery = "SHOW TABLES LIKE 'invoice_services'";
            $checkStmt = $this->db->prepare($checkQuery);
            $checkStmt->execute();
            $tableExists = $checkStmt->rowCount() > 0;

            if (!$tableExists) {
                error_log("Table invoice_services does not exist");
                return [
                    'commissions' => [],
                    'total_commission' => 0
                ];
            }

            // Check if there are any invoice services for this staff
            $countQuery = "SELECT COUNT(*) FROM invoice_services WHERE staff_id = :staff_id";
            $countStmt = $this->db->prepare($countQuery);
            $countStmt->bindParam(':staff_id', $staff_id);
            $countStmt->execute();
            $count = $countStmt->fetchColumn();

            if ($count == 0) {
                error_log("No invoice services found for staff ID: $staff_id");
                return [
                    'commissions' => [],
                    'total_commission' => 0
                ];
            }

            // Main query to get commissions - adjusted for the actual database structure
            $query = "SELECT
                    inv_serv.id as invoice_service_id,
                    inv_serv.total_price as service_amount,
                    s.commission_rate,
                    (inv_serv.total_price * s.commission_rate / 100) as commission_amount,
                    srv.name as service_name,
                    i.id as invoice_number,
                    i.invoice_date
                FROM invoice_services inv_serv
                JOIN invoices i ON inv_serv.invoice_id = i.id
                JOIN staff s ON inv_serv.staff_id = s.id
                JOIN services srv ON inv_serv.service_id = srv.id
                WHERE inv_serv.staff_id = :staff_id
                AND i.invoice_date BETWEEN :start_date AND :end_date
                AND i.payment_status = 'paid'
                AND NOT EXISTS (
                    SELECT 1 FROM payroll_commissions pc
                    WHERE pc.invoice_service_id = inv_serv.id
                )";

            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':staff_id', $staff_id);
            $stmt->bindParam(':start_date', $start_date);
            $stmt->bindParam(':end_date', $end_date);
            $stmt->execute();

            $commissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $total_commission = 0;

            foreach ($commissions as $commission) {
                $total_commission += floatval($commission['commission_amount']);
            }

            return [
                'commissions' => $commissions,
                'total_commission' => $total_commission
            ];
        } catch (PDOException $e) {
            error_log("Database error in calculateCommissions: " . $e->getMessage());
            throw $e;
        } catch (Exception $e) {
            error_log("General error in calculateCommissions: " . $e->getMessage());
            throw $e;
        }
    }
}
