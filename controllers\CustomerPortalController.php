<?php
/**
 * Customer Portal Controller
 * Handles customer-specific pages like appointments, invoices, and memberships
 */
class CustomerPortalController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            flash('error', 'Please login to access this page');
            $this->redirect(base_url('login'));
        }

        // Check if user has customer role
        if (!has_role(['customer'])) {
            flash('error', 'You do not have permission to access this page');
            $this->redirect(base_url());
        }
    }

    /**
     * Display customer dashboard
     */
    public function index() {
        // Get current user
        $user_id = $_SESSION['user_id'];
        $user_model = new UserModel();
        $user = $user_model->find($user_id);

        // Get customer data
        $customer_model = new CustomerModel();
        $customer = $customer_model->findByUserId($user_id);

        if (!$customer) {
            flash('error', 'Customer profile not found');
            $this->redirect(base_url());
        }

        // Get customer appointments
        $appointment_model = new AppointmentModel();
        $appointments = $appointment_model->getByCustomerId($customer['id']);

        // Get customer invoices
        $invoice_model = new InvoiceModel();
        $invoices = $invoice_model->getByCustomerId($customer['id']);

        // Get customer membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->getCustomerMembership($customer['id']);

        // Render view
        $this->render('customer_portal/dashboard', [
            'user' => $user,
            'customer' => $customer,
            'appointments' => $appointments,
            'invoices' => $invoices,
            'membership' => $membership
        ]);
    }

    /**
     * Display customer appointments
     */
    public function appointments() {
        // Get current user
        $user_id = $_SESSION['user_id'];
        $customer_model = new CustomerModel();
        $customer = $customer_model->findByUserId($user_id);

        if (!$customer) {
            flash('error', 'Customer profile not found');
            $this->redirect(base_url());
        }

        // Get customer appointments
        $appointment_model = new AppointmentModel();
        $appointments = $appointment_model->getByCustomerId($customer['id']);

        // Render view
        $this->render('customer_portal/appointments', [
            'customer' => $customer,
            'appointments' => $appointments
        ]);
    }

    /**
     * Display customer invoices
     */
    public function invoices() {
        // Get current user
        $user_id = $_SESSION['user_id'];
        $customer_model = new CustomerModel();
        $customer = $customer_model->findByUserId($user_id);

        if (!$customer) {
            flash('error', 'Customer profile not found');
            $this->redirect(base_url());
        }

        // Get customer invoices
        $invoice_model = new InvoiceModel();
        $invoices = $invoice_model->getByCustomerId($customer['id']);

        // Render view
        $this->render('customer_portal/invoices', [
            'customer' => $customer,
            'invoices' => $invoices
        ]);
    }

    /**
     * Display customer memberships
     */
    public function memberships() {
        // Get current user
        $user_id = $_SESSION['user_id'];
        $customer_model = new CustomerModel();
        $customer = $customer_model->findByUserId($user_id);

        if (!$customer) {
            flash('error', 'Customer profile not found');
            $this->redirect(base_url());
        }

        // Get customer membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->getCustomerMembership($customer['id']);

        // Get membership history
        $membership_history = $membership_model->getCustomerMembershipHistory($customer['id']);

        // Render view
        $this->render('customer_portal/memberships', [
            'customer' => $customer,
            'membership' => $membership,
            'membership_history' => $membership_history
        ]);
    }

    /**
     * View invoice details
     */
    public function viewInvoice($id) {
        // Get current user
        $user_id = $_SESSION['user_id'];
        $customer_model = new CustomerModel();
        $customer = $customer_model->findByUserId($user_id);

        if (!$customer) {
            flash('error', 'Customer profile not found');
            $this->redirect(base_url());
        }

        // Get invoice
        $invoice_model = new InvoiceModel();
        $invoice = $invoice_model->getWithDetails($id);

        if (!$invoice) {
            flash('error', 'Invoice not found');
            $this->redirect(base_url('customer/invoices'));
        }

        // Check if invoice belongs to the customer
        if ($invoice['customer_id'] != $customer['id']) {
            flash('error', 'You do not have permission to view this invoice');
            $this->redirect(base_url('customer/invoices'));
        }

        // Render view
        $this->render('customer_portal/view_invoice', [
            'customer' => $customer,
            'invoice' => $invoice
        ]);
    }

    /**
     * View appointment details
     */
    public function viewAppointment($id) {
        // Get current user
        $user_id = $_SESSION['user_id'];
        $customer_model = new CustomerModel();
        $customer = $customer_model->findByUserId($user_id);

        if (!$customer) {
            flash('error', 'Customer profile not found');
            $this->redirect(base_url());
        }

        // Get appointment
        $appointment_model = new AppointmentModel();
        $appointment = $appointment_model->getWithDetails($id);

        if (!$appointment) {
            flash('error', 'Appointment not found');
            $this->redirect(base_url('customer/appointments'));
        }

        // Check if appointment belongs to the customer
        if ($appointment['customer_id'] != $customer['id']) {
            flash('error', 'You do not have permission to view this appointment');
            $this->redirect(base_url('customer/appointments'));
        }

        // Render view
        $this->render('customer_portal/view_appointment', [
            'customer' => $customer,
            'appointment' => $appointment
        ]);
    }
}
