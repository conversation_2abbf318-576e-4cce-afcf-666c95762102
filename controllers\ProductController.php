/**
 * Search products via AJAX for Select2
 */
public function search() {
    // Check if this is an AJAX request
    if (!$this->isAjaxRequest()) {
        $this->redirect(base_url('404'));
        return;
    }
    
    // Get search term and page
    $search = $this->input->get('q', '');
    $page = (int)$this->input->get('page', 1);
    $limit = 10;
    $offset = ($page - 1) * $limit;
    
    // Get products matching search term
    $products = $this->product_model->searchProducts($search, $limit, $offset);
    $total = $this->product_model->countSearchResults($search);
    
    // Format for Select2
    $items = [];
    foreach ($products as $product) {
        $items[] = [
            'id' => $product['id'],
            'text' => $product['name'] . ' - ' . $product['category_name'] . ' (' . format_currency($product['price']) . ') - Stock: ' . $product['quantity'],
            'price' => $product['price'],
            'stock' => $product['quantity']
        ];
    }
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode([
        'items' => $items,
        'more' => ($offset + $limit) < $total
    ]);
    exit;
}