<?php
/**
 * Contact Controller
 */
class ContactController extends Controller {
    /**
     * Display contact page
     */
    public function index() {
        // Get salon settings
        $settings_model = new SettingsModel();
        $settings = $settings_model->get();

        // Make sure we have all the salon-specific settings
        if (!isset($settings['salon_name'])) {
            $settings['salon_name'] = APP_NAME;
        }

        if (!isset($settings['salon_address']) && isset($settings['address'])) {
            $settings['salon_address'] = $settings['address'];
        }

        if (!isset($settings['salon_phone']) && isset($settings['phone'])) {
            $settings['salon_phone'] = $settings['phone'];
        }

        if (!isset($settings['salon_email']) && isset($settings['email'])) {
            $settings['salon_email'] = $settings['email'];
        }

        // Render contact page
        $this->render('contact/index', [
            'settings' => $settings
        ]);
    }

    /**
     * Process contact form submission
     */
    public function send() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('contact'));
        }

        // Get form data
        $name = input('name');
        $email = input('email');
        $phone = input('phone');
        $subject = input('subject');
        $message = input('message');

        // Validate form data
        if (empty($name) || empty($email) || empty($subject) || empty($message)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('contact'));
        }

        // In a real application, you would send an email here
        // For now, we'll just simulate a successful submission

        // Save contact message to database
        $contact_model = new ContactModel();
        $contact_data = [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'subject' => $subject,
            'message' => $message,
            'status' => 'unread'
        ];

        $result = $contact_model->create($contact_data);

        if ($result) {
            flash('success', 'Your message has been sent successfully. We will get back to you soon.');
        } else {
            flash('error', 'Failed to send message. Please try again later.');
        }

        $this->redirect(base_url('contact'));
    }
}
