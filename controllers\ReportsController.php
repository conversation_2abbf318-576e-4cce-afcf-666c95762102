<?php
/**
 * Reports Controller
 */
class ReportsController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            $this->redirect(base_url('login'));
        }
        
        // Check if user has permission
        if (!has_role(['admin', 'manager'])) {
            flash('error', 'You do not have permission to access reports');
            $this->redirect(base_url('dashboard'));
        }
    }
    
    /**
     * Display reports dashboard
     */
    public function index() {
        // Get date range
        $start_date = input('start_date', date('Y-m-01')); // First day of current month
        $end_date = input('end_date', date('Y-m-t')); // Last day of current month
        
        // Get reports model
        $reports_model = new ReportsModel();
        
        // Get sales summary
        $sales_report = $reports_model->getSalesReport($start_date, $end_date, 'day');
        
        // Get service sales summary
        $service_sales_report = $reports_model->getServiceSalesReport($start_date, $end_date);
        
        // Get staff performance summary
        $staff_performance_report = $reports_model->getStaffPerformanceReport($start_date, $end_date);
        
        // Get appointment summary
        $appointment_report = $reports_model->getAppointmentReport($start_date, $end_date);
        
        // Render view
        $this->render('reports/dashboard', [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'sales_report' => $sales_report,
            'service_sales_report' => $service_sales_report,
            'staff_performance_report' => $staff_performance_report,
            'appointment_report' => $appointment_report,
            'active_tab' => 'dashboard'
        ]);
    }
    
    /**
     * Display sales report
     */
    public function sales() {
        // Get date range
        $start_date = input('start_date', date('Y-m-01')); // First day of current month
        $end_date = input('end_date', date('Y-m-t')); // Last day of current month
        $group_by = input('group_by', 'day');
        
        // Get reports model
        $reports_model = new ReportsModel();
        
        // Get sales report
        $sales_report = $reports_model->getSalesReport($start_date, $end_date, $group_by);
        
        // Render view
        $this->render('reports/sales', [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'group_by' => $group_by,
            'sales_report' => $sales_report,
            'active_tab' => 'sales'
        ]);
    }
    
    /**
     * Display service sales report
     */
    public function services() {
        // Get date range
        $start_date = input('start_date', date('Y-m-01')); // First day of current month
        $end_date = input('end_date', date('Y-m-t')); // Last day of current month
        
        // Get reports model
        $reports_model = new ReportsModel();
        
        // Get service sales report
        $service_sales_report = $reports_model->getServiceSalesReport($start_date, $end_date);
        
        // Render view
        $this->render('reports/services', [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'service_sales_report' => $service_sales_report,
            'active_tab' => 'services'
        ]);
    }
    
    /**
     * Display product sales report
     */
    public function products() {
        // Get date range
        $start_date = input('start_date', date('Y-m-01')); // First day of current month
        $end_date = input('end_date', date('Y-m-t')); // Last day of current month
        
        // Get reports model
        $reports_model = new ReportsModel();
        
        // Get product sales report
        $product_sales_report = $reports_model->getProductSalesReport($start_date, $end_date);
        
        // Render view
        $this->render('reports/products', [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'product_sales_report' => $product_sales_report,
            'active_tab' => 'products'
        ]);
    }
    
    /**
     * Display staff performance report
     */
    public function staff() {
        // Get date range
        $start_date = input('start_date', date('Y-m-01')); // First day of current month
        $end_date = input('end_date', date('Y-m-t')); // Last day of current month
        
        // Get reports model
        $reports_model = new ReportsModel();
        
        // Get staff performance report
        $staff_performance_report = $reports_model->getStaffPerformanceReport($start_date, $end_date);
        
        // Render view
        $this->render('reports/staff', [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'staff_performance_report' => $staff_performance_report,
            'active_tab' => 'staff'
        ]);
    }
    
    /**
     * Display appointment report
     */
    public function appointments() {
        // Get date range
        $start_date = input('start_date', date('Y-m-01')); // First day of current month
        $end_date = input('end_date', date('Y-m-t')); // Last day of current month
        
        // Get reports model
        $reports_model = new ReportsModel();
        
        // Get appointment report
        $appointment_report = $reports_model->getAppointmentReport($start_date, $end_date);
        
        // Render view
        $this->render('reports/appointments', [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'appointment_report' => $appointment_report,
            'active_tab' => 'appointments'
        ]);
    }
    
    /**
     * Display inventory report
     */
    public function inventory() {
        // Get reports model
        $reports_model = new ReportsModel();
        
        // Get inventory report
        $inventory_report = $reports_model->getInventoryReport();
        
        // Render view
        $this->render('reports/inventory', [
            'inventory_report' => $inventory_report,
            'active_tab' => 'inventory'
        ]);
    }
    
    /**
     * Display customer report
     */
    public function customers() {
        // Get date range
        $start_date = input('start_date', date('Y-m-01')); // First day of current month
        $end_date = input('end_date', date('Y-m-t')); // Last day of current month
        
        // Get reports model
        $reports_model = new ReportsModel();
        
        // Get customer report
        $customer_report = $reports_model->getCustomerReport($start_date, $end_date);
        
        // Render view
        $this->render('reports/customers', [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'customer_report' => $customer_report,
            'active_tab' => 'customers'
        ]);
    }
    
    /**
     * Export report to CSV
     */
    public function export() {
        // Get report type
        $report_type = input('type');
        $start_date = input('start_date', date('Y-m-01'));
        $end_date = input('end_date', date('Y-m-t'));
        $group_by = input('group_by', 'day');
        
        // Get reports model
        $reports_model = new ReportsModel();
        
        // Get report data
        $report_data = [];
        $filename = 'report_' . date('Y-m-d') . '.csv';
        $headers = [];
        
        switch ($report_type) {
            case 'sales':
                $report = $reports_model->getSalesReport($start_date, $end_date, $group_by);
                $report_data = $report['data'];
                $filename = 'sales_report_' . date('Y-m-d') . '.csv';
                $headers = ['Period', 'Invoice Count', 'Subtotal', 'Tax', 'Discount', 'Total', 'Payment'];
                break;
                
            case 'services':
                $report = $reports_model->getServiceSalesReport($start_date, $end_date);
                $report_data = $report['data'];
                $filename = 'service_sales_report_' . date('Y-m-d') . '.csv';
                $headers = ['Service ID', 'Service Name', 'Service Price', 'Service Count', 'Total Amount'];
                break;
                
            case 'products':
                $report = $reports_model->getProductSalesReport($start_date, $end_date);
                $report_data = $report['data'];
                $filename = 'product_sales_report_' . date('Y-m-d') . '.csv';
                $headers = ['Product ID', 'Product Name', 'Product Price', 'Quantity Sold', 'Total Amount', 'Profit'];
                break;
                
            case 'staff':
                $report = $reports_model->getStaffPerformanceReport($start_date, $end_date);
                $report_data = $report['data'];
                $filename = 'staff_performance_report_' . date('Y-m-d') . '.csv';
                $headers = ['Staff ID', 'Staff Name', 'Position', 'Invoice Count', 'Service Count', 'Service Amount', 'Commission Amount'];
                break;
                
            case 'inventory':
                $report = $reports_model->getInventoryReport();
                $report_data = $report['data'];
                $filename = 'inventory_report_' . date('Y-m-d') . '.csv';
                $headers = ['Product ID', 'Product Name', 'Category', 'Current Stock', 'Min Stock', 'Cost Price', 'Selling Price', 'Profit Margin', 'Profit Percentage', 'Inventory Value'];
                break;
                
            case 'customers':
                $report = $reports_model->getCustomerReport($start_date, $end_date);
                $report_data = $report['data'];
                $filename = 'customer_report_' . date('Y-m-d') . '.csv';
                $headers = ['Customer ID', 'Customer Name', 'Email', 'Phone', 'Invoice Count', 'Total Spent', 'Last Visit'];
                break;
                
            default:
                flash('error', 'Invalid report type');
                $this->redirect(base_url('reports'));
                break;
        }
        
        // Generate CSV
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // Add headers
        fputcsv($output, $headers);
        
        // Add data
        foreach ($report_data as $row) {
            fputcsv($output, $row);
        }
        
        fclose($output);
        exit;
    }
}
