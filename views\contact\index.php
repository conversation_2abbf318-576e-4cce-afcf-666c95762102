<div class="container">
    <div class="row mb-5 fade-in">
        <div class="col-md-12 text-center">
            <h1 class="display-4 fw-bold">Get In Touch</h1>
            <p class="lead">We'd love to hear from you. Reach out to us with any questions or to book an appointment.</p>
        </div>
    </div>

    <div class="row">
        <!-- Contact Information -->
        <div class="col-lg-5 mb-4 fade-in" style="animation-delay: 0.1s;">
            <div class="card shadow h-100 border-0">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <?php if (!empty($settings['logo_path'])): ?>
                            <div class="bg-dark rounded p-3 d-inline-block mb-3" style="background-color:rgb(61, 61, 61);">
                                <img src="<?= base_url($settings['logo_path']) ?>" alt="<?= $settings['salon_name'] ?? 'Beauty Salon' ?>" class="img-fluid" style="max-height: 120px;">
                            </div>
                        <?php else: ?>
                            <div class="icon-circle bg-primary text-white mx-auto mb-3" style="width: 80px; height: 80px;">
                                <i class="fas fa-spa fa-3x"></i>
                            </div>
                            <h3 class="fw-bold"><?= $settings['salon_name'] ?? 'Beauty Salon' ?></h3>
                        <?php endif; ?>
                        <p class="text-muted">Your beauty and wellness destination</p>
                    </div>

                    <div class="contact-info">
                        <div class="contact-item d-flex align-items-center mb-4">
                            <div class="icon-circle bg-primary-light text-primary me-3">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">Our Location</h6>
                                <p class="mb-0 text-muted">
                                    <?= $settings['salon_address'] ?? $settings['address'] ?? '123 Main Street, City, State, ZIP' ?>
                                </p>
                            </div>
                        </div>

                        <div class="contact-item d-flex align-items-center mb-4">
                            <div class="icon-circle bg-primary-light text-primary me-3">
                                <i class="fas fa-phone-alt"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">Phone Number</h6>
                                <p class="mb-0">
                                    <a href="tel:<?= $settings['salon_phone'] ?? $settings['phone'] ?? '+1234567890' ?>" class="text-decoration-none text-primary">
                                        <?= $settings['salon_phone'] ?? $settings['phone'] ?? '+1234567890' ?>
                                    </a>
                                </p>
                            </div>
                        </div>

                        <div class="contact-item d-flex align-items-center mb-4">
                            <div class="icon-circle bg-primary-light text-primary me-3">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">Email Address</h6>
                                <p class="mb-0">
                                    <a href="mailto:<?= $settings['salon_email'] ?? $settings['email'] ?? '<EMAIL>' ?>" class="text-decoration-none text-primary">
                                        <?= $settings['salon_email'] ?? $settings['email'] ?? '<EMAIL>' ?>
                                    </a>
                                </p>
                            </div>
                        </div>

                        <?php if (!empty($settings['salon_website'])): ?>
                        <div class="contact-item d-flex align-items-center mb-4">
                            <div class="icon-circle bg-primary-light text-primary me-3">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">Website</h6>
                                <p class="mb-0">
                                    <a href="<?= $settings['salon_website'] ?>" class="text-decoration-none text-primary" target="_blank">
                                        <?= $settings['salon_website'] ?>
                                    </a>
                                </p>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="contact-item d-flex align-items-center mb-4">
                            <div class="icon-circle bg-primary-light text-primary me-3">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-1">Working Hours</h6>
                                <div class="small">
                                    <?php
                                    $business_hours = isset($settings['business_hours']) ? json_decode($settings['business_hours'], true) : [
                                        'monday' => '9:00-18:00',
                                        'tuesday' => '9:00-18:00',
                                        'wednesday' => '9:00-18:00',
                                        'thursday' => '9:00-18:00',
                                        'friday' => '9:00-18:00',
                                        'saturday' => '9:00-18:00',
                                        'sunday' => 'closed'
                                    ];

                                    $days = [
                                        'monday' => 'Monday',
                                        'tuesday' => 'Tuesday',
                                        'wednesday' => 'Wednesday',
                                        'thursday' => 'Thursday',
                                        'friday' => 'Friday',
                                        'saturday' => 'Saturday',
                                        'sunday' => 'Sunday'
                                    ];

                                    foreach ($days as $day_key => $day_name):
                                        $hours = $business_hours[$day_key] ?? 'Closed';
                                        $is_today = strtolower(date('l')) === $day_key;
                                    ?>
                                        <div class="d-flex justify-content-between mb-1 <?= $is_today ? 'text-primary fw-bold' : '' ?>">
                                            <span><?= $day_name ?>:</span>
                                            <span><?= strtoupper($hours) ?></span>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="social-links mt-4 text-center">
                        <h6 class="fw-bold mb-3">Follow Us</h6>
                        <div class="d-flex justify-content-center gap-3">
                            <a href="<?= $settings['facebook_url'] ?? '#' ?>" class="social-icon" target="_blank">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="<?= $settings['instagram_url'] ?? '#' ?>" class="social-icon" target="_blank">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="<?= $settings['twitter_url'] ?? '#' ?>" class="social-icon" target="_blank">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="<?= $settings['youtube_url'] ?? '#' ?>" class="social-icon" target="_blank">
                                <i class="fab fa-youtube"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Form -->
        <div class="col-lg-7 mb-4 fade-in" style="animation-delay: 0.2s;">
            <div class="card shadow border-0">
                <div class="card-body p-4">
                    <h3 class="fw-bold mb-4">Send Us a Message</h3>
                    <form action="<?= base_url('contact/send') ?>" method="post">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text bg-primary text-white"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="name" name="name" placeholder="John Doe" required>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text bg-primary text-white"><i class="fas fa-envelope"></i></span>
                                    <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-primary text-white"><i class="fas fa-phone"></i></span>
                                    <input type="tel" class="form-control" id="phone" name="phone" placeholder="+1234567890">
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text bg-primary text-white"><i class="fas fa-tag"></i></span>
                                    <input type="text" class="form-control" id="subject" name="subject" placeholder="Appointment Inquiry" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="message" class="form-label">Message <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text bg-primary text-white"><i class="fas fa-comment"></i></span>
                                <textarea class="form-control" id="message" name="message" rows="5" placeholder="How can we help you?" required></textarea>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i> Send Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Google Map -->
    <div class="row mb-5 fade-in" style="animation-delay: 0.3s;">
        <div class="col-md-12">
            <div class="card shadow border-0 overflow-hidden">
                <div class="card-body p-0">
                    <div class="ratio ratio-21x9">
                        <?php
                        // Get the salon address for the map
                        $mapAddress = $settings['salon_address'] ?? $settings['address'] ?? '123 Main Street, City, State, ZIP';
                        $encodedAddress = urlencode($mapAddress);
                        ?>
                        <iframe src="https://maps.google.com/maps?q=<?= $encodedAddress ?>&t=&z=13&ie=UTF8&iwloc=&output=embed" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.social-icon:hover {
    background-color: var(--primary);
    color: white;
    transform: translateY(-3px);
}

.contact-item {
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateX(5px);
}

.contact-item:hover .icon-circle {
    background-color: var(--primary);
    color: white;
}
</style>
