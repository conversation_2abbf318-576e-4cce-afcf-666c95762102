/**
 * Hero Slider Styles - Completely Rebuilt
 */

/* Reset all margins and paddings for slider-related elements */
html, body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    width: 100%;
}

/* Basic hero container */
.hero-slider-container {
    position: relative;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
    box-sizing: border-box;
}

/* Full width technique - using fixed positioning */
.hero-slider-container {
    width: calc(100vw + 12px); /* Add 12px to compensate for the negative left */
    position: relative;
    left: -12px; /* Fix for alignment */
    right: 0;
    margin-top: 0;
    margin-bottom: 40px;
    overflow: hidden;
}

/* Remove any additional classes or modifiers */
.hero-slider-container.mb-5 {
    margin-bottom: 40px !important;
}

/* Carousel container */
#heroSlider {
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Carousel items */
#heroSlider .carousel-item {
    height: 600px;
    background-color: #000;
}

#heroSlider .carousel-item img {
    object-fit: cover;
    height: 100%;
    width: 100%;
    opacity: 0.8;
}

/* Caption styling */
#heroSlider .carousel-caption {
    bottom: 100px;
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    max-width: 600px;
    margin: 0 auto;
}

#heroSlider .carousel-caption h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

#heroSlider .carousel-caption p {
    font-size: 1.2rem;
    margin-bottom: 20px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Carousel controls */
#heroSlider .carousel-control-prev,
#heroSlider .carousel-control-next {
    width: 5%;
}

#heroSlider .carousel-indicators {
    margin-bottom: 20px;
}

#heroSlider .carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
}

/* Fix for navbar and hero slider interaction */
.navbar {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

/* Fix for navbar container */
.navbar .container-fluid {
    padding: 0 1rem !important;
}

/* Fix for any container issues */
.container-fluid,
.container,
.row {
    max-width: 100% !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    /* Tablet adjustments */
    #heroSlider .carousel-item {
        height: 450px;
    }

    #heroSlider .carousel-caption {
        bottom: 50px;
        padding: 15px;
        max-width: 90%;
    }

    #heroSlider .carousel-caption h2 {
        font-size: 1.8rem;
    }

    #heroSlider .carousel-caption p {
        font-size: 1rem;
    }

    /* Ensure full width on mobile */
    .hero-slider-container {
        width: 100vw;
        left: -12px !important; /* Fix for mobile alignment */
        right: 0;
        position: relative;
    }

    /* Container and body fixes */
    .container .hero-slider-container,
    main .hero-slider-container,
    body .hero-slider-container {
        left: -12px !important;
    }
}

@media (max-width: 576px) {
    /* Mobile phone adjustments */
    #heroSlider .carousel-item {
        height: 350px;
    }

    #heroSlider .carousel-caption {
        bottom: 30px;
        padding: 10px;
    }

    #heroSlider .carousel-caption h2 {
        font-size: 1.5rem;
        margin-bottom: 10px;
    }

    #heroSlider .carousel-caption p {
        font-size: 0.9rem;
        margin-bottom: 15px;
    }

    #heroSlider .carousel-caption .btn {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }

    /* Ensure full width on small mobile devices */
    .hero-slider-container {
        left: -12px !important;
        width: calc(100vw + 12px) !important;
    }
}
