<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-file-invoice"></i> Create New Invoice</h1>
        <div>
            <a href="<?= base_url('billing') ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Invoices
            </a>
        </div>
    </div>

    <div class="card shadow-sm mb-4 border-0">
        <div class="card-body">
            <form id="invoice-form" action="<?= base_url('billing/store') ?>" method="post">
                <div class="row">
                    <!-- Customer Selection -->
                    <div class="col-md-6 mb-3">
                        <label for="customer_id" class="form-label">Customer <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <select class="form-select" id="customer_id" name="customer_id" data-search="true" required>
                                <option value="">Search for a customer...</option>
                            </select>
                            <a href="<?= base_url('customers/create') ?>" class="btn btn-outline-secondary" target="_blank">
                                <i class="fas fa-plus"></i> New
                            </a>
                        </div>
                    </div>

                    <!-- Invoice Date -->
                    <div class="col-md-6 mb-3">
                        <label for="invoice_date" class="form-label">Invoice Date <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            <input type="date" class="form-control" id="invoice_date" name="invoice_date" value="<?= date('Y-m-d') ?>" required>
                        </div>
                    </div>



                    <!-- Services Section -->
                    <div class="col-md-12 mt-4 mb-3">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-cut me-2"></i>Services</h5>
                    </div>

                    <div class="col-md-12 mb-4">
                        <div id="services-container" class="mb-3">
                            <!-- Default service row -->
                            <div class="service-row row mb-3 align-items-center bg-light py-2 rounded">
                                <div class="col-md-4 mb-2 mb-md-0">
                                    <label class="form-label small text-muted d-md-none">Service</label>
                                    <select class="form-select form-select-sm service-select" name="services[0][service_id]" data-search="true">
                                        <option value="">Search for a service...</option>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-2 mb-md-0">
                                    <label class="form-label small text-muted d-md-none">Staff (Optional)</label>
                                    <select class="form-select form-select-sm staff-select" name="services[0][staff_id]">
                                        <option value="">Select Staff (Optional)</option>
                                        <?php foreach ($staff as $staff_member): ?>
                                            <option value="<?= $staff_member['id'] ?>"
                                                <?= isset($_GET['staff_id']) && $_GET['staff_id'] == $staff_member['id'] ? 'selected' : '' ?>>
                                                <?= $staff_member['name'] ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-2 mb-md-0">
                                    <label class="form-label small text-muted d-md-none">Price</label>
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                                        <input type="number" class="form-control service-price" name="services[0][unit_price]" placeholder="Price" min="0" step="0.01">
                                    </div>
                                </div>
                                <div class="col-md-2 mb-2 mb-md-0">
                                    <label class="form-label small text-muted d-md-none">Quantity</label>
                                    <input type="number" class="form-control form-control-sm service-quantity" name="services[0][quantity]" placeholder="Qty" value="1" min="1">
                                </div>
                                <div class="col-md-1 text-end">
                                    <button type="button" class="btn btn-sm btn-outline-danger remove-service">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="mt-2 text-center">
                            <button type="button" class="btn btn-sm btn-outline-primary px-4" id="add-service">
                                <i class="fas fa-plus me-1"></i> Add Service
                            </button>
                        </div>
                    </div>

                    <!-- Service Row Template (hidden) -->
                    <template id="service-row-template">
                        <div class="service-row row mb-3 align-items-center bg-light py-2 rounded">
                            <div class="col-md-4 mb-2 mb-md-0">
                                <label class="form-label small text-muted d-md-none">Service</label>
                                <select class="form-select form-select-sm service-select" name="services[INDEX][service_id]" data-search="true">
                                    <option value="">Search for a service...</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-2 mb-md-0">
                                <label class="form-label small text-muted d-md-none">Staff (Optional)</label>
                                <select class="form-select form-select-sm staff-select" name="services[INDEX][staff_id]">
                                    <option value="">Select Staff (Optional)</option>
                                    <?php foreach ($staff as $staff_member): ?>
                                        <option value="<?= $staff_member['id'] ?>"
                                            <?= isset($_GET['staff_id']) && $_GET['staff_id'] == $staff_member['id'] ? 'selected' : '' ?>>
                                            <?= $staff_member['name'] ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2 mb-2 mb-md-0">
                                <label class="form-label small text-muted d-md-none">Price</label>
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                                    <input type="number" class="form-control service-price" name="services[INDEX][unit_price]" placeholder="Price" min="0" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-2 mb-2 mb-md-0">
                                <label class="form-label small text-muted d-md-none">Quantity</label>
                                <input type="number" class="form-control form-control-sm service-quantity" name="services[INDEX][quantity]" placeholder="Qty" value="1" min="1">
                            </div>
                            <div class="col-md-1 text-end">
                                <button type="button" class="btn btn-sm btn-outline-danger remove-service">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </template>

                    <!-- Products Section -->
                    <div class="col-md-12 mt-4 mb-3">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-box me-2"></i>Products</h5>
                    </div>

                    <div class="col-md-12 mb-4">
                        <div id="products-container" class="mb-3">
                            <!-- Product rows will be added here -->
                        </div>

                        <div class="mt-2 text-center">
                            <button type="button" class="btn btn-sm btn-outline-primary px-4" id="add-product">
                                <i class="fas fa-plus me-1"></i> Add Product
                            </button>
                        </div>
                    </div>

                    <!-- Totals Section -->
                    <div class="col-md-12 mt-4 mb-4">
                        <div class="card bg-light border-0 shadow-sm">
                            <div class="card-header bg-primary text-white py-3">
                                <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Invoice Summary</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">Notes</label>
                                            <textarea class="form-control" id="notes" name="notes" rows="5" placeholder="Enter any additional notes or information for this invoice"></textarea>
                                            <div class="form-text">Add any special instructions, payment terms, or additional information here.</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label for="subtotal" class="form-label">Subtotal</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                                                        <input type="text" class="form-control bg-light" id="subtotal" name="subtotal" readonly>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="coupon_code" class="form-label">Coupon Code</label>
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" id="coupon_code" name="coupon_code" placeholder="Enter coupon code">
                                                        <button class="btn btn-outline-primary" type="button" id="apply_coupon">Apply</button>
                                                    </div>
                                                    <div id="coupon_message" class="mt-1"></div>
                                                    <input type="hidden" id="coupon_id" name="coupon_id" value="">
                                                </div>
                                                <div class="mb-3">
                                                    <label for="discount_amount" class="form-label">Discount (%)</label>
                                                    <div class="input-group">
                                                        <input type="number" class="form-control" id="discount" name="discount_amount" value="0" min="0" max="100" step="0.01">
                                                        <span class="input-group-text">%</span>
                                                    </div>
                                                    <input type="hidden" id="discount_value" name="discount_value" value="0">
                                                    <div id="membership_discount_info" class="form-text text-success mt-2 p-2 border border-success rounded bg-light" style="display: none;">
                                                        <i class="fas fa-crown me-1"></i> <span id="membership_discount_text" class="mdt-bold"></span>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="tax_amount" class="form-label">Tax</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">%</span>
                                                        <input type="number" class="form-control" id="tax_rate" name="tax_rate" value="<?= $settings['tax_rate'] ?? 0 ?>" min="0" max="100" step="0.01">
                                                        <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                                                        <input type="number" class="form-control" id="tax" name="tax_amount" value="0" min="0" step="0.01" readonly>
                                                    </div>
                                                </div>
                                                <hr>
                                                <div class="mb-0">
                                                    <label for="total_amount" class="form-label fw-bold">Total Amount</label>
                                                    <div class="input-group input-group-lg">
                                                        <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                                                        <input type="text" class="form-control bg-light fw-bold" id="total_amount" name="total_amount" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Section -->
                    <div class="col-md-12 mt-4 mb-3">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-money-bill-wave me-2"></i>Payment Details</h5>
                    </div>

                    <div class="col-md-12 mb-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="payment_method" class="form-label">Payment Method</label>
                                        <select class="form-select" id="payment_method" name="payment_method">
                                            <option value="cash">Cash</option>
                                            <option value="card">Card</option>
                                            <option value="upi">UPI</option>
                                            <option value="wallet">Wallet</option>
                                            <option value="mixed">Mixed Payment</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="payment_amount" class="form-label">Payment Amount</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                                            <input type="number" class="form-control" id="payment_amount" name="payment_amount" value="0" min="0" step="0.01">
                                        </div>
                                        <div class="form-text">Enter amount received from customer</div>
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="payment_status" class="form-label">Invoice Status</label>
                                        <select class="form-select" id="status" name="payment_status">
                                            <option value="unpaid">Unpaid</option>
                                            <option value="partial">Partially Paid</option>
                                            <option value="paid">Paid</option>
                                        </select>
                                        <div class="form-text">Will be set automatically based on payment</div>
                                    </div>
                                </div>

                                <!-- Wallet Payment Section -->
                                <div id="wallet-payment-section" class="row mt-3" style="display: none;">
                                    <div class="col-md-12">
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-wallet"></i> Wallet Payment</h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-2">
                                                        <strong>Customer Wallet Balance:</strong>
                                                        <span id="customer-wallet-balance" class="text-success">₹0.00</span>
                                                        <span id="wallet-insufficient-badge" class="badge bg-warning ms-2" style="display: none;">
                                                            <i class="fas fa-exclamation-triangle"></i> Insufficient
                                                        </span>
                                                    </div>
                                                    <div class="mb-2">
                                                        <label for="wallet_amount" class="form-label">Amount from Wallet</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text">₹</span>
                                                            <input type="number" class="form-control" id="wallet_amount" name="wallet_amount" value="0" min="0" step="0.01">
                                                            <button type="button" class="btn btn-outline-primary" id="use-full-wallet">Use Full Balance</button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-2">
                                                        <strong>Remaining Amount:</strong>
                                                        <span id="remaining-amount" class="text-warning">₹0.00</span>
                                                    </div>
                                                    <div class="form-text">
                                                        <i class="fas fa-info-circle"></i>
                                                        The remaining amount will be paid via the selected payment method.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-md-12 mt-4 mb-3 text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-save me-2"></i> Create Invoice
                        </button>
                        <a href="<?= base_url('billing') ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Product Row Template (hidden) -->
<template id="product-row-template">
    <div class="product-row row mb-3 align-items-center bg-light py-2 rounded">
        <div class="col-md-4 mb-2 mb-md-0">
            <label class="form-label small text-muted d-md-none">Product</label>
            <select class="form-select form-select-sm product-select" name="products[{index}][product_id]" data-search="true">
                <option value="">Search for a product...</option>
            </select>
        </div>
        <div class="col-md-3 mb-2 mb-md-0">
            <label class="form-label small text-muted d-md-none">Price</label>
            <div class="input-group input-group-sm">
                <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                <input type="number" class="form-control product-price" name="products[{index}][unit_price]" placeholder="Price" min="0" step="0.01">
            </div>
        </div>
        <div class="col-md-2 mb-2 mb-md-0">
            <label class="form-label small text-muted d-md-none">Quantity</label>
            <input type="number" class="form-control form-control-sm product-quantity" name="products[{index}][quantity]" placeholder="Qty" value="1" min="1">
        </div>
        <div class="col-md-2 mb-2 mb-md-0">
            <label class="form-label small text-muted d-md-none">Total</label>
            <div class="input-group input-group-sm">
                <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                <input type="text" class="form-control product-total" readonly>
            </div>
        </div>
        <div class="col-md-1 text-end">
            <button type="button" class="btn btn-sm btn-outline-danger remove-product">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</template>

<script>

document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables
    let serviceIndex = 0; // Start with 0 for the default row
    let productIndex = 0;
    // Use HTML entity for currency symbol to avoid encoding issues
    const CURRENCY_SYMBOL = '<?= html_entity_decode(CURRENCY_SYMBOL, ENT_HTML5, "UTF-8") ?>';
    
    // Initialize select2 for customer dropdown
    initializeCustomerSelect2();

    // Initialize the default service row's select2
    initializeServiceSelect2(document.querySelector('.service-select'));

    // Add event listener to the default service row's remove button
    document.querySelector('.remove-service').addEventListener('click', function() {
        // Only remove if there's more than one service row
        if (document.querySelectorAll('.service-row').length > 1) {
            this.closest('.service-row').remove();
            calculateTotals();
        }
    });

    // Add event listener to the default service row's select
    $(document.querySelector('.service-select')).on('change', function() {
        updateServicePrice(this);
    });

    // Service price change handler
    function updateServicePrice(select) {
        const row = select.closest('.service-row');
        const priceInput = row.querySelector('.service-price');

        // Check if using Select2
        if ($(select).hasClass('select2-hidden-accessible')) {
            const data = $(select).select2('data')[0];
            if (data && data.id && data.price) {
                priceInput.value = data.price;
            } else {
                priceInput.value = '';
            }
        } else {
            // For regular select elements
            const selectedOption = select.options[select.selectedIndex];
            if (selectedOption && selectedOption.value) {
                const price = selectedOption.getAttribute('data-price');
                priceInput.value = price;
            } else {
                priceInput.value = '';
            }
        }

        calculateTotals();
    }

    // Add service row using template
    document.getElementById('add-service').addEventListener('click', function() {
        // Increment the service index for the new row
        serviceIndex++;

        // Get the template and create a new row
        const template = document.getElementById('service-row-template');
        const container = document.getElementById('services-container');

        // Clone the template content
        const newRow = document.importNode(template.content, true).querySelector('.service-row');

        // Replace INDEX placeholder with actual index
        const rowHtml = newRow.innerHTML.replace(/INDEX/g, serviceIndex);
        newRow.innerHTML = rowHtml;

        // Add the row to the DOM
        container.appendChild(newRow);

        // Initialize select2 for the service dropdown
        const serviceSelect = newRow.querySelector('.service-select');
        initializeServiceSelect2(serviceSelect);

        // Add event listeners
        $(serviceSelect).on('change', function() {
            updateServicePrice(this);
        });

        // Add remove button event listener
        newRow.querySelector('.remove-service').addEventListener('click', function() {
            // Always allow removing additional rows
            newRow.remove();
            calculateTotals();
        });
    });

    // Note: Remove service row functionality is now handled in the add-service function

    // Add product row
    document.getElementById('add-product').addEventListener('click', function() {
        const container = document.getElementById('products-container');
        const template = document.getElementById('product-row-template');
        const newRow = document.importNode(template.content, true).querySelector('.product-row');

        // Replace {index} with actual index
        newRow.innerHTML = newRow.innerHTML.replace(/{index}/g, productIndex);

        // Add event listeners
        const productSelect = newRow.querySelector('.product-select');

        // Initialize select2 for the product dropdown
        initializeProductSelect2(productSelect);

        // Add change event listener for regular select
        $(productSelect).on('change', function() {
            updateProductPrice(this);
        });

        newRow.querySelector('.product-quantity').addEventListener('input', function() {
            updateProductTotal(this);
        });

        newRow.querySelector('.product-price').addEventListener('input', function() {
            updateProductTotal(this);
        });

        newRow.querySelector('.remove-product').addEventListener('click', function() {
            newRow.remove();
            calculateTotals();
        });

        container.appendChild(newRow);
        productIndex++;
    });

    // Update product price when product is selected
    function updateProductPrice(select) {
        const row = select.closest('.product-row');
        const priceInput = row.querySelector('.product-price');
        const quantityInput = row.querySelector('.product-quantity');

        // Check if using Select2
        if ($(select).hasClass('select2-hidden-accessible')) {
            const data = $(select).select2('data')[0];
            if (data && data.id) {
                if (data.price) {
                    priceInput.value = data.price;
                }
                if (data.stock) {
                    quantityInput.max = data.stock;
                    if (parseInt(quantityInput.value) > parseInt(data.stock)) {
                        quantityInput.value = data.stock;
                    }
                }
            } else {
                priceInput.value = '';
                quantityInput.max = '';
            }
        } else {
            // For regular select elements
            const selectedOption = select.options[select.selectedIndex];
            if (selectedOption && selectedOption.value) {
                const price = selectedOption.getAttribute('data-price');
                const stock = selectedOption.getAttribute('data-stock');

                priceInput.value = price;
                quantityInput.max = stock;

                if (parseInt(quantityInput.value) > parseInt(stock)) {
                    quantityInput.value = stock;
                }
            } else {
                priceInput.value = '';
                quantityInput.max = '';
            }
        }

        updateProductTotal(priceInput);
    }

    // Update product total when quantity or price changes
    function updateProductTotal(input) {
        const row = input.closest('.product-row');
        const priceInput = row.querySelector('.product-price');
        const quantityInput = row.querySelector('.product-quantity');
        const totalInput = row.querySelector('.product-total');

        if (priceInput.value && quantityInput.value) {
            const price = parseFloat(priceInput.value);
            const quantity = parseInt(quantityInput.value);
            const total = price * quantity;

            totalInput.value = total.toFixed(2);
        } else {
            totalInput.value = '';
        }

        calculateTotals();
    }

    // Calculate invoice totals
    function calculateTotals(skipMembershipCheck = false) {
        let subtotal = 0;

        // Calculate services subtotal
        document.querySelectorAll('.service-row').forEach(function(row) {
            const priceInput = row.querySelector('.service-price');
            const quantityInput = row.querySelector('.service-quantity');

            if (priceInput.value && quantityInput.value) {
                const price = parseFloat(priceInput.value);
                const quantity = parseInt(quantityInput.value);
                subtotal += price * quantity;
            }
        });

        // Calculate products subtotal
        document.querySelectorAll('.product-row').forEach(function(row) {
            const totalInput = row.querySelector('.product-total');

            if (totalInput.value) {
                subtotal += parseFloat(totalInput.value);
            }
        });

        // Update subtotal
        document.getElementById('subtotal').value = subtotal.toFixed(2);

        // If customer has a membership and we're not using a manual discount, apply membership discount
        // Only do this if we're not already in a membership discount calculation (to avoid infinite loop)
        if (!skipMembershipCheck && window.customerMembership && document.getElementById('membership_discount_info').style.display !== 'none') {
            // Recalculate membership discount based on current services and products
            applyMembershipDiscount();
            return; // Exit early as applyMembershipDiscount will call calculateTotals again
        }

        // Calculate discount (applied to subtotal before tax)
        const discountPercentage = parseFloat(document.getElementById('discount').value) || 0;
        const discountAmount = subtotal * (discountPercentage / 100);

        // Store the actual discount amount for the backend
        document.getElementById('discount_value').value = discountAmount.toFixed(2);

        // Calculate subtotal after discount
        const subtotalAfterDiscount = subtotal - discountAmount;

        // Calculate tax on the discounted subtotal
        const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;
        const taxAmount = subtotalAfterDiscount * (taxRate / 100);
        document.getElementById('tax').value = taxAmount.toFixed(2);

        // Calculate total (rounded to nearest integer)
        const totalExact = subtotalAfterDiscount + taxAmount;
        const total = Math.round(totalExact);

        // Update total and payment amount
        document.getElementById('total_amount').value = total.toFixed(2);
        document.getElementById('payment_amount').value = total.toFixed(2);
    }

    // Note: Event listeners for service rows are now added when each row is created

    // Add event listeners to discount and tax inputs
    document.getElementById('discount').addEventListener('input', function() {
        // If user manually changes the discount, hide the membership discount info
        // This indicates they want to use a custom discount instead of the membership discount
        if (window.customerMembership && document.getElementById('membership_discount_info').style.display !== 'none') {
            // Check if the value is different from the membership discount
            const currentDiscount = parseFloat(this.value) || 0;
            const membershipServiceDiscount = parseFloat(window.customerMembership.service_discount) || 0;
            const membershipProductDiscount = parseFloat(window.customerMembership.product_discount) || 0;

            // If the discount has been manually changed significantly
            if (Math.abs(currentDiscount - membershipServiceDiscount) > 0.1 &&
                Math.abs(currentDiscount - membershipProductDiscount) > 0.1) {
                // Hide membership info and show manual discount message
                document.getElementById('membership_discount_info').style.display = 'none';

                // Add a message about manual discount
                const discountInfo = document.createElement('div');
                discountInfo.id = 'manual_discount_info';
                discountInfo.className = 'form-text text-info mt-2 p-2 border border-info rounded bg-light';
                discountInfo.innerHTML = '<i class="fas fa-info-circle me-1"></i> <span class="fw-bold">Manual discount applied</span>';

                // Remove existing manual discount info if it exists
                const existingInfo = document.getElementById('manual_discount_info');
                if (existingInfo) existingInfo.remove();

                // Add the new info
                this.parentNode.parentNode.appendChild(discountInfo);
            }
        }

        calculateTotals();
    });
    document.getElementById('tax_rate').addEventListener('input', calculateTotals);

    // Function to initialize select2 for service dropdowns
    function initializeServiceSelect2(element) {
        const selector = element ? $(element) : $('.service-select[data-search="true"]');

        // Create data array for top services
        const topServicesData = [];  // Initialize as empty array

        // Add AJAX functionality to load services
        selector.select2({
            theme: 'bootstrap-5',
            placeholder: 'Select a service',
            allowClear: true,
            width: '100%',
            data: topServicesData,
            ajax: {
                url: base_url + 'billing/searchServices',
                type: 'POST',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        term: params.term || '',
                        page: params.page || 1
                    };
                },
                processResults: function(data, params) {
                    return {
                        results: data,
                        pagination: {
                            more: false
                        }
                    };
                },
                cache: true
            }
        });

        // Add select2:select event handler to update price when a service is selected
        selector.on('select2:select', function(e) {
            const data = e.params.data;
            if (data && data.price) {
                const row = $(this).closest('.service-row');
                row.find('.service-price').val(data.price);
                calculateTotals();
            }
        });

        // Add select2:clear event handler to clear the price when selection is cleared
        selector.on('select2:clear', function() {
            const row = $(this).closest('.service-row');
            row.find('.service-price').val('');
            calculateTotals();
        });
    }

    // Function to initialize select2 for product dropdowns
    function initializeProductSelect2(element) {
        const selector = element ? $(element) : $('.product-select[data-search="true"]');

        // Create data array for top products
        const topProductsData = [];  // Initialize as empty array

        // Add AJAX functionality to load products
        console.log('Initializing product select2 with URL:', base_url + 'billing/searchProducts');

        selector.select2({
            theme: 'bootstrap-5',
            placeholder: 'Select a product',
            allowClear: true,
            width: '100%',
            data: topProductsData,
            ajax: {
                url: base_url + 'billing/searchProducts',
                type: 'POST',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    console.log('Product search params:', params);
                    return {
                        term: params.term || '',
                        page: params.page || 1
                    };
                },
                processResults: function(data, params) {
                    console.log('Product search results:', data);
                    return {
                        results: data,
                        pagination: {
                            more: false
                        }
                    };
                },
                error: function(xhr, status, error) {
                    console.error('Error in product search:', status, error);
                },
                cache: true
            }
        });

        // Add select2:select event handler to update price and stock when a product is selected
        selector.on('select2:select', function(e) {
            const data = e.params.data;
            if (data) {
                const row = $(this).closest('.product-row');
                if (data.price) {
                    row.find('.product-price').val(data.price);
                }
                if (data.stock) {
                    const quantityInput = row.find('.product-quantity')[0];
                    quantityInput.max = data.stock;
                    if (parseInt(quantityInput.value) > parseInt(data.stock)) {
                        quantityInput.value = data.stock;
                    }
                }
                updateProductTotal(row.find('.product-price')[0]);
            }
        });

        // Add select2:clear event handler to clear the price when selection is cleared
        selector.on('select2:clear', function() {
            const row = $(this).closest('.product-row');
            row.find('.product-price').val('');
            row.find('.product-total').val('');
            calculateTotals();
        });
    }

    // Function to initialize select2 for customer dropdown
    function initializeCustomerSelect2() {
        // Create data array for top customers
        const topCustomersData = [];  // Initialize as empty array

        // Initialize select2
        $('#customer_id').select2({
            theme: 'bootstrap-5',
            placeholder: 'Select a customer',
            allowClear: true,
            width: '100%',
            data: topCustomersData,
            ajax: {
                url: base_url + 'billing/searchCustomers',
                type: 'POST',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        term: params.term || '',
                        page: params.page || 1
                    };
                },
                processResults: function(data, params) {
                    return {
                        results: data,
                        pagination: {
                            more: false
                        }
                    };
                },
                cache: true
            }
        });

        // Add event handler for when a customer is selected
        $('#customer_id').on('select2:select', function(e) {
            // First reset any existing membership discount
            resetMembershipDiscount();

            const data = e.params.data;

            // Load customer wallet balance
            if (data && data.id) {
                loadCustomerWalletBalance(data.id);
            }
            if (data && data.id) {
                // Check if the selected customer has a membership
                checkCustomerMembership(data.id);
            }
        });

        // Add event handler for when a customer is unselected/cleared
        $('#customer_id').on('select2:clear', function() {
            // Reset membership discount
            resetMembershipDiscount();
        });
    }

    // Function to reset membership discount
    function resetMembershipDiscount() {
        console.log('Resetting membership discount');

        // Reset membership data
        window.customerMembership = null;

        // Hide membership discount info
        document.getElementById('membership_discount_info').style.display = 'none';
        document.getElementById('membership_discount_text').textContent = '';

        // Reset discount to 0 if it was set by membership
        if (document.getElementById('manual_discount_info') === null) {
            document.getElementById('discount').value = '0';
        }

        // Recalculate totals
        calculateTotals(true);
    }

    // Function to check if customer has a membership and apply discount
    function checkCustomerMembership(customerId) {
        // Reset membership discount info
        const discountInfo = document.getElementById('membership_discount_info');
        const discountText = document.getElementById('membership_discount_text');
        discountInfo.style.display = 'none';
        discountText.textContent = '';

        // Make AJAX request to get customer membership
        fetch(base_url + 'billing/getCustomerMembership', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: 'customer_id=' + encodeURIComponent(customerId)
        })
        .then(response => {
            return response.json();
        })
        .then(data => {
            if (data.success && data.has_membership) {
                // Customer has an active membership
                const membership = data.membership;

                // Store membership info for later use
                window.customerMembership = membership;

                // Display membership info
                document.getElementById('membership_discount_info').style.display = 'block';
                document.getElementById('membership_discount_text').textContent =
                    `${membership.name} membership holder: ${membership.service_discount}% off services, ${membership.product_discount}% off products`;

                // Apply membership discount
                applyMembershipDiscount();
            } else {
                // Customer doesn't have an active membership
                window.customerMembership = null;
            }
        })
        .catch(error => {
            console.error('Error checking membership:', error);
        });

    }

    // Function to apply membership discount
    function applyMembershipDiscount() {
        if (!window.customerMembership) {
            return;
        }

        // Calculate the weighted average discount based on services and products
        let serviceTotal = 0;
        let productTotal = 0;

        // Calculate services subtotal
        document.querySelectorAll('.service-row').forEach(function(row) {
            const priceInput = row.querySelector('.service-price');
            const quantityInput = row.querySelector('.service-quantity');

            if (priceInput.value && quantityInput.value) {
                const price = parseFloat(priceInput.value);
                const quantity = parseInt(quantityInput.value);
                serviceTotal += price * quantity;
            }
        });

        // Calculate products subtotal
        document.querySelectorAll('.product-row').forEach(function(row) {
            const totalInput = row.querySelector('.product-total');

            if (totalInput.value) {
                productTotal += parseFloat(totalInput.value);
            }
        });

        const subtotal = serviceTotal + productTotal;

        if (subtotal > 0) {
            // Get discount percentages, ensuring they are numbers
            const serviceDiscountPercent = parseFloat(window.customerMembership.service_discount) || 0;
            const productDiscountPercent = parseFloat(window.customerMembership.product_discount) || 0;

            // Calculate weighted discount
            let weightedDiscount = 0;

            // If there are only services, use service discount
            if (serviceTotal > 0 && productTotal === 0) {
                weightedDiscount = serviceDiscountPercent;
            }
            // If there are only products, use product discount
            else if (productTotal > 0 && serviceTotal === 0) {
                weightedDiscount = productDiscountPercent;
            }
            // If there are both, calculate weighted average
            else if (serviceTotal > 0 && productTotal > 0) {
                const serviceDiscount = (serviceTotal / subtotal) * serviceDiscountPercent;
                const productDiscount = (productTotal / subtotal) * productDiscountPercent;
                weightedDiscount = serviceDiscount + productDiscount;
            }

            // Apply the discount
            document.getElementById('discount').value = weightedDiscount.toFixed(2);

            // Recalculate totals with skipMembershipCheck=true to avoid infinite loop
            calculateTotals(true);
        }
    }

    // Add event listener for coupon application
    document.getElementById('apply_coupon').addEventListener('click', function() {
        const couponCode = document.getElementById('coupon_code').value.trim();
        const subtotal = parseFloat(document.getElementById('subtotal').value) || 0;
        const messageContainer = document.getElementById('coupon_message');

        if (!couponCode) {
            messageContainer.innerHTML = '<div class="text-danger small">Please enter a coupon code</div>';
            return;
        }

        // Clear previous messages
        messageContainer.innerHTML = '<div class="text-info small">Validating coupon...</div>';

        // Disable the apply button during validation
        document.getElementById('apply_coupon').disabled = true;

        // Make AJAX request to validate coupon
        fetch(base_url + 'coupons/validate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: 'code=' + encodeURIComponent(couponCode) + '&amount=' + subtotal
        })
        .then(response => response.json())
        .then(data => {
            if (data.valid) {
                // Apply the discount
                if (data.coupon.discount_type === 'percentage') {
                    document.getElementById('discount').value = data.coupon.discount_value;
                } else {
                    // For fixed amount coupons, calculate the equivalent percentage
                    const discountPercentage = (data.discount / subtotal) * 100;
                    document.getElementById('discount').value = discountPercentage.toFixed(2);
                }

                // Store the coupon ID
                document.getElementById('coupon_id').value = data.coupon.id;

                // Show success message
                messageContainer.innerHTML = '<div class="text-success small">' + data.message + '</div>';

                // Recalculate totals
                calculateTotals();
            } else {
                // Show error message
                messageContainer.innerHTML = '<div class="text-danger small">' + data.message + '</div>';

                // Reset coupon ID
                document.getElementById('coupon_id').value = '';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            messageContainer.innerHTML = '<div class="text-danger small">An error occurred while validating the coupon</div>';
        })
        .finally(() => {
            // Re-enable the apply button
            document.getElementById('apply_coupon').disabled = false;
        });
    });

    // Set payment status based on payment amount
    document.getElementById('payment_amount').addEventListener('input', function() {
        const total = parseFloat(document.getElementById('total_amount').value) || 0;
        const payment = parseFloat(this.value) || 0;

        if (payment >= total) {
            document.getElementById('status').value = 'paid';
        } else if (payment > 0) {
            document.getElementById('status').value = 'partial';
        } else {
            document.getElementById('status').value = 'unpaid';
        }
    });

    // Note: First service row is now included directly in the HTML and initialized in the DOMContentLoaded event

    // Add first product row and initialize it
    document.getElementById('add-product').click();

    // Add a fallback option for products if none are loaded via AJAX
    setTimeout(function() {
        // Check if any products were loaded
        const productSelects = document.querySelectorAll('.product-select');
        productSelects.forEach(function(select) {
            if (select.options.length <= 1) { // Only has the placeholder option
                console.log('No products loaded via AJAX, fetching directly');

                // Make a direct fetch request to get products
                fetch(base_url + 'billing/searchProducts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Direct product fetch results:', data);

                    // Add the products to the dropdown
                    if (data && data.length > 0) {
                        data.forEach(function(product) {
                            const option = new Option(product.text, product.id, false, false);
                            option.dataset.price = product.price;
                            option.dataset.stock = product.stock;
                            select.add(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error fetching products directly:', error);
                });
            }
        });
    }, 1000); // Wait 1 second to allow AJAX to complete

    // Wallet-related variables
    let customerWalletBalance = 0;

    // Load customer wallet balance
    function loadCustomerWalletBalance(customerId) {
        fetch(`<?= base_url('wallet/get-balance/') ?>${customerId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    customerWalletBalance = parseFloat(data.balance) || 0;
                    document.getElementById('customer-wallet-balance').textContent = data.formatted_balance;
                    updateWalletPaymentSection();
                } else {
                    customerWalletBalance = 0;
                    document.getElementById('customer-wallet-balance').textContent = '₹0.00';
                }
            })
            .catch(error => {
                console.error('Error fetching wallet balance:', error);
                customerWalletBalance = 0;
                document.getElementById('customer-wallet-balance').textContent = '₹0.00';
            });
    }

    // Update wallet payment section visibility
    function updateWalletPaymentSection() {
        const paymentMethod = document.getElementById('payment_method').value;
        const walletSection = document.getElementById('wallet-payment-section');
        const totalAmount = parseFloat(document.getElementById('total_amount').value) || 0;

        if (paymentMethod === 'wallet' || paymentMethod === 'mixed') {
            // Check if wallet balance is sufficient for wallet-only payment
            if (paymentMethod === 'wallet' && customerWalletBalance < totalAmount) {
                // Show warning and suggest mixed payment
                alert(`Insufficient wallet balance!\n\nWallet Balance: ₹${customerWalletBalance.toFixed(2)}\nBill Amount: ₹${totalAmount.toFixed(2)}\n\nPlease use "Mixed Payment" option or add money to wallet first.`);
                document.getElementById('payment_method').value = 'mixed';
                walletSection.style.display = 'block';
                updateWalletCalculations();
                return;
            }

            walletSection.style.display = 'block';
            updateWalletCalculations();
        } else {
            walletSection.style.display = 'none';
            document.getElementById('wallet_amount').value = 0;
        }
    }

    // Update wallet calculations
    function updateWalletCalculations() {
        const totalAmount = parseFloat(document.getElementById('total_amount').value) || 0;
        const walletAmount = parseFloat(document.getElementById('wallet_amount').value) || 0;
        const remainingAmount = Math.max(0, totalAmount - walletAmount);

        document.getElementById('remaining-amount').textContent = `₹${remainingAmount.toFixed(2)}`;

        // Show/hide insufficient balance badge
        const insufficientBadge = document.getElementById('wallet-insufficient-badge');
        if (customerWalletBalance < totalAmount && totalAmount > 0) {
            insufficientBadge.style.display = 'inline-block';
        } else {
            insufficientBadge.style.display = 'none';
        }

        // Update payment amount to reflect wallet + remaining
        const paymentMethod = document.getElementById('payment_method').value;
        if (paymentMethod === 'wallet' && walletAmount >= totalAmount) {
            document.getElementById('payment_amount').value = totalAmount.toFixed(2);
        } else if (paymentMethod === 'mixed') {
            document.getElementById('payment_amount').value = totalAmount.toFixed(2);
        }
    }

    // Payment method change handler
    document.getElementById('payment_method').addEventListener('change', function() {
        updateWalletPaymentSection();
    });

    // Wallet amount change handler
    document.getElementById('wallet_amount').addEventListener('input', function() {
        const walletAmount = parseFloat(this.value) || 0;
        const maxWallet = Math.min(customerWalletBalance, parseFloat(document.getElementById('total_amount').value) || 0);

        if (walletAmount > maxWallet) {
            this.value = maxWallet.toFixed(2);
        }

        updateWalletCalculations();
    });

    // Use full wallet balance button
    document.getElementById('use-full-wallet').addEventListener('click', function() {
        const totalAmount = parseFloat(document.getElementById('total_amount').value) || 0;
        const maxWallet = Math.min(customerWalletBalance, totalAmount);
        document.getElementById('wallet_amount').value = maxWallet.toFixed(2);
        updateWalletCalculations();
    });

    // Form submission validation
    document.getElementById('invoice-form').addEventListener('submit', function(e) {
        const customerId = document.getElementById('customer_id').value;
        const serviceRows = document.querySelectorAll('.service-row');
        const productRows = document.querySelectorAll('.product-row');

        let hasServices = false;
        let hasProducts = false;

        // Check if at least one service is selected
        serviceRows.forEach(function(row) {
            const serviceSelect = row.querySelector('.service-select');
            if (serviceSelect.value) {
                hasServices = true;
            }
        });

        // Check if at least one product is selected
        productRows.forEach(function(row) {
            const productSelect = row.querySelector('.product-select');
            if (productSelect.value) {
                hasProducts = true;
            }
        });

        // Validate wallet payment
        const paymentMethod = document.getElementById('payment_method').value;
        const walletAmount = parseFloat(document.getElementById('wallet_amount').value) || 0;

        if ((paymentMethod === 'wallet' || paymentMethod === 'mixed') && walletAmount > 0) {
            if (walletAmount > customerWalletBalance) {
                alert('Wallet amount cannot exceed customer wallet balance');
                e.preventDefault();
                return false;
            }
        }

        if (!customerId) {
            alert('Please select a customer');
            e.preventDefault();
            return false;
        }

        if (!hasServices && !hasProducts) {
            alert('Please add at least one service or product');
            e.preventDefault();
            return false;
        }

        return true;
    });
});
</script>

<style>
/* Custom styles for the invoice form */
.form-control:focus, .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.service-row, .product-row {
    transition: all 0.3s ease;
}

.service-row:hover, .product-row:hover {
    background-color: #f8f9fa !important;
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.btn-outline-primary:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Fix for Select2 in Bootstrap 5 */
.select2-container {
    width: 100% !important;
}

.select2-container--bootstrap-5 .select2-selection {
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}
.mdt-bold {
    font-weight: 400;
    font-size: 0.75rem;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
    padding-left: 0;
    line-height: 1.5;
    color: #495057;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
    height: calc(1.5em + 0.75rem);
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__placeholder {
    color: #6c757d;
}

.select2-container--bootstrap-5 .select2-dropdown {
    border-color: #80bdff;
    border-radius: 0.25rem;
}

.select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
    background-color: #007bff;
}

/* Fix input group with select2 */
.input-group > .select2-container--bootstrap-5 {
    flex: 1 1 auto;
    width: 1% !important;
}

.input-group > .select2-container--bootstrap-5 .select2-selection {
    height: 100%;
    line-height: 1.5;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* Fix for date input */
input[type="date"].form-control {
    height: 38px;
    padding: 0.375rem 0.75rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .service-row, .product-row {
        margin-bottom: 1.5rem !important;
        padding: 1rem !important;
        border: 1px solid #dee2e6;
    }

    .select2-container--bootstrap-5 {
        width: 100% !important;
    }
}
</style>





