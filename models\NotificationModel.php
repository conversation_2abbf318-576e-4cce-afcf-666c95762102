<?php
/**
 * Notification Model
 */
class NotificationModel extends Model {
    protected $table = 'notifications';
    
    /**
     * Get unread notifications for a user
     * 
     * @param int $user_id User ID
     * @param int $limit Limit number of notifications
     * @return array Notifications
     */
    public function getUnreadForUser($user_id, $limit = 5) {
        $query = "SELECT * FROM " . $this->table . " 
                 WHERE user_id = :user_id AND is_read = 0 
                 ORDER BY created_at DESC 
                 LIMIT :limit";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get all notifications for a user
     * 
     * @param int $user_id User ID
     * @param int $limit Limit number of notifications
     * @return array Notifications
     */
    public function getAllForUser($user_id, $limit = 10) {
        $query = "SELECT * FROM " . $this->table . " 
                 WHERE user_id = :user_id 
                 ORDER BY created_at DESC 
                 LIMIT :limit";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Count unread notifications for a user
     * 
     * @param int $user_id User ID
     * @return int Count
     */
    public function countUnread($user_id) {
        $query = "SELECT COUNT(*) FROM " . $this->table . " 
                 WHERE user_id = :user_id AND is_read = 0";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchColumn();
    }
    
    /**
     * Mark notification as read
     * 
     * @param int $id Notification ID
     * @return bool Success
     */
    public function markAsRead($id) {
        $query = "UPDATE " . $this->table . " SET is_read = 1 WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        
        return $stmt->execute();
    }
    
    /**
     * Mark all notifications as read for a user
     * 
     * @param int $user_id User ID
     * @return bool Success
     */
    public function markAllAsRead($user_id) {
        $query = "UPDATE " . $this->table . " SET is_read = 1 WHERE user_id = :user_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        
        return $stmt->execute();
    }
    
    /**
     * Create a new notification
     * 
     * @param array $data Notification data
     * @return int|false Notification ID or false on failure
     */
    public function create($data) {
        $query = "INSERT INTO " . $this->table . " (user_id, type, message, link, is_read, created_at) 
                 VALUES (:user_id, :type, :message, :link, :is_read, NOW())";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $data['user_id'], PDO::PARAM_INT);
        $stmt->bindParam(':type', $data['type']);
        $stmt->bindParam(':message', $data['message']);
        $stmt->bindParam(':link', $data['link']);
        $stmt->bindValue(':is_read', 0, PDO::PARAM_INT);
        
        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }
}
