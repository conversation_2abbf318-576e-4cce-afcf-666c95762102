<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-user-edit"></i> Edit Customer</h1>
        <div>
            <a href="<?= base_url('customers/view/' . $customer['id']) ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Customer
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <form action="<?= base_url('customers/update/' . $customer['id']) ?>" method="post" autocomplete="off">
                <!-- Hidden field to prevent browser autofill -->
                <input type="text" style="display:none" name="prevent_autofill" id="prevent_autofill" value="" />
                <div class="row">
                    <!-- Personal Information -->
                    <div class="col-md-12 mb-4">
                        <h5 class="border-bottom pb-2">Personal Information</h5>
                    </div>

                    <!-- Name -->
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="name" name="name" value="<?= $customer['name'] ?>" required>
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                            <input type="email" class="form-control" id="email" name="email" value="<?= $customer['email'] ?>" required>
                        </div>
                    </div>

                    <!-- Phone -->
                    <div class="col-md-6 mb-3">
                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                            <input type="tel" class="form-control" id="phone" name="phone" value="<?= $customer['phone'] ?>" required>
                        </div>
                    </div>

                    <!-- Gender -->
                    <div class="col-md-6 mb-3">
                        <label for="gender" class="form-label">Gender <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-venus-mars"></i></span>
                            <select class="form-select" id="gender" name="gender" required>
                                <option value="">Select Gender</option>
                                <option value="male" <?= $customer['gender'] == 'male' ? 'selected' : '' ?>>Male</option>
                                <option value="female" <?= $customer['gender'] == 'female' ? 'selected' : '' ?>>Female</option>
                                <option value="other" <?= $customer['gender'] == 'other' ? 'selected' : '' ?>>Other</option>
                            </select>
                        </div>
                    </div>

                    <!-- Date of Birth -->
                    <div class="col-md-6 mb-3">
                        <label for="date_of_birth" class="form-label">Date of Birth</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth"
                                value="<?= (!empty($customer['date_of_birth']) && $customer['date_of_birth'] != '0000-00-00' && $customer['date_of_birth'] != null) ? $customer['date_of_birth'] : '' ?>">
                        </div>
                        <div class="form-text">Format: YYYY-MM-DD (leave empty if not applicable)</div>
                    </div>

                    <!-- Address -->
                    <div class="col-md-6 mb-3">
                        <label for="address" class="form-label">Address</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                            <input type="text" class="form-control" id="address" name="address" value="<?= $customer['address'] ?>">
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-toggle-on"></i></span>
                            <select class="form-select" id="status" name="status" required>
                                <option value="active" <?= $customer['status'] == 'active' ? 'selected' : '' ?>>Active</option>
                                <option value="inactive" <?= $customer['status'] == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                        </div>
                    </div>

                    <!-- Account Information -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2">Account Information</h5>
                        <p class="text-muted small">Leave password fields empty if you don't want to change the password.</p>
                    </div>

                    <!-- Password -->
                    <div class="col-md-6 mb-3">
                        <label for="password" class="form-label">New Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="password" name="password" autocomplete="new-password">
                        </div>
                        <div class="form-text">Password must be at least 6 characters long</div>
                    </div>

                    <!-- Confirm Password -->
                    <div class="col-md-6 mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" autocomplete="new-password">
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-md-12 mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Customer
                        </button>
                        <a href="<?= base_url('customers/view/' . $customer['id']) ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
