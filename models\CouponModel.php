<?php
/**
 * Coupon Model
 */
class CouponModel extends Model {
    protected $table = 'coupons';
    
    /**
     * Get all coupons
     * 
     * @return array Coupons
     */
    public function all() {
        $query = "SELECT * FROM " . $this->table . " ORDER BY created_at DESC";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Find coupon by ID
     * 
     * @param int $id Coupon ID
     * @return array|false Coupon data or false if not found
     */
    public function find($id) {
        $query = "SELECT * FROM " . $this->table . " WHERE id = :id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Find coupon by code
     * 
     * @param string $code Coupon code
     * @return array|false Coupon data or false if not found
     */
    public function findByCode($code) {
        $query = "SELECT * FROM " . $this->table . " WHERE code = :code LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':code', $code);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Check if coupon code exists
     * 
     * @param string $code Coupon code
     * @return bool True if exists, false otherwise
     */
    public function codeExists($code) {
        $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE code = :code";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':code', $code);
        $stmt->execute();
        
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * Get usage count for a coupon
     * 
     * @param int $coupon_id Coupon ID
     * @return int Usage count
     */
    public function getUsageCount($coupon_id) {
        $query = "SELECT COUNT(*) FROM coupon_usage WHERE coupon_id = :coupon_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':coupon_id', $coupon_id);
        $stmt->execute();
        
        return $stmt->fetchColumn();
    }
    
    /**
     * Record coupon usage
     * 
     * @param int $coupon_id Coupon ID
     * @param int $invoice_id Invoice ID
     * @param float $discount_amount Discount amount
     * @return bool Success or failure
     */
    public function recordUsage($coupon_id, $invoice_id, $discount_amount) {
        $query = "INSERT INTO coupon_usage (coupon_id, invoice_id, discount_amount) VALUES (:coupon_id, :invoice_id, :discount_amount)";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':coupon_id', $coupon_id);
        $stmt->bindParam(':invoice_id', $invoice_id);
        $stmt->bindParam(':discount_amount', $discount_amount);
        
        return $stmt->execute();
    }
    
    /**
     * Validate coupon
     * 
     * @param string $code Coupon code
     * @param float $amount Purchase amount
     * @return array Validation result
     */
    public function validateCoupon($code, $amount) {
        // Find coupon by code
        $coupon = $this->findByCode(strtoupper($code));
        
        if (!$coupon) {
            return ['valid' => false, 'message' => 'Invalid coupon code'];
        }
        
        // Check if coupon is active
        if ($coupon['status'] != 'active') {
            return ['valid' => false, 'message' => 'This coupon is inactive'];
        }
        
        // Check validity period
        $today = date('Y-m-d');
        if (!empty($coupon['valid_from']) && $today < $coupon['valid_from']) {
            return ['valid' => false, 'message' => 'This coupon is not yet valid'];
        }
        
        if (!empty($coupon['valid_to']) && $today > $coupon['valid_to']) {
            return ['valid' => false, 'message' => 'This coupon has expired'];
        }
        
        // Check usage limit
        if (!empty($coupon['usage_limit'])) {
            $usage_count = $this->getUsageCount($coupon['id']);
            if ($usage_count >= $coupon['usage_limit']) {
                return ['valid' => false, 'message' => 'This coupon has reached its usage limit'];
            }
        }
        
        // Check minimum purchase
        if (!empty($coupon['min_purchase']) && $amount < $coupon['min_purchase']) {
            return ['valid' => false, 'message' => 'Minimum purchase amount of ' . format_currency($coupon['min_purchase']) . ' required'];
        }
        
        // Calculate discount
        $discount = 0;
        if ($coupon['discount_type'] == 'fixed') {
            $discount = $coupon['discount_value'];
        } else {
            $discount = ($amount * $coupon['discount_value']) / 100;
            
            // Apply maximum discount if set
            if (!empty($coupon['max_discount']) && $discount > $coupon['max_discount']) {
                $discount = $coupon['max_discount'];
            }
        }
        
        // Ensure discount doesn't exceed purchase amount
        if ($discount > $amount) {
            $discount = $amount;
        }
        
        return [
            'valid' => true,
            'coupon' => $coupon,
            'discount' => $discount,
            'message' => 'Coupon applied successfully'
        ];
    }
}
