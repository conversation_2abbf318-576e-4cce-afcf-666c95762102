<?php
/**
 * Database Check Script
 *
 * This script checks the database structure for invoice_services table
 */

// Create direct database connection
try {
    $db = new PDO(
        "mysql:host=localhost;dbname=salon_db",
        "root",
        ""
    );
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db->exec("set names utf8");
} catch(PDOException $e) {
    echo "Connection error: " . $e->getMessage() . "\n";
    exit;
}

try {
    // Check if invoice_services table exists
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "Tables in database:\n";
    foreach ($tables as $table) {
        echo "- $table\n";
    }
    echo "\n";

    // Check invoice_services structure if it exists
    if (in_array('invoice_services', $tables)) {
        echo "Structure of invoice_services table:\n";
        $columns = $db->query("DESCRIBE invoice_services")->fetchAll(PDO::FETCH_ASSOC);
        foreach ($columns as $column) {
            echo "- {$column['Field']} ({$column['Type']})\n";
        }
        echo "\n";

        // Check if there are any records
        $count = $db->query("SELECT COUNT(*) FROM invoice_services")->fetchColumn();
        echo "Number of records in invoice_services: $count\n\n";

        if ($count > 0) {
            // Show a sample record
            echo "Sample record from invoice_services:\n";
            $sample = $db->query("SELECT * FROM invoice_services LIMIT 1")->fetch(PDO::FETCH_ASSOC);
            foreach ($sample as $key => $value) {
                echo "- $key: $value\n";
            }
            echo "\n";

            // Check if there are any staff_id values
            $staffCount = $db->query("SELECT COUNT(*) FROM invoice_services WHERE staff_id IS NOT NULL")->fetchColumn();
            echo "Number of records with staff_id: $staffCount\n\n";

            if ($staffCount > 0) {
                // Show a sample record with staff_id
                echo "Sample record with staff_id:\n";
                $staffSample = $db->query("SELECT * FROM invoice_services WHERE staff_id IS NOT NULL LIMIT 1")->fetch(PDO::FETCH_ASSOC);
                foreach ($staffSample as $key => $value) {
                    echo "- $key: $value\n";
                }
            }
        }
    } else {
        echo "invoice_services table does not exist!\n";
    }

    // Check invoices table structure
    if (in_array('invoices', $tables)) {
        echo "\nStructure of invoices table:\n";
        $columns = $db->query("DESCRIBE invoices")->fetchAll(PDO::FETCH_ASSOC);
        foreach ($columns as $column) {
            echo "- {$column['Field']} ({$column['Type']})\n";
        }

        // Check if there are any records
        $count = $db->query("SELECT COUNT(*) FROM invoices")->fetchColumn();
        echo "\nNumber of records in invoices: $count\n";

        if ($count > 0) {
            // Check payment_status values
            $statusCounts = $db->query("SELECT payment_status, COUNT(*) as count FROM invoices GROUP BY payment_status")->fetchAll(PDO::FETCH_ASSOC);
            echo "\nPayment status counts:\n";
            foreach ($statusCounts as $status) {
                echo "- {$status['payment_status']}: {$status['count']}\n";
            }
        }
    }

    // Check staff table structure
    if (in_array('staff', $tables)) {
        echo "\nStructure of staff table:\n";
        $columns = $db->query("DESCRIBE staff")->fetchAll(PDO::FETCH_ASSOC);
        foreach ($columns as $column) {
            echo "- {$column['Field']} ({$column['Type']})\n";
        }

        // Check if there are any records
        $count = $db->query("SELECT COUNT(*) FROM staff")->fetchColumn();
        echo "\nNumber of records in staff: $count\n";

        if ($count > 0) {
            // Show commission rates
            $commissionRates = $db->query("SELECT id, commission_rate FROM staff")->fetchAll(PDO::FETCH_ASSOC);
            echo "\nStaff commission rates:\n";
            foreach ($commissionRates as $staff) {
                echo "- Staff ID {$staff['id']}: {$staff['commission_rate']}%\n";
            }
        }
    }

} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
