<?php
/**
 * Environment Detection
 *
 * This file detects whether the application is running on a local or live server
 * and sets the appropriate environment constants.
 */

// List of local server hostnames
$local_hostnames = [
    'localhost',
    '127.0.0.1',
    '::1'
];

// Get the server hostname
$server_hostname = $_SERVER['HTTP_HOST'] ?? '';

// Determine the environment
if (in_array($server_hostname, $local_hostnames) ||
    strpos($server_hostname, '.local') !== false ||
    strpos($server_hostname, '.test') !== false) {
    // Local environment
    define('ENVIRONMENT', 'development');
} else {
    // Live environment
    define('ENVIRONMENT', 'production');
}

// Set error reporting based on environment
if (ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    // Temporarily enable errors in production to debug
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    // Original settings:
    // error_reporting(0);
    // ini_set('display_errors', 0);
}

/**
 * Get the base URL of the application
 *
 * @return string The base URL with trailing slash
 */
function get_base_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];

    // For local development, use a fixed path
    if (ENVIRONMENT === 'development') {
        if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
            // Use a fixed path for local development
            $path = '/projects/SALON5/';

            // Log the base URL for debugging
            error_log("Base URL set to: $protocol://$host$path");
            return "$protocol://$host$path";
        }
    }

    // For production or other environments, use the script path
    $path = dirname($_SERVER['SCRIPT_NAME']);
    $path = $path === '/' || $path === '\\' ? '' : $path;

    // Ensure trailing slash
    $path = rtrim($path, '/') . '/';

    return "$protocol://$host$path";
}

// Define the base URL
define('BASE_URL', get_base_url());

// Define the base path if not already defined
if (!defined('BASE_PATH')) {
    define('BASE_PATH', realpath(dirname(__FILE__) . '/../'));
}
?>



