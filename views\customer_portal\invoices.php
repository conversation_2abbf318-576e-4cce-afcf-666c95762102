<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-file-invoice-dollar"></i> My Invoices</h1>
        <div>
            <a href="<?= base_url('customer') ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-header bg-white">
            <ul class="nav nav-tabs card-header-tabs" id="invoiceTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="true">
                        <i class="fas fa-list"></i> All Invoices
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="unpaid-tab" data-bs-toggle="tab" data-bs-target="#unpaid" type="button" role="tab" aria-controls="unpaid" aria-selected="false">
                        <i class="fas fa-exclamation-circle"></i> Unpaid
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="paid-tab" data-bs-toggle="tab" data-bs-target="#paid" type="button" role="tab" aria-controls="paid" aria-selected="false">
                        <i class="fas fa-check-circle"></i> Paid
                    </button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="invoiceTabsContent">
                <!-- All Invoices -->
                <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
                    <?php if (empty($invoices)): ?>
                        <div class="text-center p-5">
                            <img src="<?= base_url('assets/images/invoice.svg') ?>" alt="No Invoices" style="width: 150px; opacity: 0.5;" class="mb-3">
                            <h5>No Invoices Found</h5>
                            <p class="text-muted">You don't have any invoices yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Invoice #</th>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($invoices as $invoice): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold">#<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?></div>
                                            </td>
                                            <td><?= format_date($invoice['invoice_date']) ?></td>
                                            <td><?= format_currency($invoice['total_amount']) ?></td>
                                            <td>
                                                <span class="badge <?= get_payment_status_class($invoice['payment_status']) ?>">
                                                    <?= ucfirst($invoice['payment_status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('customer/view-invoice/' . $invoice['id']) ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Unpaid Invoices -->
                <div class="tab-pane fade" id="unpaid" role="tabpanel" aria-labelledby="unpaid-tab">
                    <?php
                    $unpaid_invoices = array_filter($invoices, function($invoice) {
                        return $invoice['payment_status'] == 'unpaid' || $invoice['payment_status'] == 'partial';
                    });
                    ?>
                    
                    <?php if (empty($unpaid_invoices)): ?>
                        <div class="text-center p-5">
                            <img src="<?= base_url('assets/images/invoice-paid.svg') ?>" alt="No Unpaid Invoices" style="width: 150px; opacity: 0.5;" class="mb-3">
                            <h5>No Unpaid Invoices</h5>
                            <p class="text-muted">You don't have any unpaid invoices. Great job!</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Invoice #</th>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($unpaid_invoices as $invoice): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold">#<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?></div>
                                            </td>
                                            <td><?= format_date($invoice['invoice_date']) ?></td>
                                            <td><?= format_currency($invoice['total_amount']) ?></td>
                                            <td>
                                                <span class="badge <?= get_payment_status_class($invoice['payment_status']) ?>">
                                                    <?= ucfirst($invoice['payment_status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('customer/view-invoice/' . $invoice['id']) ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Paid Invoices -->
                <div class="tab-pane fade" id="paid" role="tabpanel" aria-labelledby="paid-tab">
                    <?php
                    $paid_invoices = array_filter($invoices, function($invoice) {
                        return $invoice['payment_status'] == 'paid';
                    });
                    ?>
                    
                    <?php if (empty($paid_invoices)): ?>
                        <div class="text-center p-5">
                            <img src="<?= base_url('assets/images/invoice.svg') ?>" alt="No Paid Invoices" style="width: 150px; opacity: 0.5;" class="mb-3">
                            <h5>No Paid Invoices</h5>
                            <p class="text-muted">You don't have any paid invoices yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Invoice #</th>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($paid_invoices as $invoice): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold">#<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?></div>
                                            </td>
                                            <td><?= format_date($invoice['invoice_date']) ?></td>
                                            <td><?= format_currency($invoice['total_amount']) ?></td>
                                            <td>
                                                <span class="badge bg-success">Paid</span>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('customer/view-invoice/' . $invoice['id']) ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Helper function
function get_payment_status_class($status) {
    switch ($status) {
        case 'paid':
            return 'bg-success';
        case 'partial':
            return 'bg-info';
        case 'unpaid':
            return 'bg-warning';
        default:
            return 'bg-secondary';
    }
}
?>
