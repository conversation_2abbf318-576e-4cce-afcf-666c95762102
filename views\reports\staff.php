<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-users"></i> Staff Performance Report</h1>
    </div>

    <!-- Date Range Filter -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('reports/staff') ?>" method="get" class="row align-items-end">
                <div class="col-md-4 mb-2">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                </div>
                <div class="col-md-4 mb-2">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                </div>
                <div class="col-md-4 mb-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i> Apply Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <!-- Reports Navigation -->
        <div class="col-md-3 mb-4">
            <?php include 'partials/reports_nav.php'; ?>
        </div>
        
        <!-- Reports Content -->
        <div class="col-md-9">
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Services</h6>
                                    <h4 class="mb-0"><?= $staff_performance_report['totals']['service_count'] ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-concierge-bell text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Revenue</h6>
                                    <h4 class="mb-0"><?= format_currency($staff_performance_report['totals']['service_amount']) ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-dollar-sign text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Commission</h6>
                                    <h4 class="mb-0"><?= format_currency($staff_performance_report['totals']['commission_amount']) ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-percentage text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Staff Performance Chart -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Staff Performance by Revenue</h5>
                    <a href="<?= base_url('reports/export?type=staff&start_date=' . $start_date . '&end_date=' . $end_date) ?>" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-download me-1"></i> Export CSV
                    </a>
                </div>
                <div class="card-body">
                    <canvas id="staffChart" height="300"></canvas>
                </div>
            </div>
            
            <!-- Staff Performance Data Table -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Staff Performance Data</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($staff_performance_report['data'])): ?>
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No staff performance data available for the selected period.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Staff</th>
                                        <th>Position</th>
                                        <th class="text-end">Services</th>
                                        <th class="text-end">Invoices</th>
                                        <th class="text-end">Revenue</th>
                                        <th class="text-end">Commission</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($staff_performance_report['data'] as $row): ?>
                                        <tr>
                                            <td><?= $row['staff_name'] ?></td>
                                            <td><?= $row['position'] ?></td>
                                            <td class="text-end"><?= $row['service_count'] ?></td>
                                            <td class="text-end"><?= $row['invoice_count'] ?></td>
                                            <td class="text-end"><?= format_currency($row['service_amount']) ?></td>
                                            <td class="text-end"><?= format_currency($row['commission_amount']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="2">Total</th>
                                        <th class="text-end"><?= $staff_performance_report['totals']['service_count'] ?></th>
                                        <th class="text-end"><?= $staff_performance_report['totals']['invoice_count'] ?></th>
                                        <th class="text-end"><?= format_currency($staff_performance_report['totals']['service_amount']) ?></th>
                                        <th class="text-end"><?= format_currency($staff_performance_report['totals']['commission_amount']) ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Staff Performance Chart
    const staffData = <?= json_encode($staff_performance_report['data']) ?>;
    
    if (staffData.length > 0) {
        // Sort data by service amount in descending order
        staffData.sort((a, b) => b.service_amount - a.service_amount);
        
        const staffLabels = staffData.map(item => item.staff_name);
        const revenueData = staffData.map(item => item.service_amount);
        const commissionData = staffData.map(item => item.commission_amount);
        
        const staffCtx = document.getElementById('staffChart').getContext('2d');
        new Chart(staffCtx, {
            type: 'bar',
            data: {
                labels: staffLabels,
                datasets: [
                    {
                        label: 'Revenue',
                        data: revenueData,
                        backgroundColor: 'rgba(52, 152, 219, 0.7)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Commission',
                        data: commissionData,
                        backgroundColor: 'rgba(46, 204, 113, 0.7)',
                        borderColor: 'rgba(46, 204, 113, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '<?= CURRENCY_SYMBOL ?>' + value;
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': <?= CURRENCY_SYMBOL ?>' + context.raw;
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>
