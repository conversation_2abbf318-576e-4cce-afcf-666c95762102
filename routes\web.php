<?php
/**
 * Web routes
 */

// Auth routes
$router->get('/login', 'AuthController', 'loginForm');
$router->post('/login', 'AuthController', 'login');
$router->get('/logout', 'AuthController', 'logout');
$router->get('/register', 'AuthController', 'registerForm');
$router->post('/register', 'AuthController', 'register');
$router->get('/forgot-password', 'AuthController', 'forgotPasswordForm');
$router->post('/forgot-password', 'AuthController', 'forgotPassword');
$router->get('/reset-password/{token}', 'AuthController', 'resetPasswordForm');
$router->post('/reset-password', 'AuthController', 'resetPassword');

// Dashboard
$router->get('/dashboard', 'DashboardController', 'index');

// Customers
$router->get('/customers', 'CustomerController', 'index');
$router->get('/customers/create', 'CustomerController', 'create');
$router->post('/customers/store', 'CustomerController', 'store');
$router->get('/customers/view/{id}', 'CustomerController', 'view');
$router->get('/customers/edit/{id}', 'CustomerController', 'edit');
$router->post('/customers/update/{id}', 'CustomerController', 'update');
$router->get('/customers/delete/{id}', 'CustomerController', 'delete');
$router->get('/customers/export', 'CustomerController', 'export');
$router->post('/customers/import', 'CustomerController', 'import');

// Services
$router->get('/services', 'ServiceController', 'index');
$router->get('/services/create', 'ServiceController', 'create');
$router->post('/services/store', 'ServiceController', 'store');
$router->get('/services/edit/{id}', 'ServiceController', 'edit');
$router->post('/services/update/{id}', 'ServiceController', 'update');
$router->get('/services/delete/{id}', 'ServiceController', 'delete');
$router->get('/services/export', 'ServiceController', 'export');
$router->post('/services/import', 'ServiceController', 'import');

// Staff
$router->get('/staff', 'StaffController', 'index');
$router->get('/staff/create', 'StaffController', 'create');
$router->post('/staff/store', 'StaffController', 'store');
$router->get('/staff/edit/{id}', 'StaffController', 'edit');
$router->post('/staff/update/{id}', 'StaffController', 'update');
$router->get('/staff/delete/{id}', 'StaffController', 'delete');

// Appointments
$router->get('/appointments', 'AppointmentController', 'index');
$router->get('/appointments/create', 'AppointmentController', 'create');
$router->post('/appointments/store', 'AppointmentController', 'store');
$router->get('/appointments/edit/{id}', 'AppointmentController', 'edit');
$router->post('/appointments/update/{id}', 'AppointmentController', 'update');
$router->get('/appointments/delete/{id}', 'AppointmentController', 'delete');
$router->get('/appointments/calendar', 'AppointmentController', 'calendar');

// Billing
$router->get('/billing', 'BillingController', 'index');
$router->get('/billing/create', 'BillingController', 'create');
$router->post('/billing/store', 'BillingController', 'store');
$router->get('/billing/view/{id}', 'BillingController', 'view');
$router->get('/billing/edit/{id}', 'BillingController', 'edit');
$router->post('/billing/update/{id}', 'BillingController', 'update');
$router->get('/billing/print/{id}', 'BillingController', 'print');
$router->get('/billing/printA4/{id}', 'BillingController', 'printA4');
$router->get('/billing/thermalPrint/{id}', 'BillingController', 'thermalPrint');
$router->post('/billing/update-status/{id}', 'BillingController', 'updateStatus');
$router->get('/billing/mark-as-paid/{id}', 'BillingController', 'markAsPaid');
$router->get('/billing/cancel/{id}', 'BillingController', 'cancel');
$router->post('/billing/searchServices', 'BillingController', 'searchServices');
$router->get('/billing/searchServices', 'BillingController', 'searchServices');
$router->post('/billing/searchCustomers', 'BillingController', 'searchCustomers');
$router->get('/billing/searchCustomers', 'BillingController', 'searchCustomers');
$router->post('/billing/searchProducts', 'BillingController', 'searchProducts');
$router->get('/billing/searchProducts', 'BillingController', 'searchProducts');
$router->post('/billing/getCustomerMembership', 'BillingController', 'getCustomerMembership');
$router->post('/billing/record-payment/{id}', 'BillingController', 'recordPayment');
$router->get('/billing/delete/{id}', 'BillingController', 'delete');

// Inventory
$router->get('/inventory', 'InventoryController', 'index');
$router->get('/inventory/create', 'InventoryController', 'create');
$router->post('/inventory/store', 'InventoryController', 'store');
$router->get('/inventory/view/{id}', 'InventoryController', 'view');
$router->get('/inventory/edit/{id}', 'InventoryController', 'edit');
$router->post('/inventory/update/{id}', 'InventoryController', 'update');
$router->get('/inventory/delete/{id}', 'InventoryController', 'delete');
$router->get('/inventory/adjust-stock/{id}', 'InventoryController', 'adjustStock');
$router->post('/inventory/update-stock/{id}', 'InventoryController', 'updateStock');
$router->get('/inventory/categories', 'InventoryController', 'categories');
$router->post('/inventory/categories/store', 'InventoryController', 'storeCategory');
$router->post('/inventory/categories/update', 'InventoryController', 'updateCategory');
$router->post('/inventory/categories/delete', 'InventoryController', 'deleteCategory');
$router->post('/inventory/categories/store-ajax', 'InventoryController', 'storeCategoryAjax');
$router->get('/inventory/export', 'InventoryController', 'export');
$router->get('/inventory/download-template', 'InventoryController', 'downloadTemplate');
$router->post('/inventory/import', 'InventoryController', 'import');

// Memberships (Legacy - keeping for backward compatibility)
$router->get('/memberships', 'MembershipController', 'index');
$router->get('/memberships/create', 'MembershipController', 'create');
$router->post('/memberships/store', 'MembershipController', 'store');
$router->get('/memberships/edit/{id}', 'MembershipController', 'edit');
$router->post('/memberships/update/{id}', 'MembershipController', 'update');
$router->get('/memberships/delete/{id}', 'MembershipController', 'delete');
$router->get('/memberships/members/{id}', 'MembershipController', 'members');
$router->get('/memberships/add-member/{id}', 'MembershipController', 'addMember');
$router->post('/memberships/store-member/{id}', 'MembershipController', 'storeMember');
$router->get('/memberships/cancel-membership/{id}', 'MembershipController', 'cancelMembership');
$router->get('/memberships/renew-membership/{id}', 'MembershipController', 'renewMembership');

// Discounts (New membership/discount management)
$router->get('/discounts', 'DiscountController', 'index');
$router->get('/discounts/create', 'DiscountController', 'create');
$router->post('/discounts/store', 'DiscountController', 'store');
$router->get('/discounts/edit/{id}', 'DiscountController', 'edit');
$router->post('/discounts/update/{id}', 'DiscountController', 'update');
$router->get('/discounts/delete/{id}', 'DiscountController', 'delete');
$router->get('/discounts/members/{id}', 'DiscountController', 'members');
$router->get('/discounts/add-member/{id}', 'DiscountController', 'addMember');
$router->post('/discounts/store-member/{id}', 'DiscountController', 'storeMember');
$router->get('/discounts/cancel-membership/{id}', 'DiscountController', 'cancelMembership');
$router->get('/discounts/renew-membership/{id}', 'DiscountController', 'renewMembership');

// Wallet System
$router->get('/wallet', 'WalletController', 'index');
$router->get('/wallet/add-money', 'WalletController', 'addMoney');
$router->get('/wallet/add-money/{customer_id}', 'WalletController', 'addMoney');
$router->post('/wallet/store-add-money', 'WalletController', 'storeAddMoney');
$router->get('/wallet/view/{customer_id}', 'WalletController', 'view');
$router->get('/wallet/adjust-balance/{customer_id}', 'WalletController', 'adjustBalance');
$router->post('/wallet/store-adjustment/{customer_id}', 'WalletController', 'storeAdjustment');
$router->get('/wallet/get-balance/{customer_id}', 'WalletController', 'getBalance');
$router->get('/wallet/transactions/{customer_id}', 'WalletController', 'transactions');

// Coupons
$router->get('/coupons', 'CouponController', 'index');
$router->get('/coupons/create', 'CouponController', 'create');
$router->post('/coupons/store', 'CouponController', 'store');
$router->get('/coupons/edit/{id}', 'CouponController', 'edit');
$router->post('/coupons/update/{id}', 'CouponController', 'update');
$router->get('/coupons/delete/{id}', 'CouponController', 'delete');

// Staff
$router->get('/staff', 'StaffController', 'index');
$router->get('/staff/create', 'StaffController', 'create');
$router->post('/staff/store', 'StaffController', 'store');
$router->get('/staff/view/{id}', 'StaffController', 'view');
$router->get('/staff/edit/{id}', 'StaffController', 'edit');
$router->post('/staff/update/{id}', 'StaffController', 'update');
$router->get('/staff/delete/{id}', 'StaffController', 'delete');

// Payroll
$router->get('/payroll', 'PayrollController', 'index');
$router->get('/payroll/create', 'PayrollController', 'create');
$router->post('/payroll/store', 'PayrollController', 'store');
$router->get('/payroll/view/{id}', 'PayrollController', 'view');
$router->get('/payroll/edit/{id}', 'PayrollController', 'edit');
$router->post('/payroll/update/{id}', 'PayrollController', 'update');
$router->get('/payroll/delete/{id}', 'PayrollController', 'delete');
$router->get('/payroll/print/{id}', 'PayrollController', 'print');
$router->post('/payroll/calculateCommissions', 'PayrollController', 'calculateCommissions');

// Reports
$router->get('/reports', 'ReportsController', 'index');
$router->get('/reports/sales', 'ReportsController', 'sales');
$router->get('/reports/services', 'ReportsController', 'services');
$router->get('/reports/products', 'ReportsController', 'products');
$router->get('/reports/staff', 'ReportsController', 'staff');
$router->get('/reports/appointments', 'ReportsController', 'appointments');
$router->get('/reports/inventory', 'ReportsController', 'inventory');
$router->get('/reports/customers', 'ReportsController', 'customers');
$router->get('/reports/export', 'ReportsController', 'export');

// Settings
$router->get('/settings', 'SettingsController', 'index');
$router->post('/settings/update-general', 'SettingsController', 'save_general');
$router->get('/settings/business', 'SettingsController', 'business');
$router->post('/settings/update-business', 'SettingsController', 'updateBusiness');
$router->get('/settings/appearance', 'SettingsController', 'appearance');
$router->post('/settings/update-appearance', 'SettingsController', 'updateAppearance');
$router->get('/settings/email', 'SettingsController', 'email');
$router->post('/settings/update-email', 'SettingsController', 'updateEmail');

$router->post('/settings/update', 'SettingsController', 'save_setting');

// Frontend booking
$router->get('/', 'BookingController', 'index');
$router->get('/home', 'BookingController', 'index');
$router->get('/booking', 'BookingController', 'index');
$router->get('/booking/services', 'BookingController', 'services');
$router->get('/booking/staff', 'BookingController', 'staff');
$router->get('/booking/datetime', 'BookingController', 'datetime');
$router->post('/booking/confirm', 'BookingController', 'confirm');
$router->get('/booking/success', 'BookingController', 'success');

// Contact
$router->get('/contact', 'ContactController', 'index');
$router->post('/contact/send', 'ContactController', 'send');

// Profile
$router->get('/profile', 'ProfileController', 'index');
$router->post('/profile/update', 'ProfileController', 'update');
$router->post('/profile/upload-picture', 'ProfileController', 'uploadPicture');

// Customer Portal
$router->get('/customer', 'CustomerPortalController', 'index');
$router->get('/customer/appointments', 'CustomerPortalController', 'appointments');
$router->get('/customer/invoices', 'CustomerPortalController', 'invoices');
$router->get('/customer/memberships', 'CustomerPortalController', 'memberships');
$router->get('/customer/view-invoice/{id}', 'CustomerPortalController', 'viewInvoice');
$router->get('/customer/view-appointment/{id}', 'CustomerPortalController', 'viewAppointment');

// Notifications
$router->get('/notifications', 'NotificationsController', 'index');
$router->post('/notifications/mark-as-read', 'NotificationsController', 'markAsRead');
$router->post('/notifications/mark-all-as-read', 'NotificationsController', 'markAllAsRead');
$router->get('/notifications/get', 'NotificationsController', 'getNotifications');



// Hero Slider
$router->post('/hero-slider/add', 'HeroSliderController', 'add');
$router->get('/hero-slider/edit/{id}', 'HeroSliderController', 'edit');
$router->post('/hero-slider/edit/{id}', 'HeroSliderController', 'edit');
$router->get('/hero-slider/delete/{id}', 'HeroSliderController', 'delete');
$router->post('/hero-slider/update-order', 'HeroSliderController', 'updateOrder');

// Gallery
$router->get('/gallery', 'GalleryController', 'index');
$router->get('/gallery/admin', 'GalleryController', 'admin');
$router->get('/gallery/add', 'GalleryController', 'add');
$router->post('/gallery/create', 'GalleryController', 'create');
$router->get('/gallery/edit/{id}', 'GalleryController', 'edit');
$router->post('/gallery/update/{id}', 'GalleryController', 'update');
$router->get('/gallery/delete/{id}', 'GalleryController', 'delete');
$router->get('/gallery/categories', 'GalleryController', 'categories');
$router->post('/gallery/categories/store', 'GalleryController', 'storeCategory');
$router->post('/gallery/categories/update', 'GalleryController', 'updateCategory');
$router->get('/gallery/categories/delete/{id}', 'GalleryController', 'deleteCategory');

