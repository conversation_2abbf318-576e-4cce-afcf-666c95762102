<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-calendar-check"></i> Appointment Report</h1>
    </div>

    <!-- Date Range Filter -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('reports/appointments') ?>" method="get" class="row align-items-end">
                <div class="col-md-4 mb-2">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                </div>
                <div class="col-md-4 mb-2">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                </div>
                <div class="col-md-4 mb-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i> Apply Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <!-- Reports Navigation -->
        <div class="col-md-3 mb-4">
            <?php include 'partials/reports_nav.php'; ?>
        </div>
        
        <!-- Reports Content -->
        <div class="col-md-9">
            <!-- Summary Card -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Appointments</h6>
                                    <h4 class="mb-0"><?= $appointment_report['total'] ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-calendar-check text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Appointment Status Chart -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Appointment Status Distribution</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($appointment_report['by_status'])): ?>
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No appointment data available for the selected period.</p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <div class="col-md-6">
                                <canvas id="statusChart" height="250"></canvas>
                            </div>
                            <div class="col-md-6">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Status</th>
                                                <th class="text-end">Count</th>
                                                <th class="text-end">Percentage</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            foreach ($appointment_report['by_status'] as $status): 
                                                $percentage = ($status['appointment_count'] / $appointment_report['total']) * 100;
                                            ?>
                                                <tr>
                                                    <td>
                                                        <?php if ($status['status'] == 'completed'): ?>
                                                            <span class="badge bg-success">Completed</span>
                                                        <?php elseif ($status['status'] == 'confirmed'): ?>
                                                            <span class="badge bg-primary">Confirmed</span>
                                                        <?php elseif ($status['status'] == 'pending'): ?>
                                                            <span class="badge bg-warning">Pending</span>
                                                        <?php elseif ($status['status'] == 'cancelled'): ?>
                                                            <span class="badge bg-danger">Cancelled</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary"><?= ucfirst($status['status']) ?></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="text-end"><?= $status['appointment_count'] ?></td>
                                                    <td class="text-end"><?= number_format($percentage, 1) ?>%</td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Appointment Trend Chart -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Appointment Trend</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($appointment_report['by_date'])): ?>
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No appointment data available for the selected period.</p>
                        </div>
                    <?php else: ?>
                        <canvas id="trendChart" height="250"></canvas>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Appointment Data Table -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Appointment Data by Date</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($appointment_report['by_date'])): ?>
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No appointment data available for the selected period.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th class="text-end">Appointments</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($appointment_report['by_date'] as $row): ?>
                                        <tr>
                                            <td><?= format_date($row['date']) ?></td>
                                            <td class="text-end"><?= $row['appointment_count'] ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th>Total</th>
                                        <th class="text-end"><?= $appointment_report['total'] ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Status Chart
    const statusData = <?= json_encode($appointment_report['by_status']) ?>;
    
    if (statusData.length > 0) {
        const statusLabels = statusData.map(item => item.status.charAt(0).toUpperCase() + item.status.slice(1));
        const statusValues = statusData.map(item => item.appointment_count);
        const statusColors = statusData.map(item => {
            switch(item.status) {
                case 'completed': return 'rgba(46, 204, 113, 0.8)';
                case 'confirmed': return 'rgba(52, 152, 219, 0.8)';
                case 'pending': return 'rgba(241, 196, 15, 0.8)';
                case 'cancelled': return 'rgba(231, 76, 60, 0.8)';
                default: return 'rgba(149, 165, 166, 0.8)';
            }
        });
        
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: statusLabels,
                datasets: [{
                    data: statusValues,
                    backgroundColor: statusColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    }
    
    // Trend Chart
    const trendData = <?= json_encode($appointment_report['by_date']) ?>;
    
    if (trendData.length > 0) {
        const trendLabels = trendData.map(item => item.date);
        const trendValues = trendData.map(item => item.appointment_count);
        
        const trendCtx = document.getElementById('trendChart').getContext('2d');
        new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: trendLabels,
                datasets: [{
                    label: 'Appointments',
                    data: trendValues,
                    backgroundColor: 'rgba(52, 152, 219, 0.2)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }
});
</script>
