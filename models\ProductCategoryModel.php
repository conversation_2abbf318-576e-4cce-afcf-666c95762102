<?php
/**
 * Product Category Model
 */
class ProductCategoryModel extends Model {
    protected $table = 'product_categories';

    /**
     * Get all categories
     *
     * @return array Categories
     */
    public function all() {
        $query = "SELECT * FROM " . $this->table . " ORDER BY name";
        $stmt = $this->db->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Find category by ID
     *
     * @param int $id Category ID
     * @return array|false Category data or false if not found
     */
    public function find($id) {
        $query = "SELECT * FROM " . $this->table . " WHERE id = :id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Find category by name
     *
     * @param string $name Category name
     * @return array|false Category data or false if not found
     */
    public function findByName($name) {
        $query = "SELECT * FROM " . $this->table . " WHERE name = :name LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':name', $name);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
