<?php
/**
 * Invoice Model
 */
class InvoiceModel extends Model {
    protected $table = 'invoices';

    /**
     * Get invoice with details
     *
     * @param int $id Invoice ID
     * @return array|false Invoice data or false if not found
     */
    public function getWithDetails($id) {
        // Get invoice
        $query = "SELECT i.*, u.name as customer_name, u.email as customer_email, c.phone as customer_phone
                 FROM " . $this->table . " i
                 JOIN customers c ON i.customer_id = c.id
                 JOIN users u ON c.user_id = u.id
                 WHERE i.id = :id
                 LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);

        // Get coupon information if available
        $query = "SELECT cu.*, c.code as coupon_code, c.discount_type, c.discount_value
                 FROM coupon_usage cu
                 JOIN coupons c ON cu.coupon_id = c.id
                 WHERE cu.invoice_id = :invoice_id
                 LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':invoice_id', $id);
        $stmt->execute();

        $coupon_usage = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($coupon_usage) {
            $invoice['coupon'] = $coupon_usage;
        }

        if (!$invoice) {
            return false;
        }

        // Get invoice services with more details
        $query = "SELECT `is`.*, s.name as service_name, s.description as service_description,
                 s.duration as service_duration, sc.name as service_category_name,
                 u.name as staff_name
                 FROM invoice_services `is`
                 JOIN services s ON `is`.service_id = s.id
                 JOIN service_categories sc ON s.category_id = sc.id
                 LEFT JOIN staff st ON `is`.staff_id = st.id
                 LEFT JOIN users u ON st.user_id = u.id
                 WHERE `is`.invoice_id = :invoice_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':invoice_id', $id);
        $stmt->execute();

        $invoice['services'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // If no services found, try a simpler query to check if services exist
        if (empty($invoice['services'])) {
            $query = "SELECT `is`.*, s.name as service_name
                     FROM invoice_services `is`
                     JOIN services s ON `is`.service_id = s.id
                     WHERE `is`.invoice_id = :invoice_id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':invoice_id', $id);
            $stmt->execute();

            $invoice['services'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }

        // Get invoice products with more details
        $query = "SELECT ip.*, p.name as product_name, p.description as product_description,
                 pc.name as product_category_name
                 FROM invoice_products ip
                 JOIN products p ON ip.product_id = p.id
                 LEFT JOIN product_categories pc ON p.category_id = pc.id
                 WHERE ip.invoice_id = :invoice_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':invoice_id', $id);
        $stmt->execute();

        $invoice['products'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // If no products found, try a simpler query to check if products exist
        if (empty($invoice['products'])) {
            $query = "SELECT ip.*, p.name as product_name
                     FROM invoice_products ip
                     JOIN products p ON ip.product_id = p.id
                     WHERE ip.invoice_id = :invoice_id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':invoice_id', $id);
            $stmt->execute();

            $invoice['products'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }

        return $invoice;
    }

    /**
     * Get today's sales
     *
     * @return array Sales data
     */
    public function getTodaySales() {
        $query = "SELECT COUNT(*) as count, SUM(total_amount) as total_amount
                 FROM " . $this->table . "
                 WHERE DATE(created_at) = CURDATE()";
        $stmt = $this->db->prepare($query);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get all invoices with details
     *
     * @param string $status Filter by status (optional)
     * @param string $date_from Filter by date from (optional)
     * @param string $date_to Filter by date to (optional)
     * @param int $customer_id Filter by customer ID (optional)
     * @return array Invoices
     */
    public function getAllWithDetails($status = null, $date_from = null, $date_to = null, $customer_id = null) {
        $query = "SELECT i.*, u.name as customer_name, u.email as customer_email, c.phone as customer_phone
                 FROM " . $this->table . " i
                 JOIN customers c ON i.customer_id = c.id
                 JOIN users u ON c.user_id = u.id
                 WHERE 1=1";

        $params = [];

        if ($status) {
            $query .= " AND i.status = :status";
            $params[':status'] = $status;
        }

        if ($date_from) {
            $query .= " AND i.invoice_date >= :date_from";
            $params[':date_from'] = $date_from;
        }

        if ($date_to) {
            $query .= " AND i.invoice_date <= :date_to";
            $params[':date_to'] = $date_to;
        }

        if ($customer_id) {
            $query .= " AND i.customer_id = :customer_id";
            $params[':customer_id'] = $customer_id;
        }

        $query .= " ORDER BY i.invoice_date DESC, i.id DESC";

        $stmt = $this->db->prepare($query);

        foreach ($params as $param => $value) {
            $stmt->bindValue($param, $value);
        }

        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get invoices by customer ID
     *
     * @param int $customer_id Customer ID
     * @return array Invoices
     */
    public function getByCustomerId($customer_id) {
        $query = "SELECT i.*
                 FROM " . $this->table . " i
                 WHERE i.customer_id = :customer_id
                 ORDER BY i.invoice_date DESC, i.id DESC";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':customer_id', $customer_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }



    /**
     * Get sales statistics
     *
     * @return array Statistics
     */
    public function getSalesStatistics() {
        $stats = [
            'today' => 0,
            'this_week' => 0,
            'this_month' => 0,
            'this_year' => 0,
            'total_customers' => 0
        ];

        // Today's sales
        $query = "SELECT SUM(total_amount) FROM " . $this->table . " WHERE DATE(created_at) = CURDATE()";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        $stats['today'] = $stmt->fetchColumn() ?: 0;

        // This week's sales
        $query = "SELECT SUM(total_amount) FROM " . $this->table . " WHERE YEARWEEK(created_at) = YEARWEEK(CURDATE())";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        $stats['this_week'] = $stmt->fetchColumn() ?: 0;

        // This month's sales
        $query = "SELECT SUM(total_amount) FROM " . $this->table . " WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        $stats['this_month'] = $stmt->fetchColumn() ?: 0;

        // This year's sales
        $query = "SELECT SUM(total_amount) FROM " . $this->table . " WHERE YEAR(created_at) = YEAR(CURDATE())";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        $stats['this_year'] = $stmt->fetchColumn() ?: 0;

        // Total customers
        $query = "SELECT COUNT(*) FROM customers";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        $stats['total_customers'] = $stmt->fetchColumn();

        return $stats;
    }
}
