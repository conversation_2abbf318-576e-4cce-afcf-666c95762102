<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-user-circle"></i> My Profile</h1>
    </div>

    <div class="row">
        <!-- Profile Picture -->
        <div class="col-md-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <?php if (!empty($user['profile_picture'])): ?>
                            <img src="<?= base_url('uploads/profile/' . $user['profile_picture']) ?>" alt="Profile Picture" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px; object-fit: cover;">
                        <?php else: ?>
                            <div class="avatar-circle mx-auto">
                                <span class="avatar-initials"><?= substr($user['name'], 0, 1) ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <h5 class="mb-1"><?= $user['name'] ?></h5>
                    <p class="text-muted mb-3"><?= ucfirst($user['role']) ?></p>

                    <form action="<?= base_url('profile/upload-picture') ?>" method="post" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="profile_picture" class="form-label">Update Profile Picture</label>
                            <input type="file" class="form-control" id="profile_picture" name="profile_picture" accept="image/*" required>
                            <div class="form-text">Max file size: 2MB. Supported formats: JPEG, PNG, GIF</div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-1"></i> Upload Picture
                        </button>
                    </form>
                </div>
            </div>

            <!-- Account Information -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Account Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <div class="text-muted small">Email</div>
                        <div><?= $user['email'] ?></div>
                    </div>
                    <div class="mb-2">
                        <div class="text-muted small">Role</div>
                        <div><?= ucfirst($user['role']) ?></div>
                    </div>
                    <div class="mb-2">
                        <div class="text-muted small">Status</div>
                        <div>
                            <?php if ($user['status'] == 'active'): ?>
                                <span class="badge bg-success">Active</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Inactive</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="text-muted small">Member Since</div>
                        <div><?= format_date($user['created_at']) ?></div>
                    </div>

                    <?php if ($user['role'] == 'customer'): ?>
                    <hr>
                    <div class="d-grid gap-2 mt-3">
                        <a href="<?= base_url('customer') ?>" class="btn btn-primary">
                            <i class="fas fa-tachometer-alt me-1"></i> Customer Dashboard
                        </a>
                        <a href="<?= base_url('customer/appointments') ?>" class="btn btn-outline-primary">
                            <i class="fas fa-calendar-alt me-1"></i> My Appointments
                        </a>
                        <a href="<?= base_url('customer/invoices') ?>" class="btn btn-outline-primary">
                            <i class="fas fa-file-invoice-dollar me-1"></i> My Invoices
                        </a>
                        <a href="<?= base_url('customer/memberships') ?>" class="btn btn-outline-primary">
                            <i class="fas fa-id-card me-1"></i> My Membership
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Profile Details -->
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Edit Profile</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('profile/update') ?>" method="post">
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-12 mb-3">
                                <h6 class="border-bottom pb-2">Basic Information</h6>
                            </div>

                            <!-- Name -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="<?= $user['name'] ?>" required>
                            </div>

                            <!-- Email -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" value="<?= $user['email'] ?>" required>
                            </div>

                            <?php if ($user['role'] == 'staff' || $user['role'] == 'customer'): ?>
                                <!-- Phone -->
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="text" class="form-control" id="phone" name="phone" value="<?= $profile_data['phone'] ?? '' ?>">
                                </div>

                                <!-- Address -->
                                <div class="col-md-6 mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control" id="address" name="address" rows="3"><?= $profile_data['address'] ?? '' ?></textarea>
                                </div>
                            <?php endif; ?>

                            <?php if ($user['role'] == 'customer'): ?>
                                <!-- Date of Birth -->
                                <div class="col-md-6 mb-3">
                                    <label for="date_of_birth" class="form-label">Date of Birth</label>
                                    <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" value="<?= $profile_data['date_of_birth'] ?? '' ?>">
                                </div>

                                <!-- Gender -->
                                <div class="col-md-6 mb-3">
                                    <label for="gender" class="form-label">Gender</label>
                                    <select class="form-select" id="gender" name="gender">
                                        <option value="">Select Gender</option>
                                        <option value="male" <?= isset($profile_data['gender']) && $profile_data['gender'] == 'male' ? 'selected' : '' ?>>Male</option>
                                        <option value="female" <?= isset($profile_data['gender']) && $profile_data['gender'] == 'female' ? 'selected' : '' ?>>Female</option>
                                        <option value="other" <?= isset($profile_data['gender']) && $profile_data['gender'] == 'other' ? 'selected' : '' ?>>Other</option>
                                    </select>
                                </div>
                            <?php endif; ?>

                            <!-- Change Password -->
                            <div class="col-md-12 mt-4 mb-3">
                                <h6 class="border-bottom pb-2">Change Password</h6>
                                <div class="form-text mb-3">Leave blank if you don't want to change your password</div>
                            </div>

                            <!-- Current Password -->
                            <div class="col-md-12 mb-3">
                                <label for="current_password" class="form-label">Current Password</label>
                                <input type="password" class="form-control" id="current_password" name="current_password">
                            </div>

                            <!-- New Password -->
                            <div class="col-md-6 mb-3">
                                <label for="new_password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="new_password" name="new_password">
                            </div>

                            <!-- Confirm New Password -->
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                            </div>

                            <!-- Submit Button -->
                            <div class="col-md-12 mt-4 text-center">
                                <button type="submit" class="btn btn-primary btn-lg px-5">
                                    <i class="fas fa-save me-2"></i> Save Changes
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 150px;
    height: 150px;
    background-color: #3498db;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.avatar-initials {
    color: white;
    font-size: 64px;
    font-weight: bold;
    text-transform: uppercase;
}
</style>
