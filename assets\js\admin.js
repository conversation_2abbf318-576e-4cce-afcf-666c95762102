/**
 * Admin Dashboard JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Sidebar toggle functionality
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const mobileSidebarToggle = document.getElementById('mobile-sidebar-toggle');
    const adminLayout = document.querySelector('.admin-layout');
    const sidebar = document.querySelector('.sidebar');

    // Create sidebar overlay for mobile
    const sidebarOverlay = document.createElement('div');
    sidebarOverlay.className = 'sidebar-overlay';
    document.body.appendChild(sidebarOverlay);

    // Function to toggle sidebar
    function toggleSidebar() {
        if (window.innerWidth < 992) {
            // On mobile, show/hide the sidebar
            sidebar.classList.toggle('show');
            sidebarOverlay.classList.toggle('show');

            // Prevent body scrolling when sidebar is open
            if (sidebar.classList.contains('show')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        } else {
            // On desktop, collapse/expand the sidebar
            adminLayout.classList.toggle('sidebar-collapsed');
            sidebar.classList.toggle('collapsed');

            // Hide/show text in sidebar
            const sidebarTexts = document.querySelectorAll('.sidebar-link span, .sidebar-user-info, .sidebar-footer-link span, .sidebar-brand span');
            sidebarTexts.forEach(text => {
                text.style.opacity = adminLayout.classList.contains('sidebar-collapsed') ? '0' : '1';
                text.style.display = adminLayout.classList.contains('sidebar-collapsed') ? 'none' : 'inline';
            });
        }
    }

    // Add click event listeners
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }

    if (mobileSidebarToggle) {
        mobileSidebarToggle.addEventListener('click', toggleSidebar);
    }

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
        if (window.innerWidth < 992 &&
            sidebar.classList.contains('show') &&
            !sidebar.contains(event.target) &&
            event.target !== mobileSidebarToggle) {
            sidebar.classList.remove('show');
            sidebarOverlay.classList.remove('show');
            document.body.style.overflow = '';
        }
    });

    // Close sidebar when clicking on overlay
    sidebarOverlay.addEventListener('click', function() {
        sidebar.classList.remove('show');
        sidebarOverlay.classList.remove('show');
        document.body.style.overflow = '';
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 992) {
            // On desktop
            sidebar.classList.remove('show');
            sidebarOverlay.classList.remove('show');
            document.body.style.overflow = '';
        } else {
            // On mobile
            adminLayout.classList.remove('sidebar-collapsed');
            sidebar.classList.remove('collapsed');

            // Reset text visibility
            const sidebarTexts = document.querySelectorAll('.sidebar-link span, .sidebar-user-info, .sidebar-footer-link span, .sidebar-brand span');
            sidebarTexts.forEach(text => {
                text.style.opacity = '1';
                text.style.display = 'inline';
            });
        }
    });

    // Set active menu item based on current URL
    const currentPath = window.location.pathname;
    const sidebarLinks = document.querySelectorAll('.sidebar-link');

    sidebarLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.includes(href.split('/').pop())) {
            link.classList.add('active');

            // Scroll active menu item into view
            setTimeout(() => {
                if (link.classList.contains('active')) {
                    link.scrollIntoView({ block: 'center' });
                }
            }, 100);
        }
    });

    // Update page title in top navbar
    const pageTitle = document.querySelector('.page-title');
    if (pageTitle) {
        // Get the active menu item text
        const activeLink = document.querySelector('.sidebar-link.active');
        if (activeLink) {
            const activeLinkText = activeLink.querySelector('span').textContent;
            pageTitle.textContent = activeLinkText;
        }
    }
});
