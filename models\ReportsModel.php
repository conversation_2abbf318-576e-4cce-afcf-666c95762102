<?php
/**
 * Reports Model
 */
class ReportsModel extends Model {
    /**
     * Get sales report data
     *
     * @param string $start_date Start date
     * @param string $end_date End date
     * @param string $group_by Group by (day, month, year)
     * @return array Report data
     */
    public function getSalesReport($start_date, $end_date, $group_by = 'day') {
        $format = '%Y-%m-%d';
        $group_label = 'Day';

        if ($group_by == 'month') {
            $format = '%Y-%m';
            $group_label = 'Month';
        } else if ($group_by == 'year') {
            $format = '%Y';
            $group_label = 'Year';
        }

        $query = "SELECT
                    DATE_FORMAT(invoice_date, '$format') as period,
                    COUNT(*) as invoice_count,
                    SUM(subtotal) as subtotal,
                    SUM(tax_amount) as tax_amount,
                    SUM(discount_amount) as discount_amount,
                    SUM(total_amount) as total_amount,
                    SUM(payment_amount) as payment_amount
                 FROM invoices
                 WHERE invoice_date BETWEEN :start_date AND :end_date
                 GROUP BY period
                 ORDER BY period";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculate totals
        $totals = [
            'invoice_count' => 0,
            'subtotal' => 0,
            'tax_amount' => 0,
            'discount_amount' => 0,
            'total_amount' => 0,
            'payment_amount' => 0
        ];

        foreach ($results as $row) {
            $totals['invoice_count'] += $row['invoice_count'];
            $totals['subtotal'] += $row['subtotal'];
            $totals['tax_amount'] += $row['tax_amount'];
            $totals['discount_amount'] += $row['discount_amount'];
            $totals['total_amount'] += $row['total_amount'];
            $totals['payment_amount'] += $row['payment_amount'];
        }

        return [
            'data' => $results,
            'totals' => $totals,
            'group_label' => $group_label
        ];
    }

    /**
     * Get service sales report data
     *
     * @param string $start_date Start date
     * @param string $end_date End date
     * @return array Report data
     */
    public function getServiceSalesReport($start_date, $end_date) {
        $query = "SELECT
                    s.id as service_id,
                    s.name as service_name,
                    s.price as service_price,
                    COUNT(`is`.id) as service_count,
                    SUM(`is`.total_price) as total_amount
                 FROM invoice_services `is`
                 JOIN services s ON `is`.service_id = s.id
                 JOIN invoices i ON `is`.invoice_id = i.id
                 WHERE i.invoice_date BETWEEN :start_date AND :end_date
                 GROUP BY s.id
                 ORDER BY total_amount DESC";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculate totals
        $totals = [
            'service_count' => 0,
            'total_amount' => 0
        ];

        foreach ($results as $row) {
            $totals['service_count'] += $row['service_count'];
            $totals['total_amount'] += $row['total_amount'];
        }

        return [
            'data' => $results,
            'totals' => $totals
        ];
    }

    /**
     * Get product sales report data
     *
     * @param string $start_date Start date
     * @param string $end_date End date
     * @return array Report data
     */
    public function getProductSalesReport($start_date, $end_date) {
        $query = "SELECT
                    p.id as product_id,
                    p.name as product_name,
                    p.selling_price as product_price,
                    SUM(ip.quantity) as quantity_sold,
                    SUM(ip.total_price) as total_amount,
                    SUM((ip.unit_price - p.cost_price) * ip.quantity) as profit
                 FROM invoice_products ip
                 JOIN products p ON ip.product_id = p.id
                 JOIN invoices i ON ip.invoice_id = i.id
                 WHERE i.invoice_date BETWEEN :start_date AND :end_date
                 GROUP BY p.id
                 ORDER BY total_amount DESC";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculate totals
        $totals = [
            'quantity_sold' => 0,
            'total_amount' => 0,
            'profit' => 0
        ];

        foreach ($results as $row) {
            $totals['quantity_sold'] += $row['quantity_sold'];
            $totals['total_amount'] += $row['total_amount'];
            $totals['profit'] += $row['profit'];
        }

        return [
            'data' => $results,
            'totals' => $totals
        ];
    }

    /**
     * Get staff performance report data
     *
     * @param string $start_date Start date
     * @param string $end_date End date
     * @return array Report data
     */
    public function getStaffPerformanceReport($start_date, $end_date) {
        $query = "SELECT
                    s.id as staff_id,
                    u.name as staff_name,
                    s.position,
                    COUNT(DISTINCT `is`.invoice_id) as invoice_count,
                    COUNT(`is`.id) as service_count,
                    SUM(`is`.total_price) as service_amount,
                    SUM(`is`.total_price * s.commission_rate / 100) as commission_amount
                 FROM invoice_services `is`
                 JOIN staff s ON `is`.staff_id = s.id
                 JOIN users u ON s.user_id = u.id
                 JOIN invoices i ON `is`.invoice_id = i.id
                 WHERE i.invoice_date BETWEEN :start_date AND :end_date
                 GROUP BY s.id
                 ORDER BY service_amount DESC";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculate totals
        $totals = [
            'invoice_count' => 0,
            'service_count' => 0,
            'service_amount' => 0,
            'commission_amount' => 0
        ];

        foreach ($results as $row) {
            $totals['invoice_count'] += $row['invoice_count'];
            $totals['service_count'] += $row['service_count'];
            $totals['service_amount'] += $row['service_amount'];
            $totals['commission_amount'] += $row['commission_amount'];
        }

        return [
            'data' => $results,
            'totals' => $totals
        ];
    }

    /**
     * Get appointment report data
     *
     * @param string $start_date Start date
     * @param string $end_date End date
     * @return array Report data
     */
    public function getAppointmentReport($start_date, $end_date) {
        $query = "SELECT
                    a.status,
                    COUNT(*) as appointment_count
                 FROM appointments a
                 WHERE a.appointment_date BETWEEN :start_date AND :end_date
                 GROUP BY a.status";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculate total
        $total = 0;
        foreach ($results as $row) {
            $total += $row['appointment_count'];
        }

        // Get appointments by day
        $query = "SELECT
                    DATE_FORMAT(a.appointment_date, '%Y-%m-%d') as date,
                    COUNT(*) as appointment_count
                 FROM appointments a
                 WHERE a.appointment_date BETWEEN :start_date AND :end_date
                 GROUP BY date
                 ORDER BY date";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        $by_date = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return [
            'by_status' => $results,
            'by_date' => $by_date,
            'total' => $total
        ];
    }

    /**
     * Get inventory report data
     *
     * @return array Report data
     */
    public function getInventoryReport() {
        $query = "SELECT
                    p.id as product_id,
                    p.name as product_name,
                    pc.name as category_name,
                    p.quantity as current_stock,
                    p.min_quantity as min_stock,
                    p.cost_price,
                    p.selling_price,
                    (p.selling_price - p.cost_price) as profit_margin,
                    (p.selling_price - p.cost_price) / p.cost_price * 100 as profit_percentage,
                    p.cost_price * p.quantity as inventory_value
                 FROM products p
                 JOIN product_categories pc ON p.category_id = pc.id
                 ORDER BY p.quantity ASC";

        $stmt = $this->db->prepare($query);
        $stmt->execute();

        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculate totals
        $totals = [
            'product_count' => count($results),
            'total_stock' => 0,
            'inventory_value' => 0,
            'low_stock_count' => 0
        ];

        foreach ($results as $row) {
            $totals['total_stock'] += $row['current_stock'];
            $totals['inventory_value'] += $row['inventory_value'];

            if ($row['current_stock'] <= $row['min_stock']) {
                $totals['low_stock_count']++;
            }
        }

        return [
            'data' => $results,
            'totals' => $totals
        ];
    }

    /**
     * Get customer report data
     *
     * @param string $start_date Start date
     * @param string $end_date End date
     * @return array Report data
     */
    public function getCustomerReport($start_date, $end_date) {
        $query = "SELECT
                    c.id as customer_id,
                    u.name as customer_name,
                    u.email as customer_email,
                    c.phone,
                    COUNT(DISTINCT i.id) as invoice_count,
                    SUM(i.total_amount) as total_spent,
                    MAX(i.invoice_date) as last_visit
                 FROM customers c
                 JOIN users u ON c.user_id = u.id
                 LEFT JOIN invoices i ON c.id = i.customer_id AND i.invoice_date BETWEEN :start_date AND :end_date
                 GROUP BY c.id
                 ORDER BY total_spent DESC";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->execute();

        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculate totals
        $totals = [
            'customer_count' => count($results),
            'invoice_count' => 0,
            'total_spent' => 0
        ];

        foreach ($results as $row) {
            $totals['invoice_count'] += $row['invoice_count'];
            $totals['total_spent'] += $row['total_spent'];
        }

        return [
            'data' => $results,
            'totals' => $totals
        ];
    }
}
