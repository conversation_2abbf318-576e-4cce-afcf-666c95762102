<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-calendar-alt"></i> Appointments</h1>
        <div>
            <a href="<?= base_url('appointments/calendar') ?>" class="btn btn-outline-primary me-2">
                <i class="fas fa-calendar"></i> Calendar View
            </a>
            <a href="<?= base_url('appointments/create') ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> New Appointment
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('appointments') ?>" method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="date" class="form-label">Date</label>
                    <input type="date" class="form-control" id="date" name="date" value="<?= $selected_date ?>">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="pending" <?= $selected_status == 'pending' ? 'selected' : '' ?>>Pending</option>
                        <option value="confirmed" <?= $selected_status == 'confirmed' ? 'selected' : '' ?>>Confirmed</option>
                        <option value="completed" <?= $selected_status == 'completed' ? 'selected' : '' ?>>Completed</option>
                        <option value="cancelled" <?= $selected_status == 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="staff_id" class="form-label">Staff</label>
                    <select class="form-select" id="staff_id" name="staff_id">
                        <option value="">All Staff</option>
                        <?php foreach ($staff as $staff_member): ?>
                            <option value="<?= $staff_member['id'] ?>" <?= $selected_staff == $staff_member['id'] ? 'selected' : '' ?>>
                                <?= $staff_member['name'] ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter"></i> Filter
                    </button>
                    <a href="<?= base_url('appointments') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-redo"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Appointments List -->
    <div class="card shadow-sm">
        <div class="card-body p-0">
            <?php if (empty($appointments)): ?>
                <div class="p-4 text-center">
                    <img src="<?= base_url('assets/images/empty-calendar.svg') ?>" alt="No Appointments" style="width: 120px; opacity: 0.5;" class="mb-3">
                    <p class="text-muted mb-0">No appointments found matching your criteria.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date & Time</th>
                                <th>Customer</th>
                                <th>Service</th>
                                <th>Staff</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($appointments as $appointment): ?>
                                <tr>
                                    <td>#<?= $appointment['id'] ?></td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-bold"><?= format_date($appointment['appointment_date']) ?></span>
                                            <span class="small text-muted">
                                                <?= date('h:i A', strtotime($appointment['start_time'])) ?> - 
                                                <?= date('h:i A', strtotime($appointment['end_time'])) ?>
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-primary text-white me-2">
                                                <?= strtoupper(substr($appointment['customer_name'], 0, 1)) ?>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?= $appointment['customer_name'] ?></div>
                                                <div class="small text-muted"><?= $appointment['customer_phone'] ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="fw-bold"><?= $appointment['service_name'] ?></div>
                                        <div class="small text-muted"><?= format_currency($appointment['service_price']) ?> • <?= $appointment['service_duration'] ?> mins</div>
                                    </td>
                                    <td><?= $appointment['staff_name'] ?></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm dropdown-toggle status-badge status-<?= $appointment['status'] ?>" type="button" data-bs-toggle="dropdown">
                                                <?= ucfirst($appointment['status']) ?>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <form action="<?= base_url('appointments/update-status/' . $appointment['id']) ?>" method="post" class="status-form">
                                                        <input type="hidden" name="status" value="pending">
                                                        <button type="submit" class="dropdown-item status-item status-pending">Pending</button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form action="<?= base_url('appointments/update-status/' . $appointment['id']) ?>" method="post" class="status-form">
                                                        <input type="hidden" name="status" value="confirmed">
                                                        <button type="submit" class="dropdown-item status-item status-confirmed">Confirmed</button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form action="<?= base_url('appointments/update-status/' . $appointment['id']) ?>" method="post" class="status-form">
                                                        <input type="hidden" name="status" value="completed">
                                                        <button type="submit" class="dropdown-item status-item status-completed">Completed</button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form action="<?= base_url('appointments/update-status/' . $appointment['id']) ?>" method="post" class="status-form">
                                                        <input type="hidden" name="status" value="cancelled">
                                                        <button type="submit" class="dropdown-item status-item status-cancelled">Cancelled</button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            <a href="<?= base_url('appointments/view/' . $appointment['id']) ?>" class="btn btn-sm btn-outline-primary me-1" data-bs-toggle="tooltip" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('appointments/edit/' . $appointment['id']) ?>" class="btn btn-sm btn-outline-secondary me-1" data-bs-toggle="tooltip" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?= base_url('appointments/delete/' . $appointment['id']) ?>" class="btn btn-sm btn-outline-danger" data-bs-toggle="tooltip" title="Delete" onclick="return confirm('Are you sure you want to delete this appointment?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1rem;
}

.status-badge {
    border-radius: 50px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background-color: var(--warning-light);
    color: var(--warning-dark);
}

.status-confirmed {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

.status-completed {
    background-color: var(--success-light);
    color: var(--success-dark);
}

.status-cancelled {
    background-color: var(--danger-light);
    color: var(--danger-dark);
}

.status-item {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.status-form {
    margin: 0;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Auto-submit status forms
    document.querySelectorAll('.status-form').forEach(function(form) {
        form.querySelector('button').addEventListener('click', function() {
            form.submit();
        });
    });
});
</script>
