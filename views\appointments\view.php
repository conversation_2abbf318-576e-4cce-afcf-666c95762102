<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-calendar-check"></i> Appointment Details</h1>
        <div>
            <a href="<?= base_url('appointments') ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Appointments
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Appointment Details -->
        <div class="col-md-8 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Appointment #<?= $appointment['id'] ?></h5>
                    <div>
                        <?php if ($appointment['status'] == 'pending'): ?>
                            <span class="badge bg-warning">Pending</span>
                        <?php elseif ($appointment['status'] == 'confirmed'): ?>
                            <span class="badge bg-primary">Confirmed</span>
                        <?php elseif ($appointment['status'] == 'completed'): ?>
                            <span class="badge bg-success">Completed</span>
                        <?php elseif ($appointment['status'] == 'cancelled'): ?>
                            <span class="badge bg-danger">Cancelled</span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Date & Time</h6>
                            <div class="d-flex align-items-center mb-3">
                                <div class="icon-circle bg-primary-light text-primary me-3">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div>
                                    <div class="fw-bold"><?= format_date($appointment['appointment_date']) ?></div>
                                    <div class="text-muted">
                                        <?= date('h:i A', strtotime($appointment['start_time'])) ?> - 
                                        <?= date('h:i A', strtotime($appointment['end_time'])) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Service</h6>
                            <div class="d-flex align-items-center mb-3">
                                <div class="icon-circle bg-info-light text-info me-3">
                                    <i class="fas fa-cut"></i>
                                </div>
                                <div>
                                    <div class="fw-bold"><?= $appointment['service_name'] ?></div>
                                    <div class="text-muted">
                                        <?= format_currency($appointment['service_price']) ?> • <?= $appointment['service_duration'] ?> mins
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Customer</h6>
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar-circle bg-primary text-white me-3">
                                    <?= strtoupper(substr($appointment['customer_name'], 0, 1)) ?>
                                </div>
                                <div>
                                    <div class="fw-bold"><?= $appointment['customer_name'] ?></div>
                                    <div class="text-muted">
                                        <i class="fas fa-envelope me-1"></i> <?= $appointment['customer_email'] ?>
                                    </div>
                                    <div class="text-muted">
                                        <i class="fas fa-phone me-1"></i> <?= $appointment['customer_phone'] ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Staff</h6>
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar-circle bg-secondary text-white me-3">
                                    <?= strtoupper(substr($appointment['staff_name'], 0, 1)) ?>
                                </div>
                                <div>
                                    <div class="fw-bold"><?= $appointment['staff_name'] ?></div>
                                    <div class="text-muted">
                                        <i class="fas fa-user-tie me-1"></i> Stylist
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <?php if (!empty($appointment['notes'])): ?>
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Notes</h6>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <?= nl2br($appointment['notes']) ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">Appointment Timeline</h6>
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <div class="fw-bold">Appointment Created</div>
                                    <div class="text-muted"><?= format_datetime($appointment['created_at']) ?></div>
                                </div>
                            </div>
                            <?php if ($appointment['status'] != 'pending'): ?>
                                <div class="timeline-item">
                                    <div class="timeline-marker <?= $appointment['status'] == 'cancelled' ? 'bg-danger' : 'bg-primary' ?>"></div>
                                    <div class="timeline-content">
                                        <div class="fw-bold">
                                            Status Changed to <?= ucfirst($appointment['status']) ?>
                                        </div>
                                        <div class="text-muted"><?= format_datetime($appointment['updated_at']) ?></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <div>
                            <a href="<?= base_url('appointments/edit/' . $appointment['id']) ?>" class="btn btn-primary">
                                <i class="fas fa-edit"></i> Edit Appointment
                            </a>
                        </div>
                        <div>
                            <?php if ($appointment['status'] == 'pending'): ?>
                                <form action="<?= base_url('appointments/update-status/' . $appointment['id']) ?>" method="post" class="d-inline">
                                    <input type="hidden" name="status" value="confirmed">
                                    <button type="submit" class="btn btn-success me-2">
                                        <i class="fas fa-check"></i> Confirm
                                    </button>
                                </form>
                                <form action="<?= base_url('appointments/update-status/' . $appointment['id']) ?>" method="post" class="d-inline">
                                    <input type="hidden" name="status" value="cancelled">
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-times"></i> Cancel
                                    </button>
                                </form>
                            <?php elseif ($appointment['status'] == 'confirmed'): ?>
                                <form action="<?= base_url('appointments/update-status/' . $appointment['id']) ?>" method="post" class="d-inline">
                                    <input type="hidden" name="status" value="completed">
                                    <button type="submit" class="btn btn-success me-2">
                                        <i class="fas fa-check-double"></i> Mark as Completed
                                    </button>
                                </form>
                                <form action="<?= base_url('appointments/update-status/' . $appointment['id']) ?>" method="post" class="d-inline">
                                    <input type="hidden" name="status" value="cancelled">
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-times"></i> Cancel
                                    </button>
                                </form>
                            <?php elseif ($appointment['status'] == 'cancelled' && strtotime($appointment['appointment_date']) >= strtotime(date('Y-m-d'))): ?>
                                <form action="<?= base_url('appointments/update-status/' . $appointment['id']) ?>" method="post">
                                    <input type="hidden" name="status" value="confirmed">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-redo"></i> Reactivate
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Actions and Related -->
        <div class="col-md-4 mb-4">
            <!-- Quick Actions -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if ($appointment['status'] == 'completed'): ?>
                            <a href="<?= base_url('billing/create?customer_id=' . $appointment['customer_id'] . '&service_id=' . $appointment['service_id'] . '&staff_id=' . $appointment['staff_id']) ?>" class="btn btn-success">
                                <i class="fas fa-file-invoice"></i> Create Invoice
                            </a>
                        <?php endif; ?>
                        <a href="<?= base_url('appointments/create?customer_id=' . $appointment['customer_id']) ?>" class="btn btn-outline-primary">
                            <i class="fas fa-calendar-plus"></i> New Appointment for Customer
                        </a>
                        <a href="<?= base_url('customers/view/' . $appointment['customer_id']) ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-user"></i> View Customer Profile
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Upcoming Appointments -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">Customer's Upcoming Appointments</h5>
                </div>
                <div class="card-body p-0">
                    <?php
                    // In a real application, you would fetch the customer's upcoming appointments here
                    $upcoming_appointments = []; // This would be populated from the database
                    ?>
                    
                    <?php if (empty($upcoming_appointments)): ?>
                        <div class="p-3 text-center">
                            <p class="text-muted mb-0">No upcoming appointments for this customer.</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($upcoming_appointments as $upcoming): ?>
                                <a href="<?= base_url('appointments/view/' . $upcoming['id']) ?>" class="list-group-item list-group-item-action">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-bold"><?= format_date($upcoming['appointment_date']) ?></div>
                                            <div class="text-muted"><?= date('h:i A', strtotime($upcoming['start_time'])) ?></div>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary"><?= $upcoming['service_name'] ?></span>
                                        </div>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

.icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 15px;
    width: 2px;
    background-color: #e5e7eb;
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.timeline-marker {
    position: absolute;
    left: -30px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 2px #e5e7eb;
}

.timeline-content {
    padding-bottom: 1rem;
}
</style>
