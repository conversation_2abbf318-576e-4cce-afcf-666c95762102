<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="fade-in"><i class="fas fa-cut"></i> Our Services</h1>
        <?php if (is_logged_in() && has_role(['admin', 'manager'])): ?>
            <div>
                <button type="button" class="btn btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#importModal">
                    <i class="fas fa-file-import"></i> Import
                </button>
                <a href="<?= base_url('services/export') ?>" class="btn btn-outline-primary me-2">
                    <i class="fas fa-file-export"></i> Export
                </a>
                <a href="<?= base_url('services/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add New Service
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Filter by category -->
    <div class="card shadow-sm mb-4 fade-in" style="animation-delay: 0.1s;">
        <div class="card-body">
            <h5 class="mb-3">Find the perfect service for you</h5>
            <div class="category-scroll-container mb-3">
                <div class="category-buttons d-flex gap-2">
                    <a href="<?= base_url('services') ?>" class="btn <?= empty($selected_category) ? 'btn-primary' : 'btn-outline-primary' ?> flex-shrink-0">
                        All Categories
                    </a>
                    <?php foreach ($categories as $category): ?>
                        <a href="<?= base_url('services?category_id=' . $category['id']) ?>"
                           class="btn <?= $selected_category == $category['id'] ? 'btn-primary' : 'btn-outline-primary' ?> flex-shrink-0">
                            <?= $category['name'] ?>
                            <span class="badge bg-light text-dark ms-1"><?= $category['service_count'] ?></span>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>

            <form action="<?= base_url('services') ?>" method="get" class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text bg-primary text-white"><i class="fas fa-search"></i></span>
                        <select name="category_id" id="category_id" class="form-select">
                            <option value="">All Categories</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>" <?= $selected_category == $category['id'] ? 'selected' : '' ?>>
                                    <?= $category['name'] ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-4 d-flex align-items-center">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i> Filter
                    </button>
                    <a href="<?= base_url('services') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-redo me-1"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <?php if (empty($services)): ?>
        <div class="alert alert-info fade-in" style="animation-delay: 0.2s;">
            <div class="d-flex align-items-center">
                <i class="fas fa-info-circle fa-2x me-3"></i>
                <div>
                    <h5 class="mb-1">No services found</h5>
                    <p class="mb-0">There are no services available in this category. Please try another category or check back later.</p>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="row">
            <?php foreach ($services as $index => $service): ?>
                <div class="col-md-4 mb-4 fade-in" style="animation-delay: <?= 0.2 + ($index * 0.1) ?>s;">
                    <div class="service-card card h-100">
                        <div class="position-relative">
                            <div class="service-image bg-light" style="height: 180px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-spa fa-4x text-primary opacity-25"></i>
                            </div>
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-<?= $service['status'] == 'active' ? 'success' : 'danger' ?>">
                                    <?= ucfirst($service['status']) ?>
                                </span>
                            </div>
                            <div class="position-absolute bottom-0 start-0 m-2">
                                <span class="badge bg-primary">
                                    <i class="fas fa-tag me-1"></i> <?= $service['category_name'] ?>
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title"><?= $service['name'] ?></h5>
                            <p class="card-text text-muted"><?= $service['description'] ?></p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div>
                                    <span class="badge bg-light text-dark">
                                        <i class="fas fa-clock text-primary me-1"></i> <?= $service['duration'] ?> mins
                                    </span>
                                </div>
                                <h4 class="text-primary mb-0 fw-bold"><?= format_currency($service['price']) ?></h4>
                            </div>
                        </div>
                        <div class="card-footer bg-white border-top-0 pt-0">
                            <?php if (is_logged_in() && has_role(['admin', 'manager'])): ?>
                                <div class="d-flex justify-content-between">
                                    <a href="<?= base_url('services/edit/' . $service['id']) ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit me-1"></i> Edit
                                    </a>
                                    <a href="<?= base_url('services/delete/' . $service['id']) ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this service?')">
                                        <i class="fas fa-trash me-1"></i> Delete
                                    </a>
                                </div>
                            <?php elseif (is_logged_in() && has_role(['customer'])): ?>
                                <a href="<?= base_url('booking/staff?service_id=' . $service['id']) ?>" class="btn btn-primary w-100">
                                    <i class="fas fa-calendar-plus me-1"></i> Book Now
                                </a>
                            <?php else: ?>
                                <a href="<?= base_url('login') ?>" class="btn btn-primary w-100">
                                    <i class="fas fa-sign-in-alt me-1"></i> Login to Book
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">Import Services</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="<?= base_url('services/import') ?>" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="csv_file" class="form-label">CSV File</label>
                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                        <div class="form-text">
                            The CSV file should have the following columns: Name, Category, Description, Duration (mins), Price, Status
                        </div>
                        <div class="alert alert-info mt-2 small">
                            <i class="fas fa-info-circle"></i> Services with the same name and category will be updated. Services with the same name but different category will be added as new services.
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="has_header" name="has_header" checked>
                            <label class="form-check-label" for="has_header">
                                File has header row
                            </label>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-file-import"></i> Import
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.service-card {
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
}

.category-scroll-container {
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: #dee2e6 transparent;
}

.category-scroll-container::-webkit-scrollbar {
    height: 6px;
}

.category-scroll-container::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 3px;
}

.category-scroll-container::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 3px;
}

.category-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

.category-buttons {
    min-width: max-content;
    padding-bottom: 5px;
}

.category-buttons .btn {
    white-space: nowrap;
    min-width: fit-content;
}

.category-buttons .badge {
    font-size: 0.7em;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.service-image {
    transition: all 0.5s ease;
    overflow: hidden;
}

.service-card:hover .service-image i {
    transform: scale(1.1);
    opacity: 0.5 !important;
}
</style>
