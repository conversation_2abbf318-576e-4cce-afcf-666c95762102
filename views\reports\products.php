<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-shopping-bag"></i> Product Sales Report</h1>
    </div>

    <!-- Date Range Filter -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('reports/products') ?>" method="get" class="row align-items-end">
                <div class="col-md-4 mb-2">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                </div>
                <div class="col-md-4 mb-2">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                </div>
                <div class="col-md-4 mb-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i> Apply Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <!-- Reports Navigation -->
        <div class="col-md-3 mb-4">
            <?php include 'partials/reports_nav.php'; ?>
        </div>
        
        <!-- Reports Content -->
        <div class="col-md-9">
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Units Sold</h6>
                                    <h4 class="mb-0"><?= $product_sales_report['totals']['quantity_sold'] ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-box text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Revenue</h6>
                                    <h4 class="mb-0"><?= format_currency($product_sales_report['totals']['total_amount']) ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-dollar-sign text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Profit</h6>
                                    <h4 class="mb-0"><?= format_currency($product_sales_report['totals']['profit']) ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-chart-line text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Product Sales Chart -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Top Products by Revenue</h5>
                    <a href="<?= base_url('reports/export?type=products&start_date=' . $start_date . '&end_date=' . $end_date) ?>" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-download me-1"></i> Export CSV
                    </a>
                </div>
                <div class="card-body">
                    <canvas id="productChart" height="300"></canvas>
                </div>
            </div>
            
            <!-- Product Sales Data Table -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Product Sales Data</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($product_sales_report['data'])): ?>
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No product sales data available for the selected period.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th class="text-end">Price</th>
                                        <th class="text-end">Quantity</th>
                                        <th class="text-end">Revenue</th>
                                        <th class="text-end">Profit</th>
                                        <th class="text-end">Margin</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($product_sales_report['data'] as $row): ?>
                                        <tr>
                                            <td><?= $row['product_name'] ?></td>
                                            <td class="text-end"><?= format_currency($row['product_price']) ?></td>
                                            <td class="text-end"><?= $row['quantity_sold'] ?></td>
                                            <td class="text-end"><?= format_currency($row['total_amount']) ?></td>
                                            <td class="text-end"><?= format_currency($row['profit']) ?></td>
                                            <td class="text-end"><?= number_format(($row['profit'] / $row['total_amount']) * 100, 1) ?>%</td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="2">Total</th>
                                        <th class="text-end"><?= $product_sales_report['totals']['quantity_sold'] ?></th>
                                        <th class="text-end"><?= format_currency($product_sales_report['totals']['total_amount']) ?></th>
                                        <th class="text-end"><?= format_currency($product_sales_report['totals']['profit']) ?></th>
                                        <th class="text-end"><?= number_format(($product_sales_report['totals']['profit'] / $product_sales_report['totals']['total_amount']) * 100, 1) ?>%</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Product Sales Chart
    const productData = <?= json_encode($product_sales_report['data']) ?>;
    
    if (productData.length > 0) {
        // Sort data by total amount in descending order
        productData.sort((a, b) => b.total_amount - a.total_amount);
        
        // Take top 10 products
        const topProducts = productData.slice(0, 10);
        
        const productLabels = topProducts.map(item => item.product_name);
        const revenueData = topProducts.map(item => item.total_amount);
        const profitData = topProducts.map(item => item.profit);
        
        const productCtx = document.getElementById('productChart').getContext('2d');
        new Chart(productCtx, {
            type: 'bar',
            data: {
                labels: productLabels,
                datasets: [
                    {
                        label: 'Revenue',
                        data: revenueData,
                        backgroundColor: 'rgba(52, 152, 219, 0.7)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Profit',
                        data: profitData,
                        backgroundColor: 'rgba(46, 204, 113, 0.7)',
                        borderColor: 'rgba(46, 204, 113, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '<?= CURRENCY_SYMBOL ?>' + value;
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': <?= CURRENCY_SYMBOL ?>' + context.raw;
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>
