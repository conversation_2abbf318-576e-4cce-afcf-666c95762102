<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-users"></i> Staff Management</h1>
        <a href="<?= base_url('staff/create') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Staff
        </a>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <?php if (empty($staff)): ?>
                <div class="text-center py-5">
                    <img src="<?= base_url('assets/images/empty-staff.svg') ?>" alt="No Staff" style="width: 120px; opacity: 0.5;" class="mb-3">
                    <h5>No Staff Members Found</h5>
                    <p class="text-muted">You haven't added any staff members yet.</p>
                    <a href="<?= base_url('staff/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Staff Member
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Position</th>
                                <th>Salary</th>
                                <th>Commission</th>
                                <th>Status</th>
                                <th width="15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($staff as $index => $member): ?>
                                <tr>
                                    <td><?= $index + 1 ?></td>
                                    <td>
                                        <div class="fw-bold"><?= $member['name'] ?></div>
                                        <div class="small text-muted"><?= isset($member['role']) ? ucfirst($member['role']) : '' ?></div>
                                    </td>
                                    <td>
                                        <div><?= $member['email'] ?></div>
                                        <div><?= $member['phone'] ?></div>
                                    </td>
                                    <td><?= $member['position'] ?></td>
                                    <td><?= format_currency($member['salary']) ?>/month</td>
                                    <td><?= $member['commission_rate'] ?>%</td>
                                    <td>
                                        <?php if ($member['status'] == 'active'): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= base_url('staff/view/' . $member['id']) ?>" class="btn btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('staff/edit/' . $member['id']) ?>" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if ($member['user_id'] != 1): ?>
                                            <a href="<?= base_url('staff/delete/' . $member['id']) ?>" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this staff member? This action cannot be undone.')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
