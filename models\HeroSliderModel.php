<?php
/**
 * Hero Slider Model
 * 
 * Handles operations related to hero sliders on the homepage
 */
class HeroSliderModel extends Model {
    protected $table = 'hero_sliders';
    protected $primaryKey = 'id';

    /**
     * Get all active sliders ordered by order_index
     * 
     * @return array Array of slider data
     */
    public function getAllActive() {
        $query = "SELECT * FROM {$this->table} WHERE status = 'active' ORDER BY order_index ASC";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get all sliders for admin management
     * 
     * @return array Array of slider data
     */
    public function getAll() {
        $query = "SELECT * FROM {$this->table} ORDER BY order_index ASC";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Add a new slider
     * 
     * @param array $data Slider data
     * @return int|bool ID of the inserted slider or false on failure
     */
    public function add($data) {
        // Get the highest order_index and add 1
        $query = "SELECT MAX(order_index) as max_order FROM {$this->table}";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $order_index = ($result['max_order'] ?? 0) + 1;
        
        $query = "INSERT INTO {$this->table} (title, subtitle, image_path, button_text, button_link, order_index, status) 
                  VALUES (:title, :subtitle, :image_path, :button_text, :button_link, :order_index, :status)";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':title', $data['title']);
        $stmt->bindParam(':subtitle', $data['subtitle']);
        $stmt->bindParam(':image_path', $data['image_path']);
        $stmt->bindParam(':button_text', $data['button_text']);
        $stmt->bindParam(':button_link', $data['button_link']);
        $stmt->bindParam(':order_index', $order_index);
        $stmt->bindParam(':status', $data['status']);
        
        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }
    
    /**
     * Update a slider
     * 
     * @param int $id Slider ID
     * @param array $data Slider data
     * @return bool Success or failure
     */
    public function update($id, $data) {
        $query = "UPDATE {$this->table} SET 
                  title = :title, 
                  subtitle = :subtitle, 
                  image_path = :image_path, 
                  button_text = :button_text, 
                  button_link = :button_link, 
                  status = :status 
                  WHERE id = :id";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':title', $data['title']);
        $stmt->bindParam(':subtitle', $data['subtitle']);
        $stmt->bindParam(':image_path', $data['image_path']);
        $stmt->bindParam(':button_text', $data['button_text']);
        $stmt->bindParam(':button_link', $data['button_link']);
        $stmt->bindParam(':status', $data['status']);
        $stmt->bindParam(':id', $id);
        
        return $stmt->execute();
    }
    
    /**
     * Update slider order
     * 
     * @param array $order_data Array of slider IDs and their new order
     * @return bool Success or failure
     */
    public function updateOrder($order_data) {
        $success = true;
        
        foreach ($order_data as $id => $order) {
            $query = "UPDATE {$this->table} SET order_index = :order WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':order', $order);
            $stmt->bindParam(':id', $id);
            
            if (!$stmt->execute()) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * Delete a slider
     * 
     * @param int $id Slider ID
     * @return bool Success or failure
     */
    public function delete($id) {
        // Get the image path before deleting
        $query = "SELECT image_path FROM {$this->table} WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        $slider = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Delete the record
        $query = "DELETE FROM {$this->table} WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $result = $stmt->execute();
        
        // Return the image path and result for file deletion
        return [
            'success' => $result,
            'image_path' => $slider['image_path'] ?? null
        ];
    }
}
