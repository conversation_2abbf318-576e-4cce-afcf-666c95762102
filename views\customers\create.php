<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-user-plus"></i> New Customer</h1>
        <div>
            <a href="<?= base_url('customers') ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Customers
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <form action="<?= base_url('customers/store') ?>" method="post" autocomplete="off">
                <!-- Hidden field to prevent browser autofill -->
                <input type="text" style="display:none" name="prevent_autofill" id="prevent_autofill" value="" />
                <div class="row">
                    <!-- Personal Information -->
                    <div class="col-md-12 mb-4">
                        <h5 class="border-bottom pb-2">Personal Information</h5>
                    </div>

                    <!-- Name -->
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                    </div>

                    <!-- Phone -->
                    <div class="col-md-6 mb-3">
                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                            <input type="tel" class="form-control" id="phone" name="phone" required>
                        </div>
                    </div>

                    <!-- Gender -->
                    <div class="col-md-6 mb-3">
                        <label for="gender" class="form-label">Gender <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-venus-mars"></i></span>
                            <select class="form-select" id="gender" name="gender" required>
                                <option value="">Select Gender</option>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>

                    <!-- Date of Birth -->
                    <div class="col-md-6 mb-3">
                        <label for="date_of_birth" class="form-label">Date of Birth</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth">
                        </div>
                    </div>

                    <!-- Address -->
                    <div class="col-md-6 mb-3">
                        <label for="address" class="form-label">Address</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                            <input type="text" class="form-control" id="address" name="address">
                        </div>
                    </div>

                    <!-- Account Information -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2">Account Information</h5>
                    </div>

                    <!-- Password -->
                    <div class="col-md-6 mb-3">
                        <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="text" class="form-control" id="password" name="password" required autocomplete="off" value="">
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="fas fa-eye-slash"></i>
                            </button>
                        </div>
                        <div class="form-text">Auto-generated. You can change it if needed.</div>
                    </div>

                    <!-- Confirm Password -->
                    <div class="col-md-6 mb-3">
                        <label for="confirm_password" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="text" class="form-control" id="confirm_password" name="confirm_password" required autocomplete="off" value="">
                        </div>
                        <div class="form-text">Should match the password field</div>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-md-12 mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Create Customer
                        </button>
                        <a href="<?= base_url('customers') ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const nameInput = document.getElementById('name');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const togglePasswordBtn = document.getElementById('togglePassword');

    // Get salon name from PHP
    const salonName = '<?= get_salon_name() ?>';

    // Function to generate password from salon name
    function generatePasswordFromSalonName(salonName) {
        if (!salonName) return 'Sa@12345'; // Default if no salon name

        // Split the salon name into words
        const words = salonName.trim().split(/\s+/);

        // Get first letter of first word (capitalized)
        const firstLetter = words[0].charAt(0).toUpperCase();

        // Get first letter of second word if it exists, otherwise use the second letter of first word
        let secondLetter = '';
        if (words.length > 1 && words[1]) {
            secondLetter = words[1].charAt(0).toLowerCase();
        } else if (words[0].length > 1) {
            secondLetter = words[0].charAt(1).toLowerCase();
        }

        // Create password in format: Xs@12345
        return firstLetter + secondLetter + '@12345';
    }

    // Generate the password based on salon name
    const generatedPassword = generatePasswordFromSalonName(salonName);

    // Set the password in both fields
    passwordInput.value = generatedPassword;
    confirmPasswordInput.value = generatedPassword;

    // Force the browser to show the value
    setTimeout(function() {
        passwordInput.focus();
        passwordInput.blur();
        confirmPasswordInput.focus();
        confirmPasswordInput.blur();
    }, 100);

    // Toggle password visibility
    togglePasswordBtn.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        confirmPasswordInput.setAttribute('type', type);

        // Toggle icon
        const icon = this.querySelector('i');
        if (type === 'password') {
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        } else {
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        }
    });

    // No need to trigger name input event anymore since we're using salon name directly
});
</script>
