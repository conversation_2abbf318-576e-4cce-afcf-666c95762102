-- Simple fix for customer_memberships table
-- This script ensures the table has the correct structure

-- First, check if the table exists
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'customer_memberships';

-- If the table doesn't exist, create it with the correct structure
SET @query = IF(@table_exists = 0, 
'CREATE TABLE customer_memberships (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    plan_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    payment_method ENUM("cash", "card", "upi", "other") NOT NULL DEFAULT "cash",
    amount_paid DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    status ENUM("active", "expired", "cancelled") NOT NULL DEFAULT "active",
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (plan_id) REFERENCES membership_plans(id)
)',
'SELECT "Table already exists"');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- If the table exists, make sure it has the correct columns
SET @plan_id_exists = 0;
SELECT COUNT(*) INTO @plan_id_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'customer_memberships' 
AND COLUMN_NAME = 'plan_id';

-- Add plan_id column if it doesn't exist
SET @query = IF(@plan_id_exists = 0, 
'ALTER TABLE customer_memberships ADD COLUMN plan_id INT NOT NULL AFTER customer_id,
ADD FOREIGN KEY (plan_id) REFERENCES membership_plans(id)',
'SELECT "plan_id column already exists"');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check for payment_method column
SET @payment_method_exists = 0;
SELECT COUNT(*) INTO @payment_method_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'customer_memberships' 
AND COLUMN_NAME = 'payment_method';

-- Add payment_method column if it doesn't exist
SET @query = IF(@payment_method_exists = 0, 
'ALTER TABLE customer_memberships ADD COLUMN payment_method ENUM("cash", "card", "upi", "other") NOT NULL DEFAULT "cash" AFTER end_date',
'SELECT "payment_method column already exists"');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check for amount_paid column
SET @amount_paid_exists = 0;
SELECT COUNT(*) INTO @amount_paid_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'customer_memberships' 
AND COLUMN_NAME = 'amount_paid';

-- Add amount_paid column if it doesn't exist
SET @query = IF(@amount_paid_exists = 0, 
'ALTER TABLE customer_memberships ADD COLUMN amount_paid DECIMAL(10, 2) NOT NULL DEFAULT 0.00 AFTER payment_method',
'SELECT "amount_paid column already exists"');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check for notes column
SET @notes_exists = 0;
SELECT COUNT(*) INTO @notes_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'customer_memberships' 
AND COLUMN_NAME = 'notes';

-- Add notes column if it doesn't exist
SET @query = IF(@notes_exists = 0, 
'ALTER TABLE customer_memberships ADD COLUMN notes TEXT AFTER status',
'SELECT "notes column already exists"');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
