<?php
/**
 * Helper functions
 */

/**
 * Get a setting value
 *
 * @param string $key Setting key
 * @param mixed $default Default value if setting not found
 * @return mixed Setting value
 */
function get_setting($key, $default = null) {
    // Create SettingsModel instance
    $settings_model = new SettingsModel();
    return $settings_model->getValue($key, $default);
}

/**
 * Get the salon name
 *
 * @return string Salon name
 */
function get_salon_name() {
    return get_setting('salon_name', APP_NAME);
}

/**
 * Get the base URL
 *
 * @param string $path Path to append to base URL
 * @return string Complete URL
 */
function base_url($path = '') {
    // Use the constant defined in environment.php
    $base = BASE_URL;

    // For AJAX requests in JavaScript, allow overriding with a global variable
    if (isset($GLOBALS['base_url_override'])) {
        $base = $GLOBALS['base_url_override'];
    }

    // Ensure path doesn't start with a slash when concatenating
    return $base . ltrim($path, '/');
}

/**
 * Get the current URL
 *
 * @return string Current URL
 */
function current_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = $_SERVER['REQUEST_URI'];

    return $protocol . '://' . $host . $uri;
}

/**
 * Format a date
 *
 * @param string $date Date string
 * @param string $format Format string
 * @return string Formatted date
 */
function format_date($date, $format = 'd M Y') {
    return date($format, strtotime($date));
}

/**
 * Format a datetime
 *
 * @param string $datetime Datetime string
 * @param string $format Format string
 * @return string Formatted datetime
 */
function format_datetime($datetime, $format = 'd M Y h:i A') {
    return date($format, strtotime($datetime));
}

/**
 * Format time only
 *
 * @param string $datetime Datetime string
 * @return string Formatted time
 */
function format_time($datetime) {
    if (empty($datetime)) {
        return '';
    }

    return date('g:i A', strtotime($datetime));
}

/**
 * Format currency with proper symbol
 *
 * @param float|null $amount Amount to format
 * @return string Formatted currency
 */
function format_currency($amount) {
    // Handle null or empty values
    if ($amount === null || $amount === '') {
        $amount = 0;
    }

    // Convert to float to ensure it's numeric
    $amount = (float) $amount;

    // Convert HTML entity to actual character for display
    $symbol = CURRENCY_SYMBOL;
    return $symbol . number_format($amount, 2);
}

/**
 * Check if user is logged in
 *
 * @return bool
 */
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

/**
 * Get current user ID
 *
 * @return int|null User ID or null if not logged in
 */
function current_user_id() {
    return $_SESSION['user_id'] ?? null;
}

/**
 * Get current user role
 *
 * @return string|null User role or null if not logged in
 */
function current_user_role() {
    return $_SESSION['user_role'] ?? null;
}

/**
 * Check if current user has a specific role
 *
 * @param string|array $roles Role(s) to check
 * @return bool
 */
function has_role($roles) {
    if (!is_logged_in()) {
        return false;
    }

    if (is_array($roles)) {
        return in_array(current_user_role(), $roles);
    }

    return current_user_role() == $roles;
}

/**
 * Check if current user has a specific permission
 *
 * @param string|array $permissions Permission(s) to check
 * @return bool
 */
function has_permission($permissions) {
    // For now, we'll simplify and assume admin role has all permissions
    if (has_role('admin')) {
        return true;
    }

    // For other roles, we could implement a more complex permission system
    // This is a placeholder for future implementation
    return true;
}

/**
 * Generate a random string
 *
 * @param int $length String length
 * @return string Random string
 */
function random_string($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $string = '';

    for ($i = 0; $i < $length; $i++) {
        $string .= $characters[rand(0, strlen($characters) - 1)];
    }

    return $string;
}

/**
 * Flash message
 *
 * @param string $key Message key
 * @param string $message Message text
 * @param bool $keep Whether to keep the message after retrieval (default: false)
 * @return string|null Message text or null
 */
function flash($key, $message = null, $keep = false) {
    if ($message) {
        // Setting a flash message
        $_SESSION['flash'][$key] = $message;
        error_log("Flash message set: $key = $message");
    } else {
        // Retrieving a flash message
        $message = $_SESSION['flash'][$key] ?? null;

        // Only unset if $keep is false
        if (!$keep && isset($_SESSION['flash'][$key])) {
            error_log("Flash message retrieved and unset: $key = $message");
            unset($_SESSION['flash'][$key]);
        } else if ($message) {
            error_log("Flash message retrieved but kept: $key = $message");
        }

        return $message;
    }
}

/**
 * Sanitize input
 *
 * @param mixed $input Input to sanitize
 * @return mixed Sanitized input
 */
function sanitize($input) {
    if (is_array($input)) {
        $sanitized = [];
        foreach ($input as $key => $value) {
            $sanitized[$key] = sanitize($value); // Recursively sanitize arrays
        }
        return $sanitized;
    }

    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Get input value
 *
 * @param string $key Input key
 * @param mixed $default Default value
 * @return mixed Input value
 */
function input($key, $default = '') {
    if (isset($_POST[$key])) {
        return sanitize($_POST[$key]);
    }

    if (isset($_GET[$key])) {
        return sanitize($_GET[$key]);
    }

    return $default;
}

