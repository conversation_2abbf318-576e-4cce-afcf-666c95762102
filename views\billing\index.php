<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-file-invoice"></i> Billing</h1>
        <a href="<?= base_url('billing/create') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> New Invoice
        </a>
    </div>

    <!-- Sales Statistics -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">Today's Sales</h6>
                            <h3 class="mb-0"><?= format_currency($sales_stats['today']) ?></h3>
                        </div>
                        <div class="icon-circle bg-success-light text-success">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">This Week</h6>
                            <h3 class="mb-0"><?= format_currency($sales_stats['this_week']) ?></h3>
                        </div>
                        <div class="icon-circle bg-primary-light text-primary">
                            <i class="fas fa-calendar-week"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">This Month</h6>
                            <h3 class="mb-0"><?= format_currency($sales_stats['this_month']) ?></h3>
                        </div>
                        <div class="icon-circle bg-info-light text-info">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">This Year</h6>
                            <h3 class="mb-0"><?= format_currency($sales_stats['this_year']) ?></h3>
                        </div>
                        <div class="icon-circle bg-warning-light text-warning">
                            <i class="fas fa-calendar"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('billing') ?>" method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="date_from" class="form-label">Date From</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?= $selected_date_from ?>">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">Date To</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?= $selected_date_to ?>">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="unpaid" <?= $selected_status == 'unpaid' ? 'selected' : '' ?>>Unpaid</option>
                        <option value="partial" <?= $selected_status == 'partial' ? 'selected' : '' ?>>Partial</option>
                        <option value="paid" <?= $selected_status == 'paid' ? 'selected' : '' ?>>Paid</option>
                        <option value="cancelled" <?= $selected_status == 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="customer_id" class="form-label">Customer</label>
                    <select class="form-select" id="customer_id" name="customer_id">
                        <option value="">All Customers</option>
                        <?php foreach ($customers as $customer): ?>
                            <option value="<?= $customer['id'] ?>" <?= $selected_customer == $customer['id'] ? 'selected' : '' ?>>
                                <?= $customer['name'] ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-12 d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter"></i> Filter
                    </button>
                    <a href="<?= base_url('billing') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-redo"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Invoices List -->
    <div class="card shadow-sm">
        <div class="card-body p-0">
            <?php if (empty($invoices)): ?>
                <div class="p-4 text-center">
                    <img src="<?= base_url('assets/images/invoice.svg') ?>" alt="No Invoices" style="width: 120px; opacity: 0.5;" class="mb-3">
                    <p class="text-muted mb-0">No invoices found matching your criteria.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Invoice #</th>
                                <th>Date</th>
                                <th>Customer</th>
                                <th>Amount</th>
                                <th>Payment Method</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($invoices as $invoice): ?>
                                <tr>
                                    <td>
                                        <a href="<?= base_url('billing/view/' . $invoice['id']) ?>" class="fw-bold text-primary">
                                            #<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?>
                                        </a>
                                    </td>
                                    <td><?= format_date($invoice['invoice_date']) ?></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle bg-primary text-white me-2">
                                                <?= strtoupper(substr($invoice['customer_name'], 0, 1)) ?>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?= $invoice['customer_name'] ?></div>
                                                <div class="small text-muted"><?= $invoice['customer_phone'] ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="fw-bold"><?= format_currency($invoice['total_amount']) ?></div>
                                        <div class="small text-muted">
                                            <?php if ($invoice['discount_amount'] > 0): ?>
                                                Discount: <?= format_currency($invoice['discount_amount']) ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td><?= ucfirst($invoice['payment_method']) ?></td>
                                    <td>
                                        <?php if ($invoice['payment_status'] == 'unpaid'): ?>
                                            <span class="badge bg-warning">Unpaid</span>
                                        <?php elseif ($invoice['payment_status'] == 'partial'): ?>
                                            <span class="badge bg-info">Partial</span>
                                        <?php elseif ($invoice['payment_status'] == 'paid'): ?>
                                            <span class="badge bg-success">Paid</span>
                                        <?php elseif ($invoice['payment_status'] == 'cancelled'): ?>
                                            <span class="badge bg-danger">Cancelled</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex">
                                            <a href="<?= base_url('billing/view/' . $invoice['id']) ?>" class="btn btn-sm btn-outline-primary me-1" data-bs-toggle="tooltip" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('billing/print/' . $invoice['id']) ?>" class="btn btn-sm btn-outline-info me-1" data-bs-toggle="tooltip" title="Print" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <?php if (has_role(['admin', 'manager'])): ?>
                                                <?php if ($invoice['payment_status'] != 'cancelled'): ?>
                                                    <a href="<?= base_url('billing/edit/' . $invoice['id']) ?>" class="btn btn-sm btn-outline-secondary me-1" data-bs-toggle="tooltip" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <?php if ($invoice['payment_status'] != 'paid' && $invoice['payment_status'] != 'cancelled'): ?>
                                                    <a href="<?= base_url('billing/delete/' . $invoice['id']) ?>" class="btn btn-sm btn-outline-danger" data-bs-toggle="tooltip" title="Delete" onclick="return confirm('Are you sure you want to delete this invoice?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

