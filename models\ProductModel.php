<?php
/**
 * Product Model
 */
class ProductModel extends Model {
    protected $table = 'products';

    /**
     * Get product with category
     *
     * @param int $id Product ID
     * @return array|false Product data or false if not found
     */
    public function getWithCategory($id) {
        $query = "SELECT p.*, c.name as category_name
                 FROM " . $this->table . " p
                 JOIN product_categories c ON p.category_id = c.id
                 WHERE p.id = :id
                 LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get all products with categories
     *
     * @param int $category_id Filter by category ID (optional)
     * @param string $status Filter by status (optional)
     * @param int $limit Limit number of results (optional)
     * @return array Products
     */
    public function getAllWithCategories($category_id = null, $status = null, $limit = null) {
        try {
            $query = "SELECT p.*, c.name as category_name
                     FROM " . $this->table . " p
                     LEFT JOIN product_categories c ON p.category_id = c.id
                     WHERE 1=1";

            $params = [];

            if ($category_id) {
                $query .= " AND p.category_id = :category_id";
                $params[':category_id'] = $category_id;
            }

            // Only filter by status if specified and if there are products with that status
            if ($status) {
                $check_query = "SELECT COUNT(*) FROM " . $this->table . " WHERE status = :status";
                $check_stmt = $this->db->prepare($check_query);
                $check_stmt->bindParam(':status', $status);
                $check_stmt->execute();
                $status_count = $check_stmt->fetchColumn();

                if ($status_count > 0) {
                    $query .= " AND p.status = :status";
                    $params[':status'] = $status;
                } else {
                    error_log("No products found with status: $status. Ignoring status filter.");
                }
            }

            $query .= " ORDER BY p.name";

            // Add LIMIT as a direct value, not a parameter
            if ($limit) {
                $query .= " LIMIT " . (int)$limit;
            }

            $stmt = $this->db->prepare($query);

            // Bind all parameters
            foreach ($params as $param => $value) {
                $stmt->bindValue($param, $value);
            }

            $stmt->execute();
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // If no results and we were filtering, try again without filters
            if (empty($results) && ($category_id || $status)) {
                error_log("No products found with filters. Trying without filters.");
                return $this->getAllWithCategories(null, null, $limit);
            }

            return $results;
        } catch (PDOException $e) {
            error_log("Error in getAllWithCategories: " . $e->getMessage());

            // Fallback to a simpler query if the first one fails
            try {
                $query = "SELECT p.*, '' as category_name FROM " . $this->table . " p ORDER BY p.name";
                if ($limit) {
                    $query .= " LIMIT " . (int)$limit;
                }

                $stmt = $this->db->prepare($query);
                $stmt->execute();
                return $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e2) {
                error_log("Error in fallback getAllWithCategories: " . $e2->getMessage());
                return [];
            }
        }
    }

    /**
     * Get products by category ID
     *
     * @param int $category_id Category ID
     * @return array Products
     */
    public function getByCategoryId($category_id) {
        $query = "SELECT p.*, c.name as category_name
                 FROM " . $this->table . " p
                 JOIN product_categories c ON p.category_id = c.id
                 WHERE p.category_id = :category_id
                 ORDER BY p.name";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':category_id', $category_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get low stock products
     *
     * @return array Products
     */
    public function getLowStockProducts() {
        $query = "SELECT p.*, c.name as category_name
                 FROM " . $this->table . " p
                 JOIN product_categories c ON p.category_id = c.id
                 WHERE p.quantity <= p.low_stock_threshold AND p.status = 'active'
                 ORDER BY p.quantity ASC";
        $stmt = $this->db->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Update product quantity
     *
     * @param int $id Product ID
     * @param int $quantity New quantity
     * @return bool Success or failure
     */
    public function updateQuantity($id, $quantity) {
        $query = "UPDATE " . $this->table . " SET quantity = :quantity WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':quantity', $quantity);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }

    /**
     * Count products by category ID
     *
     * @param int $category_id Category ID
     * @return int Number of products in the category
     */
    public function countByCategoryId($category_id) {
        $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE category_id = :category_id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':category_id', $category_id);
        $stmt->execute();

        return $stmt->fetchColumn();
    }



    /**
     * Search products
     *
     * @param string $search Search term
     * @return array Products
     */
    public function search($search) {
        try {
            $query = "SELECT p.*, c.name as category_name
                     FROM " . $this->table . " p
                     LEFT JOIN product_categories c ON p.category_id = c.id
                     WHERE (p.name LIKE :search
                        OR p.description LIKE :search
                        OR p.sku LIKE :search
                        OR p.brand LIKE :search)";

            // Only filter by active status if there are active products
            $check_query = "SELECT COUNT(*) FROM " . $this->table . " WHERE status = 'active'";
            $check_stmt = $this->db->prepare($check_query);
            $check_stmt->execute();
            $active_count = $check_stmt->fetchColumn();

            if ($active_count > 0) {
                $query .= " AND p.status = 'active'";
            }

            $query .= " ORDER BY p.name";

            $stmt = $this->db->prepare($query);
            $search_term = "%$search%";
            $stmt->bindParam(':search', $search_term);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error in product search: " . $e->getMessage());

            // Fallback to a simpler query if the first one fails
            try {
                $query = "SELECT p.*, '' as category_name
                         FROM " . $this->table . " p
                         WHERE p.name LIKE :search
                         ORDER BY p.name";
                $stmt = $this->db->prepare($query);
                $search_term = "%$search%";
                $stmt->bindParam(':search', $search_term);
                $stmt->execute();

                return $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e2) {
                error_log("Error in fallback product search: " . $e2->getMessage());
                return [];
            }
        }
    }

    /**
     * Find product by name
     *
     * @param string $name Product name
     * @return array|false Product data or false if not found
     */
    public function findByName($name) {
        $query = "SELECT * FROM " . $this->table . " WHERE name = :name LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':name', $name);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Find product by SKU
     *
     * @param string $sku Product SKU
     * @return array|false Product data or false if not found
     */
    public function findBySku($sku) {
        $query = "SELECT * FROM " . $this->table . " WHERE sku = :sku LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':sku', $sku);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}

