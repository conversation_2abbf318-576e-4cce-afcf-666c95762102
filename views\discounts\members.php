<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-users"></i> <?= $membership['name'] ?> Members</h1>
        <div>
            <a href="<?= base_url('discounts/add-member/' . $membership['id']) ?>" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Add Member
            </a>
            <a href="<?= base_url('discounts') ?>" class="btn btn-outline-primary ms-2">
                <i class="fas fa-arrow-left"></i> Back to Discount Plans
            </a>
        </div>
    </div>

    <!-- Membership Details -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">Membership Details</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-sm">
                        <tr>
                            <th width="30%">Name:</th>
                            <td><?= $membership['name'] ?></td>
                        </tr>
                        <tr>
                            <th>Duration:</th>
                            <td><?= $membership['duration'] ?> days</td>
                        </tr>
                        <tr>
                            <th>Price:</th>
                            <td><?= format_currency($membership['price']) ?></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-sm">
                        <tr>
                            <th width="30%">Status:</th>
                            <td>
                                <?php if ($membership['status'] == 'active'): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Inactive</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Discount:</th>
                            <td><?= $membership['service_discount'] ? $membership['service_discount'] . '%' : 'None' ?></td>
                        </tr>
                        <tr>
                            <th>Benefits:</th>
                            <td>
                                <span class="text-muted">Service discount: <?= $membership['service_discount'] ?>%</span><br>
                                <span class="text-muted">Product discount: <?= $membership['product_discount'] ?>%</span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Members List -->
    <div class="card shadow-sm">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Members</h5>
            <span class="badge bg-primary"><?= count($members) ?> members</span>
        </div>
        <div class="card-body">
            <?php if (empty($members)): ?>
                <div class="text-center py-5">
                    <img src="<?= base_url('assets/images/empty-members.svg') ?>" alt="No Members" style="width: 120px; opacity: 0.5;" class="mb-3">
                    <h5>No Members Found</h5>
                    <p class="text-muted">This membership doesn't have any members yet.</p>
                    <a href="<?= base_url('discounts/add-member/' . $membership['id']) ?>" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Add First Member
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th>Customer</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Payment</th>
                                <th>Status</th>
                                <th width="15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($members as $index => $member): ?>
                                <tr>
                                    <td><?= $index + 1 ?></td>
                                    <td>
                                        <div class="fw-bold"><?= $member['customer_name'] ?></div>
                                        <div class="small text-muted"><?= $member['customer_phone'] ?></div>
                                    </td>
                                    <td><?= format_date($member['start_date']) ?></td>
                                    <td><?= format_date($member['end_date']) ?></td>
                                    <td><?= format_currency($member['amount_paid']) ?></td>
                                    <td>
                                        <?php if ($member['status'] == 'active' && strtotime($member['end_date']) >= strtotime('today')): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php elseif ($member['status'] == 'active' && strtotime($member['end_date']) < strtotime('today')): ?>
                                            <span class="badge bg-warning">Expired</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Cancelled</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= base_url('customers/view/' . $member['customer_id']) ?>" class="btn btn-outline-primary" title="View Customer">
                                                <i class="fas fa-user"></i>
                                            </a>
                                            <?php if ($member['status'] == 'active'): ?>
                                                <a href="<?= base_url('discounts/cancel-membership/' . $member['id']) ?>" class="btn btn-outline-warning" title="Cancel Membership" onclick="return confirm('Are you sure you want to cancel this membership?')">
                                                    <i class="fas fa-ban"></i>
                                                </a>
                                            <?php else: ?>
                                                <a href="<?= base_url('discounts/renew-membership/' . $member['id']) ?>" class="btn btn-outline-success" title="Renew Membership" onclick="return confirm('Are you sure you want to renew this membership?')">
                                                    <i class="fas fa-sync"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>


