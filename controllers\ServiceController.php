<?php
/**
 * Service Controller
 */
class ServiceController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in for admin actions
        if (in_array($this->getAction(), ['create', 'store', 'edit', 'update', 'delete'])) {
            if (!is_logged_in()) {
                flash('error', 'Please login to access this page');
                header('Location: ' . base_url('login'));
                exit;
            }

            // Check if user has admin or manager role
            if (!has_role(['admin', 'manager'])) {
                flash('error', 'You do not have permission to access this page');
                header('Location: ' . base_url());
                exit;
            }
        }
    }

    /**
     * Get current action
     */
    private function getAction() {
        // Default to index if REQUEST_URI is not set (e.g., in CLI mode)
        if (!isset($_SERVER['REQUEST_URI'])) {
            return 'index';
        }

        $uri = $_SERVER['REQUEST_URI'];
        $parts = explode('/', trim(parse_url($uri, PHP_URL_PATH), '/'));
        $servicePart = array_search('services', $parts);

        if ($servicePart !== false && isset($parts[$servicePart + 1])) {
            return $parts[$servicePart + 1];
        }

        return 'index';
    }

    /**
     * Display all services
     */
    public function index() {
        // Get service categories for filter (only categories with services)
        $category_model = new ServiceCategoryModel();
        $categories = $category_model->getCategoriesWithServices();

        // Get category ID from query string
        $category_id = input('category_id');

        // Get services
        $service_model = new ServiceModel();
        if ($category_id) {
            $services = $service_model->getByCategoryId($category_id);
        } else {
            $services = $service_model->getAllActive();
        }

        // Render view
        $this->render('services/index', [
            'services' => $services,
            'categories' => $categories,
            'selected_category' => $category_id
        ]);
    }

    /**
     * Display service creation form
     */
    public function create() {
        // Get service categories
        $category_model = new ServiceCategoryModel();
        $categories = $category_model->all();

        // Render view
        $this->render('services/create', [
            'categories' => $categories
        ]);
    }

    /**
     * Store new service
     */
    public function store() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('services/create'));
        }

        // Get form data
        $category_id = input('category_id');
        $name = input('name');
        $description = input('description');
        $price = input('price');
        $duration = input('duration');
        $status = input('status');

        // Validate form data
        if (empty($category_id) || empty($name) || empty($price) || empty($duration)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('services/create'));
        }

        // Create service
        $service_model = new ServiceModel();
        $service_data = [
            'category_id' => $category_id,
            'name' => $name,
            'description' => $description,
            'price' => $price,
            'duration' => $duration,
            'status' => $status
        ];

        $service_id = $service_model->create($service_data);

        if (!$service_id) {
            flash('error', 'Failed to create service');
            $this->redirect(base_url('services/create'));
        }

        flash('success', 'Service created successfully');
        $this->redirect(base_url('services'));
    }

    /**
     * Display service edit form
     */
    public function edit($id) {
        // Get service
        $service_model = new ServiceModel();
        $service = $service_model->find($id);

        if (!$service) {
            flash('error', 'Service not found');
            $this->redirect(base_url('services'));
        }

        // Get service categories
        $category_model = new ServiceCategoryModel();
        $categories = $category_model->all();

        // Render view
        $this->render('services/edit', [
            'service' => $service,
            'categories' => $categories
        ]);
    }

    /**
     * Update service
     */
    public function update($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('services/edit/' . $id));
        }

        // Get service
        $service_model = new ServiceModel();
        $service = $service_model->find($id);

        if (!$service) {
            flash('error', 'Service not found');
            $this->redirect(base_url('services'));
        }

        // Get form data
        $category_id = input('category_id');
        $name = input('name');
        $description = input('description');
        $price = input('price');
        $duration = input('duration');
        $status = input('status');

        // Validate form data
        if (empty($category_id) || empty($name) || empty($price) || empty($duration)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('services/edit/' . $id));
        }

        // Update service
        $service_data = [
            'category_id' => $category_id,
            'name' => $name,
            'description' => $description,
            'price' => $price,
            'duration' => $duration,
            'status' => $status
        ];

        $result = $service_model->update($id, $service_data);

        if (!$result) {
            flash('error', 'Failed to update service');
            $this->redirect(base_url('services/edit/' . $id));
        }

        flash('success', 'Service updated successfully');
        $this->redirect(base_url('services'));
    }

    /**
     * Delete service
     */
    public function delete($id) {
        // Get service
        $service_model = new ServiceModel();
        $service = $service_model->find($id);

        if (!$service) {
            flash('error', 'Service not found');
            $this->redirect(base_url('services'));
        }

        // Check if service is used in appointments
        $appointment_model = new AppointmentModel();
        $appointments = $appointment_model->findBy('service_id', $id);

        if (count($appointments) > 0) {
            flash('error', 'Cannot delete service because it is used in appointments');
            $this->redirect(base_url('services'));
        }

        // Delete service
        $result = $service_model->delete($id);

        if (!$result) {
            flash('error', 'Failed to delete service');
            $this->redirect(base_url('services'));
        }

        flash('success', 'Service deleted successfully');
        $this->redirect(base_url('services'));
    }

    /**
     * Export services to CSV
     */
    public function export() {
        // Check if user is logged in and has admin or manager role
        if (!is_logged_in()) {
            flash('error', 'Please login to access this page');
            header('Location: ' . base_url('login'));
            exit;
        }

        if (!has_role(['admin', 'manager'])) {
            flash('error', 'You do not have permission to access this page');
            header('Location: ' . base_url());
            exit;
        }

        // Get services
        $service_model = new ServiceModel();
        $services = $service_model->getAllWithCategories();

        // Set headers
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="services.csv"');

        // Open output stream
        $output = fopen('php://output', 'w');

        // Add header row
        fputcsv($output, ['Name', 'Category', 'Description', 'Duration (mins)', 'Price', 'Status']);

        // Add data rows
        foreach ($services as $service) {
            fputcsv($output, [
                $service['name'],
                $service['category_name'],
                $service['description'],
                $service['duration'],
                $service['price'],
                $service['status']
            ]);
        }

        // Close output stream
        fclose($output);
        exit;
    }

    /**
     * Import services from CSV
     */
    public function import() {
        // Check if user is logged in and has admin or manager role
        if (!is_logged_in()) {
            flash('error', 'Please login to access this page');
            header('Location: ' . base_url('login'));
            exit;
        }

        if (!has_role(['admin', 'manager'])) {
            flash('error', 'You do not have permission to access this page');
            header('Location: ' . base_url());
            exit;
        }

        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('services'));
        }

        // Check if file is uploaded
        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] != UPLOAD_ERR_OK) {
            flash('error', 'Please select a CSV file');
            $this->redirect(base_url('services'));
        }

        // Get file
        $file = $_FILES['csv_file']['tmp_name'];

        // Open file
        $handle = fopen($file, 'r');

        if (!$handle) {
            flash('error', 'Failed to open file');
            $this->redirect(base_url('services'));
        }

        // Skip header row if has_header is checked
        if (isset($_POST['has_header']) && $_POST['has_header'] == 'on') {
            fgetcsv($handle);
        }

        // Initialize counters
        $imported = 0;
        $updated = 0;
        $skipped = 0;

        // Get models
        $service_model = new ServiceModel();
        $category_model = new ServiceCategoryModel();

        // Process rows
        while (($data = fgetcsv($handle)) !== false) {
            // Check if row has enough columns
            if (count($data) < 5) {
                $skipped++;
                continue;
            }

            // Get data
            $name = trim($data[0]);
            $category_name = trim($data[1]);
            $description = trim($data[2]);
            $duration = trim($data[3]);
            $price = trim($data[4]);
            $status = isset($data[5]) ? trim($data[5]) : 'active';

            // Validate data
            if (empty($name) || empty($category_name) || empty($duration) || empty($price)) {
                $skipped++;
                continue;
            }

            // Find or create category
            $category = $category_model->findBy('name', $category_name);

            if (!$category) {
                // Create new category
                $category_id = $category_model->create([
                    'name' => $category_name,
                    'description' => ''
                ]);
            } else {
                $category_id = $category[0]['id'];
            }

            // Prepare service data
            $service_data = [
                'category_id' => $category_id,
                'name' => $name,
                'description' => $description,
                'duration' => $duration,
                'price' => $price,
                'status' => $status
            ];

            // Check if service with the same name and category already exists
            $existing_service = $service_model->findByNameAndCategory($name, $category_id);

            if ($existing_service) {
                // Update existing service (same name, same category)
                $result = $service_model->update($existing_service['id'], $service_data);

                if ($result) {
                    $updated++;
                } else {
                    $skipped++;
                }
            } else {
                // Create new service (either completely new or same name but different category)
                $service_id = $service_model->create($service_data);

                if ($service_id) {
                    $imported++;
                } else {
                    $skipped++;
                }
            }
        }

        // Close file
        fclose($handle);

        // Show result
        flash('success', "Imported $imported services. Updated $updated services. Skipped $skipped rows.");
        $this->redirect(base_url('services'));
    }
}

