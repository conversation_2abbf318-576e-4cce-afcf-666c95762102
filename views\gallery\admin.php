<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-images"></i> Gallery Management</h1>
        <div>
            <a href="<?= base_url('gallery/categories') ?>" class="btn btn-outline-primary me-2">
                <i class="fas fa-tags"></i> Manage Categories
            </a>
            <a href="<?= base_url('gallery/add') ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add New Image
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-<?= $_SESSION['flash_type'] ?> alert-dismissible fade show" role="alert">
            <?= $_SESSION['flash_message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['flash_message'], $_SESSION['flash_type']); ?>
    <?php endif; ?>

    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0">Gallery Images</h5>
        </div>
        <div class="card-body">
            <?php if (empty($images)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No gallery images found. Click the "Add New Image" button to add your first image.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Title</th>
                                <th>Category</th>
                                <th>Status</th>
                                <th>Created By</th>
                                <th>Created At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($images as $image): ?>
                                <tr>
                                    <td>
                                        <img src="<?= base_url($image['image_path']) ?>" alt="<?= $image['title'] ?>" class="img-thumbnail" style="max-height: 50px;">
                                    </td>
                                    <td><?= $image['title'] ?></td>
                                    <td>
                                        <span class="badge bg-primary">
                                            <?= $image['category_name'] ?? 'Uncategorized' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($image['status'] == 'active'): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= $image['created_by_name'] ?? 'System' ?></td>
                                    <td><?= format_date($image['created_at']) ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="<?= base_url('gallery/edit/' . $image['id']) ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?= base_url('gallery/delete/' . $image['id']) ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this image?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
