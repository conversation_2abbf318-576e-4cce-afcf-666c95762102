<?php
/**
 * Authentication Controller
 */
class AuthController extends Controller {
    /**
     * Display login form
     */
    public function loginForm() {
        // If already logged in, redirect to dashboard
        if (is_logged_in()) {
            if (has_role(['admin', 'manager', 'staff'])) {
                $this->redirect(base_url('dashboard'));
            } else {
                $this->redirect(base_url());
            }
        }

        $this->render('auth/login');
    }

    /**
     * Process login
     */
    public function login() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('login'));
        }

        // Get form data
        $email = input('email');
        $password = input('password');
        $remember = isset($_POST['remember']);

        // Validate form data
        if (empty($email) || empty($password)) {
            flash('error', 'Please enter email and password');
            $this->redirect(base_url('login'));
        }

        // Check if user exists
        $user_model = new UserModel();
        $user = $user_model->findByEmail($email);

        if (!$user) {
            flash('error', 'Invalid email or password');
            $this->redirect(base_url('login'));
        }

        // Verify password
        if (!password_verify($password, $user['password'])) {
            flash('error', 'Invalid email or password');
            $this->redirect(base_url('login'));
        }

        // Check if user is active
        if ($user['status'] != 'active') {
            flash('error', 'Your account is inactive. Please contact administrator.');
            $this->redirect(base_url('login'));
        }

        // Set session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['profile_picture'] = $user['profile_picture'];

        // Set remember me cookie
        if ($remember) {
            $token = random_string(32);
            $expires = time() + (86400 * 30); // 30 days

            // Save token in database
            $user_model->saveRememberToken($user['id'], $token, $expires);

            // Set cookie
            setcookie('remember_token', $token, $expires, '/');
        }

        // Redirect based on role
        flash('success', 'Login successful');

        if (has_role(['admin', 'manager', 'staff'])) {
            $this->redirect(base_url('dashboard'));
        } else {
            $this->redirect(base_url());
        }
    }

    /**
     * Logout
     */
    public function logout() {
        // Clear session
        session_unset();
        session_destroy();

        // Clear remember me cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }

        flash('success', 'Logout successful');
        $this->redirect(base_url('login'));
    }

    /**
     * Display registration form
     */
    public function registerForm() {
        // If already logged in, redirect to dashboard
        if (is_logged_in()) {
            if (has_role(['admin', 'manager', 'staff'])) {
                $this->redirect(base_url('dashboard'));
            } else {
                $this->redirect(base_url());
            }
        }

        $this->render('auth/register');
    }

    /**
     * Process registration
     */
    public function register() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('register'));
        }

        // Get form data
        $name = input('name');
        $email = input('email');
        $password = input('password');
        $confirm_password = input('confirm_password');
        $phone = input('phone');
        $address = input('address');
        $gender = input('gender');
        $date_of_birth = input('date_of_birth');

        // Validate form data
        if (empty($name) || empty($email) || empty($password) || empty($confirm_password) || empty($phone) || empty($gender)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('register'));
        }

        if ($password != $confirm_password) {
            flash('error', 'Passwords do not match');
            $this->redirect(base_url('register'));
        }

        if (strlen($password) < 6) {
            flash('error', 'Password must be at least 6 characters');
            $this->redirect(base_url('register'));
        }

        // Check if email already exists
        $user_model = new UserModel();
        if ($user_model->emailExists($email)) {
            flash('error', 'Email already exists');
            $this->redirect(base_url('register'));
        }

        // Create user
        $user_data = [
            'name' => $name,
            'email' => $email,
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'role' => 'customer',
            'status' => 'active'
        ];

        $user_id = $user_model->create($user_data);

        if (!$user_id) {
            flash('error', 'Registration failed');
            $this->redirect(base_url('register'));
        }

        // Create customer profile
        $customer_model = new CustomerModel();
        $customer_data = [
            'user_id' => $user_id,
            'phone' => $phone,
            'address' => $address,
            'gender' => $gender,
            'date_of_birth' => $date_of_birth
        ];

        $customer_id = $customer_model->create($customer_data);

        if (!$customer_id) {
            // Rollback user creation
            $user_model->delete($user_id);

            flash('error', 'Registration failed');
            $this->redirect(base_url('register'));
        }

        // Set session
        $_SESSION['user_id'] = $user_id;
        $_SESSION['user_name'] = $name;
        $_SESSION['user_email'] = $email;
        $_SESSION['user_role'] = 'customer';

        flash('success', 'Registration successful');
        $this->redirect(base_url());
    }

    /**
     * Display forgot password form
     */
    public function forgotPasswordForm() {
        $this->render('auth/forgot_password');
    }

    /**
     * Process forgot password
     */
    public function forgotPassword() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('forgot-password'));
        }

        // Get form data
        $email = input('email');

        // Validate form data
        if (empty($email)) {
            flash('error', 'Please enter email');
            $this->redirect(base_url('forgot-password'));
        }

        // Check if user exists
        $user_model = new UserModel();
        $user = $user_model->findByEmail($email);

        if (!$user) {
            flash('error', 'Email not found');
            $this->redirect(base_url('forgot-password'));
        }

        // Generate reset token
        $token = random_string(32);
        $expires = time() + 3600; // 1 hour

        // Save token in database
        $user_model->saveResetToken($user['id'], $token, $expires);

        // Send reset email
        $reset_link = base_url('reset-password/' . $token);
        $subject = 'Reset Password';
        $message = "Click the link below to reset your password:\n\n$reset_link\n\nThis link will expire in 1 hour.";

        // For now, just display the link (in a real app, you would send an email)
        flash('info', 'Password reset link: ' . $reset_link);
        $this->redirect(base_url('login'));
    }

    /**
     * Display reset password form
     */
    public function resetPasswordForm($token) {
        // Check if token is valid
        $user_model = new UserModel();
        $user = $user_model->findByResetToken($token);

        if (!$user) {
            flash('error', 'Invalid or expired token');
            $this->redirect(base_url('login'));
        }

        $this->render('auth/reset_password', ['token' => $token]);
    }

    /**
     * Process reset password
     */
    public function resetPassword() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('login'));
        }

        // Get form data
        $token = input('token');
        $password = input('password');
        $confirm_password = input('confirm_password');

        // Validate form data
        if (empty($token) || empty($password) || empty($confirm_password)) {
            flash('error', 'Please fill all fields');
            $this->redirect(base_url('reset-password/' . $token));
        }

        if ($password != $confirm_password) {
            flash('error', 'Passwords do not match');
            $this->redirect(base_url('reset-password/' . $token));
        }

        if (strlen($password) < 6) {
            flash('error', 'Password must be at least 6 characters');
            $this->redirect(base_url('reset-password/' . $token));
        }

        // Check if token is valid
        $user_model = new UserModel();
        $user = $user_model->findByResetToken($token);

        if (!$user) {
            flash('error', 'Invalid or expired token');
            $this->redirect(base_url('login'));
        }

        // Update password
        $user_model->updatePassword($user['id'], password_hash($password, PASSWORD_DEFAULT));

        // Clear reset token
        $user_model->clearResetToken($user['id']);

        flash('success', 'Password reset successful. Please login with your new password.');
        $this->redirect(base_url('login'));
    }
}
