<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt #<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?></title>
    <style>
        @page {
            size: 72mm auto; /* 2.5 inches = 72mm */
            margin: 0;
        }
        body {
            font-family: 'Courier New', monospace;
            font-size: 10px;
            line-height: 1.2;
            margin: 0;
            padding: 5px;
            width: 72mm; /* 2.5 inches */
            max-width: 72mm;
            color: #000;
            background-color: #fff;
        }
        .receipt {
            width: 100%;
        }
        .header {
            text-align: center;
            margin-bottom: 10px;
            border-bottom: 1px dashed #000;
            padding-bottom: 5px;
        }
        .salon-name {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 3px;
        }
        .salon-info {
            font-size: 9px;
            margin-bottom: 3px;
        }
        .invoice-title {
            font-size: 12px;
            font-weight: bold;
            margin: 5px 0;
        }
        .customer-info {
            margin-bottom: 10px;
            border-bottom: 1px dashed #000;
            padding-bottom: 5px;
        }
        .section-title {
            font-weight: bold;
            margin: 5px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
        }
        th, td {
            text-align: left;
            padding: 2px 0;
            font-size: 9px;
        }
        .right {
            text-align: right;
        }
        .center {
            text-align: center;
        }
        .item-name {
            width: 50%;
        }
        .item-price, .item-qty, .item-total {
            width: 16%;
        }
        .totals {
            margin-top: 5px;
            border-top: 1px dashed #000;
            padding-top: 5px;
        }
        .total-row {
            font-weight: bold;
            border-top: 1px dashed #000;
            margin-top: 3px;
            padding-top: 3px;
        }
        .footer {
            margin-top: 10px;
            text-align: center;
            border-top: 1px dashed #000;
            padding-top: 5px;
            font-size: 9px;
        }
        .no-print {
            display: none;
        }
        .membership {
            font-weight: bold;
            font-style: italic;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                width: 72mm;
                margin: 0;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="no-print" style="margin: 20px; text-align: center;">
        <button style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;" onclick="window.print()">Print Receipt</button>
        <button style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;" onclick="window.close()">Close</button>
    </div>

    <div class="receipt">
        <div class="header">
            <div class="salon-name"><?= $settings['salon_name'] ?? 'Vishal\'s makeover' ?></div>
            <div class="salon-info"><?= $settings['salon_address'] ?? 'Sadar Bazar' ?></div>
            <div class="salon-info">Phone: <?= $settings['salon_phone'] ?? '+918573061818' ?></div>
            <div class="salon-info">Email: <?= $settings['salon_email'] ?? '<EMAIL>' ?></div>
        </div>

        <div class="invoice-title center">RECEIPT #<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?></div>
        <div class="center"><?= format_date($invoice['invoice_date']) ?> <?= date('h:i A', strtotime($invoice['created_at'])) ?></div>

        <div class="customer-info">
            <div class="section-title">Customer:</div>
            <div><?= $invoice['customer_name'] ?></div>
            <div><?= $invoice['customer_phone'] ?></div>

            <?php
            // Check if customer has an active membership
            $membership_model = new MembershipModel();
            $membership = $membership_model->getActiveMembershipByCustomerId($invoice['customer_id']);
            if ($membership):
            ?>
            <div class="membership"><?= $membership['membership_name'] ?> MEMBERSHIP</div>
            <?php endif; ?>
        </div>

        <?php if (!empty($invoice['services'])): ?>
            <div class="section-title">Services:</div>
            <table>
                <tr>
                    <th class="item-name">Item</th>
                    <th class="item-price right">Price</th>
                    <th class="item-qty right">Qty</th>
                    <th class="item-total right">Total</th>
                </tr>
                <?php foreach ($invoice['services'] as $service): ?>
                    <tr>
                        <td class="item-name">
                            <?= $service['service_name'] ?>
                            <?php if (!empty($service['staff_name'])): ?>
                                <br><small>Staff: <?= $service['staff_name'] ?></small>
                            <?php endif; ?>
                        </td>
                        <td class="item-price right"><?= format_currency(isset($service['price']) ? $service['price'] : ($service['unit_price'] ?? 0)) ?></td>
                        <td class="item-qty right"><?= $service['quantity'] ?></td>
                        <td class="item-total right"><?= format_currency(isset($service['price']) ? $service['price'] * $service['quantity'] : ($service['unit_price'] ?? 0) * $service['quantity']) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        <?php endif; ?>

        <?php if (!empty($invoice['products'])): ?>
            <div class="section-title">Products:</div>
            <table>
                <tr>
                    <th class="item-name">Item</th>
                    <th class="item-price right">Price</th>
                    <th class="item-qty right">Qty</th>
                    <th class="item-total right">Total</th>
                </tr>
                <?php foreach ($invoice['products'] as $product): ?>
                    <tr>
                        <td class="item-name"><?= $product['product_name'] ?></td>
                        <td class="item-price right"><?= format_currency(isset($product['price']) ? $product['price'] : ($product['unit_price'] ?? 0)) ?></td>
                        <td class="item-qty right"><?= $product['quantity'] ?></td>
                        <td class="item-total right"><?= format_currency(isset($product['price']) ? $product['price'] * $product['quantity'] : ($product['unit_price'] ?? 0) * $product['quantity']) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        <?php endif; ?>

        <div class="totals">
            <table>
                <tr>
                    <td>Subtotal:</td>
                    <td class="right"><?= format_currency($invoice['subtotal']) ?></td>
                </tr>

                <?php if ($invoice['discount_amount'] > 0): ?>
                    <?php
                        // Calculate discount percentage
                        $discount_percentage = ($invoice['discount_amount'] / $invoice['subtotal']) * 100;
                    ?>
                    <tr>
                        <td>Discount (<?= number_format($discount_percentage, 2) ?>%):</td>
                        <td class="right">-<?= format_currency($invoice['discount_amount']) ?></td>
                    </tr>
                <?php endif; ?>

                <?php if ($invoice['tax_amount'] > 0): ?>
                    <tr>
                        <td>Tax:</td>
                        <td class="right"><?= format_currency($invoice['tax_amount']) ?></td>
                    </tr>
                <?php endif; ?>

                <tr class="total-row">
                    <td>Total:</td>
                    <td class="right"><?= format_currency($invoice['total_amount']) ?></td>
                </tr>

                <tr>
                    <td>Amount Paid:</td>
                    <td class="right"><?= format_currency($invoice['payment_amount']) ?></td>
                </tr>

                <?php if ($invoice['payment_amount'] < $invoice['total_amount']): ?>
                    <tr>
                        <td>Balance Due:</td>
                        <td class="right"><?= format_currency($invoice['total_amount'] - $invoice['payment_amount']) ?></td>
                    </tr>
                <?php endif; ?>

                <tr>
                    <td>Payment Method:</td>
                    <td class="right"><?= ucfirst(str_replace('_', ' ', $invoice['payment_method'])) ?></td>
                </tr>
            </table>
        </div>

        <?php if (!empty($invoice['notes'])): ?>
            <div class="section-title">Notes:</div>
            <div><?= nl2br($invoice['notes']) ?></div>
        <?php endif; ?>

        <div class="footer">
            <p>Thank you for your business!</p>
            <p>Visit us again soon!</p>
            <?php if ($settings['salon_website'] ?? false): ?>
            <p><?= $settings['salon_website'] ?></p>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            // Delay printing to ensure everything is loaded
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>
