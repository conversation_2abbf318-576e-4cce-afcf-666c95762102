<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-edit"></i> Adjust Wallet Balance</h1>
        <div>
            <a href="<?= base_url('wallet/view/' . $customer['id']) ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Wallet
            </a>
        </div>
    </div>

    <!-- Customer Info & Current Balance -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-user"></i> Customer Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-4"><strong>Name:</strong></div>
                        <div class="col-sm-8"><?= $customer['name'] ?></div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-4"><strong>Email:</strong></div>
                        <div class="col-sm-8"><?= $customer['email'] ?></div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-4"><strong>Phone:</strong></div>
                        <div class="col-sm-8"><?= $customer['phone'] ?></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-wallet"></i> Current Balance</h5>
                </div>
                <div class="card-body text-center">
                    <h1 class="display-4 text-<?= $wallet && $wallet['balance'] > 0 ? 'success' : 'muted' ?>">
                        <?= format_currency($wallet ? $wallet['balance'] : 0) ?>
                    </h1>
                    <p class="text-muted">Current Wallet Balance</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Warning Alert -->
    <div class="alert alert-warning" role="alert">
        <h5 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Important Notice</h5>
        <p>Balance adjustments should only be made for corrections or special circumstances. All adjustments are logged and tracked for audit purposes.</p>
        <hr>
        <p class="mb-0">Please provide a clear reason for this adjustment in the description field below.</p>
    </div>

    <!-- Adjustment Form -->
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-edit"></i> Balance Adjustment</h5>
        </div>
        <div class="card-body">
            <form action="<?= base_url('wallet/store-adjustment/' . $customer['id']) ?>" method="post" id="adjustment-form">
                <div class="row">
                    <!-- Adjustment Type -->
                    <div class="col-md-6 mb-3">
                        <label for="adjustment_type" class="form-label">Adjustment Type <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-exchange-alt"></i></span>
                            <select class="form-select" id="adjustment_type" name="adjustment_type" required>
                                <option value="">Select adjustment type...</option>
                                <option value="add">Add Money (Increase Balance)</option>
                                <option value="subtract">Subtract Money (Decrease Balance)</option>
                            </select>
                        </div>
                    </div>

                    <!-- Amount -->
                    <div class="col-md-6 mb-3">
                        <label for="amount" class="form-label">Amount <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-rupee-sign"></i></span>
                            <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0.01" required placeholder="0.00">
                        </div>
                    </div>

                    <!-- Reason -->
                    <div class="col-md-12 mb-3">
                        <label for="reason" class="form-label">Reason for Adjustment <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reason" name="reason" rows="4" required placeholder="Please provide a detailed reason for this balance adjustment..."></textarea>
                        <div class="form-text">This reason will be recorded in the transaction history for audit purposes.</div>
                    </div>

                    <!-- Preview Section -->
                    <div class="col-md-12 mb-3">
                        <div class="card bg-light" id="preview-section" style="display: none;">
                            <div class="card-body">
                                <h6 class="card-title"><i class="fas fa-eye"></i> Adjustment Preview</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>Current Balance:</strong><br>
                                        <span class="text-muted"><?= format_currency($wallet ? $wallet['balance'] : 0) ?></span>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Adjustment:</strong><br>
                                        <span id="adjustment-preview" class="fw-bold"></span>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>New Balance:</strong><br>
                                        <span id="new-balance-preview" class="fw-bold"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-md-12">
                        <div class="d-flex justify-content-end">
                            <a href="<?= base_url('wallet/view/' . $customer['id']) ?>" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-warning" id="submit-btn" disabled>
                                <i class="fas fa-edit"></i> Apply Adjustment
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const adjustmentTypeSelect = document.getElementById('adjustment_type');
    const amountInput = document.getElementById('amount');
    const reasonInput = document.getElementById('reason');
    const previewSection = document.getElementById('preview-section');
    const adjustmentPreview = document.getElementById('adjustment-preview');
    const newBalancePreview = document.getElementById('new-balance-preview');
    const submitBtn = document.getElementById('submit-btn');
    
    const currentBalance = <?= $wallet ? $wallet['balance'] : 0 ?>;

    function updatePreview() {
        const adjustmentType = adjustmentTypeSelect.value;
        const amount = parseFloat(amountInput.value) || 0;
        
        if (adjustmentType && amount > 0) {
            previewSection.style.display = 'block';
            
            let newBalance;
            let adjustmentText;
            let adjustmentClass;
            
            if (adjustmentType === 'add') {
                newBalance = currentBalance + amount;
                adjustmentText = `+₹${amount.toFixed(2)}`;
                adjustmentClass = 'text-success';
            } else {
                newBalance = currentBalance - amount;
                adjustmentText = `-₹${amount.toFixed(2)}`;
                adjustmentClass = 'text-danger';
            }
            
            adjustmentPreview.textContent = adjustmentText;
            adjustmentPreview.className = `fw-bold ${adjustmentClass}`;
            
            newBalancePreview.textContent = `₹${newBalance.toFixed(2)}`;
            newBalancePreview.className = `fw-bold ${newBalance >= 0 ? 'text-success' : 'text-danger'}`;
            
            // Show warning if balance will go negative
            if (newBalance < 0) {
                if (!document.getElementById('negative-warning')) {
                    const warning = document.createElement('div');
                    warning.id = 'negative-warning';
                    warning.className = 'alert alert-danger mt-2';
                    warning.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Warning: This adjustment will result in a negative balance!';
                    previewSection.appendChild(warning);
                }
            } else {
                const existingWarning = document.getElementById('negative-warning');
                if (existingWarning) {
                    existingWarning.remove();
                }
            }
        } else {
            previewSection.style.display = 'none';
        }
        
        // Enable/disable submit button
        const isValid = adjustmentType && amount > 0 && reasonInput.value.trim().length > 0;
        submitBtn.disabled = !isValid;
    }

    // Event listeners
    adjustmentTypeSelect.addEventListener('change', updatePreview);
    amountInput.addEventListener('input', updatePreview);
    reasonInput.addEventListener('input', updatePreview);

    // Form validation
    document.getElementById('adjustment-form').addEventListener('submit', function(e) {
        const adjustmentType = adjustmentTypeSelect.value;
        const amount = parseFloat(amountInput.value) || 0;
        const reason = reasonInput.value.trim();

        if (!adjustmentType) {
            alert('Please select an adjustment type');
            e.preventDefault();
            return false;
        }

        if (amount <= 0) {
            alert('Please enter a valid amount');
            e.preventDefault();
            return false;
        }

        if (!reason) {
            alert('Please provide a reason for the adjustment');
            e.preventDefault();
            return false;
        }

        // Confirm the adjustment
        const newBalance = adjustmentType === 'add' ? currentBalance + amount : currentBalance - amount;
        const confirmMessage = `Are you sure you want to ${adjustmentType} ₹${amount.toFixed(2)} ${adjustmentType === 'add' ? 'to' : 'from'} this customer's wallet?\n\nNew balance will be: ₹${newBalance.toFixed(2)}`;
        
        if (!confirm(confirmMessage)) {
            e.preventDefault();
            return false;
        }

        return true;
    });
});
</script>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.display-4 {
    font-weight: 700;
}

.form-control:focus, .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#preview-section {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
