<?php
/**
 * Gallery Category Model
 *
 * Handles database operations for gallery categories
 */
class GalleryCategoryModel extends Model {

    protected $table = 'gallery_categories';

    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Get all gallery categories
     *
     * @param string $status Filter by status (active, inactive, all)
     * @return array Gallery categories
     */
    public function getAll($status = 'all') {
        $query = "SELECT * FROM {$this->table}";

        if ($status != 'all') {
            $query .= " WHERE status = :status";
        }

        $query .= " ORDER BY name ASC";

        $stmt = $this->db->prepare($query);

        if ($status != 'all') {
            $stmt->bindValue(':status', $status);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get active gallery categories for frontend
     *
     * @return array Gallery categories
     */
    public function getActive() {
        $query = "SELECT * FROM {$this->table} WHERE status = 'active' ORDER BY name ASC";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Add a new gallery category
     *
     * @param array $data Gallery category data
     * @return int|bool ID of the inserted category or false on failure
     */
    public function add($data) {
        return $this->create($data);
    }

    /**
     * Update a gallery category
     *
     * @param int $id Gallery category ID
     * @param array $data Gallery category data
     * @return bool Success or failure
     */
    public function updateCategory($id, $data) {
        return $this->update($id, $data);
    }

    /**
     * Delete a gallery category
     *
     * @param int $id Gallery category ID
     * @return bool Success or failure
     */
    public function deleteCategory($id) {
        return $this->delete($id);
    }

    /**
     * Check if a category has images
     *
     * @param int $id Category ID
     * @return bool True if category has images, false otherwise
     */
    public function hasImages($id) {
        $query = "SELECT COUNT(*) as count FROM gallery_images WHERE category = :category";
        $stmt = $this->db->prepare($query);
        $stmt->bindValue(':category', $id, PDO::PARAM_INT);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result['count'] > 0;
    }
}
