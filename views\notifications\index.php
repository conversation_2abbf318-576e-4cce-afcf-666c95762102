<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-bell"></i> Notifications</h1>
        
        <form action="<?= base_url('notifications/mark-all-as-read') ?>" method="post" class="d-inline">
            <button type="submit" class="btn btn-outline-primary">
                <i class="fas fa-check-double me-1"></i> Mark All as Read
            </button>
        </form>
    </div>
    
    <div class="card shadow-sm">
        <div class="card-body">
            <?php if (empty($notifications)): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-bell-slash fa-3x text-muted"></i>
                    </div>
                    <h5 class="text-muted">No notifications</h5>
                    <p class="text-muted">You don't have any notifications at the moment.</p>
                </div>
            <?php else: ?>
                <div class="list-group notification-list">
                    <?php foreach ($notifications as $notification): ?>
                        <div class="list-group-item list-group-item-action <?= $notification['is_read'] ? '' : 'unread' ?>">
                            <div class="d-flex w-100 justify-content-between align-items-center">
                                <div class="notification-content">
                                    <div class="d-flex align-items-center">
                                        <?php if ($notification['type'] == 'appointment'): ?>
                                            <div class="notification-icon bg-primary">
                                                <i class="fas fa-calendar-check"></i>
                                            </div>
                                        <?php elseif ($notification['type'] == 'inventory'): ?>
                                            <div class="notification-icon bg-warning">
                                                <i class="fas fa-box"></i>
                                            </div>
                                        <?php elseif ($notification['type'] == 'staff'): ?>
                                            <div class="notification-icon bg-info">
                                                <i class="fas fa-user-tie"></i>
                                            </div>
                                        <?php elseif ($notification['type'] == 'customer'): ?>
                                            <div class="notification-icon bg-success">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        <?php else: ?>
                                            <div class="notification-icon bg-secondary">
                                                <i class="fas fa-info-circle"></i>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="ms-3">
                                            <p class="mb-1"><?= $notification['message'] ?></p>
                                            <small class="text-muted"><?= time_elapsed_string($notification['created_at']) ?></small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="notification-actions">
                                    <?php if (!$notification['is_read']): ?>
                                        <form action="<?= base_url('notifications/mark-as-read') ?>" method="post" class="d-inline">
                                            <input type="hidden" name="notification_id" value="<?= $notification['id'] ?>">
                                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($notification['link'])): ?>
                                        <a href="<?= base_url($notification['link']) ?>" class="btn btn-sm btn-primary ms-2">
                                            <i class="fas fa-arrow-right"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.notification-list .list-group-item.unread {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border-left: 3px solid var(--bs-primary);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.notification-content {
    flex: 1;
}

.notification-actions {
    white-space: nowrap;
}
</style>
