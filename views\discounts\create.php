<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-plus-circle"></i> Create Discount Plan</h1>
        <div>
            <a href="<?= base_url('discounts') ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Discount Plans
            </a>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('discounts/store') ?>" method="post">
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-12 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-info-circle me-2"></i>Basic Information</h5>
                    </div>

                    <!-- Membership Name -->
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">Membership Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>

                    <!-- Status -->
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>

                    <!-- Description -->
                    <div class="col-md-12 mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>

                    <!-- Pricing & Duration -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-tag me-2"></i>Pricing & Duration</h5>
                    </div>

                    <!-- Price -->
                    <div class="col-md-4 mb-3">
                        <label for="price" class="form-label">Price <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                            <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                        </div>
                    </div>

                    <!-- Duration -->
                    <div class="col-md-4 mb-3">
                        <label for="duration" class="form-label">Duration <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="duration" name="duration" min="1" required>
                        <div class="form-text">Will be converted to days based on unit</div>
                    </div>

                    <!-- Duration Unit -->
                    <div class="col-md-4 mb-3">
                        <label for="duration_unit" class="form-label">Duration Unit</label>
                        <select class="form-select" id="duration_unit" name="duration_unit">
                            <option value="days">Days</option>
                            <option value="months" selected>Months</option>
                            <option value="years">Years</option>
                        </select>
                        <div class="form-text">Will be converted to days when saved</div>
                    </div>

                    <!-- Benefits & Discounts -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-gift me-2"></i>Benefits & Discounts</h5>
                    </div>

                    <!-- Discount Percentage -->
                    <div class="col-md-6 mb-3">
                        <label for="discount_percentage" class="form-label">Discount Percentage</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="discount_percentage" name="discount_percentage" min="0" max="100" value="0">
                            <span class="input-group-text">%</span>
                        </div>
                        <div class="form-text">Discount applied to services and products</div>
                    </div>

                    <!-- Benefits -->
                    <div class="col-md-12 mb-3">
                        <label class="form-label">Service Benefits</label>
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($services as $index => $service): ?>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="service_<?= $service['id'] ?>" name="benefits[]" value="<?= $service['id'] ?>" disabled>
                                                <label class="form-check-label" for="service_<?= $service['id'] ?>">
                                                    <?= $service['name'] ?> (<?= format_currency($service['price']) ?>)
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <div class="form-text">Service benefits are not supported in the current database structure. Use the discount percentage instead.</div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-md-12 mt-4 text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-save me-2"></i> Create Membership
                        </button>
                        <a href="<?= base_url('memberships') ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
