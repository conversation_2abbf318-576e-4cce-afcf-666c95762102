<?php
/**
 * Admin Setup Script
 *
 * This script creates or updates the default admin user (ID 1)
 */

// Set environment manually for CLI
define('ENVIRONMENT', 'development');
define('BASE_PATH', realpath(dirname(__FILE__)));

// Include config files
require_once 'config/config.php';
require_once 'config/database.php';

// Create direct database connection for CLI script
try {
    $db = new PDO(
        "mysql:host=localhost;dbname=salon_db",
        "root",
        ""
    );
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $db->exec("set names utf8");
} catch(PDOException $e) {
    echo "Connection error: " . $e->getMessage();
    exit;
}

// Create admin user data
$admin_data = [
    'name' => 'Tarun Kaushal',
    'email' => '<EMAIL>',
    'password' => password_hash('admin123', PASSWORD_DEFAULT),
    'role' => 'admin',
    'status' => 'active',
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
];

try {
    // Check if user with ID 1 exists
    $stmt = $db->prepare("SELECT * FROM users WHERE id = 1");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user) {
        // Update existing user
        $stmt = $db->prepare("UPDATE users SET
            name = :name,
            email = :email,
            password = :password,
            role = :role,
            status = :status,
            updated_at = :updated_at
            WHERE id = 1");

        $stmt->bindParam(':name', $admin_data['name']);
        $stmt->bindParam(':email', $admin_data['email']);
        $stmt->bindParam(':password', $admin_data['password']);
        $stmt->bindParam(':role', $admin_data['role']);
        $stmt->bindParam(':status', $admin_data['status']);
        $stmt->bindParam(':updated_at', $admin_data['updated_at']);

        $stmt->execute();

        echo "Default admin user updated successfully!\n";
    } else {
        // Insert new user with ID 1
        $stmt = $db->prepare("INSERT INTO users (id, name, email, password, role, status, created_at, updated_at)
            VALUES (1, :name, :email, :password, :role, :status, :created_at, :updated_at)");

        $stmt->bindParam(':name', $admin_data['name']);
        $stmt->bindParam(':email', $admin_data['email']);
        $stmt->bindParam(':password', $admin_data['password']);
        $stmt->bindParam(':role', $admin_data['role']);
        $stmt->bindParam(':status', $admin_data['status']);
        $stmt->bindParam(':created_at', $admin_data['created_at']);
        $stmt->bindParam(':updated_at', $admin_data['updated_at']);

        $stmt->execute();

        echo "Default admin user created successfully!\n";
    }

    // Check if staff record exists for this user
    $stmt = $db->prepare("SELECT * FROM staff WHERE user_id = 1");
    $stmt->execute();
    $staff = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$staff) {
        // Create staff record for admin
        $stmt = $db->prepare("INSERT INTO staff (user_id, position, salary, commission_rate, joining_date, created_at, updated_at)
            VALUES (1, 'Default Staff', 0, 0, :joining_date, :created_at, :updated_at)");

        $joining_date = date('Y-m-d');
        $created_at = date('Y-m-d H:i:s');
        $updated_at = date('Y-m-d H:i:s');

        $stmt->bindParam(':joining_date', $joining_date);
        $stmt->bindParam(':created_at', $created_at);
        $stmt->bindParam(':updated_at', $updated_at);

        $stmt->execute();

        echo "Default staff record created for admin user!\n";
    }

    echo "Setup completed successfully!";
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
