<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-cogs"></i> Settings</h1>
    </div>

    <div class="row">
        <!-- Settings Navigation -->
        <div class="col-md-3 mb-4">
            <?php include 'partials/settings_nav.php'; ?>
        </div>
        
        <!-- Settings Content -->
        <div class="col-md-9">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Email Settings</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('settings/update-email') ?>" method="post">
                        <div class="row">
                            <!-- SMTP Host -->
                            <div class="col-md-6 mb-3">
                                <label for="smtp_host" class="form-label">SMTP Host</label>
                                <input type="text" class="form-control" id="smtp_host" name="smtp_host" value="<?= $settings['smtp_host'] ?? '' ?>">
                            </div>
                            
                            <!-- SMTP Port -->
                            <div class="col-md-6 mb-3">
                                <label for="smtp_port" class="form-label">SMTP Port</label>
                                <input type="text" class="form-control" id="smtp_port" name="smtp_port" value="<?= $settings['smtp_port'] ?? '' ?>">
                            </div>
                            
                            <!-- SMTP Username -->
                            <div class="col-md-6 mb-3">
                                <label for="smtp_username" class="form-label">SMTP Username</label>
                                <input type="text" class="form-control" id="smtp_username" name="smtp_username" value="<?= $settings['smtp_username'] ?? '' ?>">
                            </div>
                            
                            <!-- SMTP Password -->
                            <div class="col-md-6 mb-3">
                                <label for="smtp_password" class="form-label">SMTP Password</label>
                                <input type="password" class="form-control" id="smtp_password" name="smtp_password" placeholder="Leave blank to keep current password">
                                <div class="form-text">Leave blank to keep current password</div>
                            </div>
                            
                            <!-- SMTP Encryption -->
                            <div class="col-md-6 mb-3">
                                <label for="smtp_encryption" class="form-label">SMTP Encryption</label>
                                <select class="form-select" id="smtp_encryption" name="smtp_encryption">
                                    <option value="tls" <?= ($settings['smtp_encryption'] ?? 'tls') == 'tls' ? 'selected' : '' ?>>TLS</option>
                                    <option value="ssl" <?= ($settings['smtp_encryption'] ?? 'tls') == 'ssl' ? 'selected' : '' ?>>SSL</option>
                                    <option value="none" <?= ($settings['smtp_encryption'] ?? 'tls') == 'none' ? 'selected' : '' ?>>None</option>
                                </select>
                            </div>
                            
                            <!-- Test Email -->
                            <div class="col-md-12 mb-3">
                                <div class="alert alert-info">
                                    <h6 class="alert-heading">Email Configuration</h6>
                                    <p class="mb-0">Configure your SMTP settings to enable email notifications for appointments, invoices, and other system emails.</p>
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="col-md-12 mt-3 text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> Save Changes
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
