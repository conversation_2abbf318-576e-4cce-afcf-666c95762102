<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-file-invoice"></i> Invoice #<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?>
        </h1>
        <div>
            <a href="<?= base_url('billing') ?>" class="btn btn-outline-primary me-2">
                <i class="fas fa-arrow-left"></i> Back to Invoices
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-cog"></i> Actions
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <?php if (is_logged_in() && has_role(['admin', 'manager', 'staff'])): ?>
                        <li>
                            <a class="dropdown-item" href="<?= base_url('billing/print/' . $invoice['id']) ?>" target="_blank">
                                <i class="fas fa-print me-2"></i> Print Invoice
                            </a>
                        </li>
                        <?php if (has_role(['admin', 'manager'])): ?>
                            <?php if ($invoice['payment_status'] != 'cancelled'): ?>
                                <li>
                                    <a class="dropdown-item" href="<?= base_url('billing/edit/' . $invoice['id']) ?>">
                                        <i class="fas fa-edit me-2"></i> Edit Invoice
                                    </a>
                                </li>
                                <?php if ($invoice['payment_status'] == 'unpaid' || $invoice['payment_status'] == 'partial'): ?>
                                    <li>
                                        <a class="dropdown-item" href="<?= base_url('billing/mark-as-paid/' . $invoice['id']) ?>" onclick="return confirm('Are you sure you want to mark this invoice as paid?')">
                                            <i class="fas fa-check me-2"></i> Mark as Paid
                                        </a>
                                    </li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-danger" href="<?= base_url('billing/cancel/' . $invoice['id']) ?>" onclick="return confirm('Are you sure you want to cancel this invoice?')">
                                        <i class="fas fa-times me-2"></i> Cancel Invoice
                                    </a>
                                </li>
                                <?php if ($invoice['payment_status'] != 'paid'): ?>
                                <li>
                                    <a class="dropdown-item text-danger" href="<?= base_url('billing/delete/' . $invoice['id']) ?>" onclick="return confirm('Are you sure you want to delete this invoice? This action cannot be undone.')">
                                        <i class="fas fa-trash me-2"></i> Delete Invoice
                                    </a>
                                </li>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Invoice Details -->
        <div class="col-md-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Invoice Details</h5>
                    <div>
                        <?php if ($invoice['payment_status'] == 'unpaid'): ?>
                            <span class="badge bg-warning">Unpaid</span>
                        <?php elseif ($invoice['payment_status'] == 'partial'): ?>
                            <span class="badge bg-info">Partial</span>
                        <?php elseif ($invoice['payment_status'] == 'paid'): ?>
                            <span class="badge bg-success">Paid</span>
                        <?php elseif ($invoice['payment_status'] == 'cancelled'): ?>
                            <span class="badge bg-danger">Cancelled</span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Customer Information</h6>
                            <div class="d-flex align-items-center mb-3">
                                <div class="avatar-circle bg-primary text-white me-3">
                                    <?= strtoupper(substr($invoice['customer_name'], 0, 1)) ?>
                                </div>
                                <div>
                                    <div class="fw-bold"><?= $invoice['customer_name'] ?></div>
                                    <div class="text-muted">
                                        <i class="fas fa-envelope me-1"></i> <?= $invoice['customer_email'] ?>
                                    </div>
                                    <div class="text-muted">
                                        <i class="fas fa-phone me-1"></i> <?= $invoice['customer_phone'] ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Invoice Information</h6>
                            <div class="mb-2">
                                <div class="text-muted small">Invoice Date</div>
                                <div><?= format_date($invoice['invoice_date']) ?></div>
                            </div>

                            <div class="mb-2">
                                <div class="text-muted small">Payment Method</div>
                                <div><?= ucfirst(str_replace('_', ' ', $invoice['payment_method'])) ?></div>
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Summary -->
                    <?php if (!empty($invoice['services'])): ?>
                        <h6 class="border-bottom pb-2 mb-3">Services</h6>
                        <div class="table-responsive mb-4">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Service</th>
                                        <th>Category</th>
                                        <th>Staff</th>
                                        <th class="text-end">Price</th>
                                        <th class="text-end">Qty</th>
                                        <th class="text-end">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($invoice['services'] as $service): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?= $service['service_name'] ?></div>
                                                <?php if (!empty($service['service_description'])): ?>
                                                    <div class="small text-muted"><?= substr($service['service_description'], 0, 50) . (strlen($service['service_description']) > 50 ? '...' : '') ?></div>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= $service['service_category_name'] ?></td>
                                            <td><?= $service['staff_name'] ?? 'N/A' ?></td>
                                            <td class="text-end"><?= format_currency(isset($service['price']) ? $service['price'] : ($service['unit_price'] ?? 0)) ?></td>
                                            <td class="text-end"><?= $service['quantity'] ?></td>
                                            <td class="text-end"><?= format_currency(isset($service['price']) ? $service['price'] * $service['quantity'] : ($service['unit_price'] ?? 0) * $service['quantity']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($invoice['products'])): ?>
                        <h6 class="border-bottom pb-2 mb-3">Products</h6>
                        <div class="table-responsive mb-4">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Category</th>
                                        <th class="text-end">Price</th>
                                        <th class="text-end">Qty</th>
                                        <th class="text-end">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($invoice['products'] as $product): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-bold"><?= $product['product_name'] ?></div>
                                                <?php if (!empty($product['product_description'])): ?>
                                                    <div class="small text-muted"><?= substr($product['product_description'], 0, 50) . (strlen($product['product_description']) > 50 ? '...' : '') ?></div>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= $product['product_category_name'] ?></td>
                                            <td class="text-end"><?= format_currency(isset($product['price']) ? $product['price'] : ($product['unit_price'] ?? 0)) ?></td>
                                            <td class="text-end"><?= $product['quantity'] ?></td>
                                            <td class="text-end"><?= format_currency(isset($product['price']) ? $product['price'] * $product['quantity'] : ($product['unit_price'] ?? 0) * $product['quantity']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($invoice['notes'])): ?>
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Notes</h6>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <?= nl2br($invoice['notes']) ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="text-muted small">Created By</div>
                                <div><?= $invoice['created_by_name'] ?? 'System' ?></div>
                            </div>
                            <div class="mb-3">
                                <div class="text-muted small">Created At</div>
                                <div><?= format_datetime($invoice['created_at']) ?></div>
                            </div>
                            <?php if ($invoice['updated_at'] != $invoice['created_at']): ?>
                                <div class="mb-3">
                                    <div class="text-muted small">Last Updated</div>
                                    <div><?= format_datetime($invoice['updated_at']) ?></div>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-2">
                                        <div>Subtotal:</div>
                                        <div class="fw-bold"><?= format_currency($invoice['subtotal']) ?></div>
                                    </div>
                                    <?php if ($invoice['discount_amount'] > 0): ?>
                                        <?php
                                            // Calculate discount percentage
                                            $discount_percentage = ($invoice['discount_amount'] / $invoice['subtotal']) * 100;
                                        ?>
                                        <div class="d-flex justify-content-between mb-2">
                                            <div>Discount (<?= number_format($discount_percentage, 2) ?>%):</div>
                                            <div class="text-danger">-<?= format_currency($invoice['discount_amount']) ?></div>
                                        </div>
                                        <?php if (isset($invoice['coupon'])): ?>
                                        <div class="d-flex justify-content-between mb-2">
                                            <div><small class="text-muted">Coupon Applied:</small></div>
                                            <div><small class="text-muted"><?= $invoice['coupon']['coupon_code'] ?>
                                                (<?= $invoice['coupon']['discount_type'] === 'percentage' ? $invoice['coupon']['discount_value'] . '%' : format_currency($invoice['coupon']['discount_value']) ?>)</small></div>
                                        </div>
                                        <?php elseif (isset($invoice['customer_id'])): ?>
                                        <?php
                                            // Check if customer has an active membership
                                            $membership_model = new MembershipModel();
                                            $membership = $membership_model->getActiveMembershipByCustomerId($invoice['customer_id']);
                                            if ($membership):
                                        ?>
                                        <div class="d-flex justify-content-between mb-2">
                                            <div><small class="text-success"><i class="fas fa-crown me-1"></i> <?= $membership['membership_name'] ?> Membership:</small></div>
                                            <div><small class="text-success"><?= $membership['service_discount'] ?>% off services</small></div>
                                        </div>
                                        <?php endif; ?>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <?php if ($invoice['tax_amount'] > 0): ?>
                                        <div class="d-flex justify-content-between mb-2">
                                            <div>Tax:</div>
                                            <div><?= format_currency($invoice['tax_amount']) ?></div>
                                        </div>
                                    <?php endif; ?>
                                    <div class="d-flex justify-content-between border-top pt-2 mt-2">
                                        <div class="fw-bold">Total:</div>
                                        <div class="fw-bold fs-5"><?= format_currency($invoice['total_amount']) ?></div>
                                    </div>
                                    <div class="d-flex justify-content-between border-top pt-2 mt-2">
                                        <div>Amount Paid:</div>
                                        <div class="text-success"><?= format_currency($invoice['payment_amount']) ?></div>
                                    </div>
                                    <?php if ($invoice['payment_amount'] < $invoice['total_amount']): ?>
                                        <div class="d-flex justify-content-between mt-2">
                                            <div>Balance Due:</div>
                                            <div class="text-danger"><?= format_currency($invoice['total_amount'] - $invoice['payment_amount']) ?></div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Information and Actions -->
        <div class="col-md-4 mb-4">
            <!-- Quick Actions -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('billing/print/' . $invoice['id']) ?>" class="btn btn-primary mb-2" target="_blank">
                            <i class="fas fa-print"></i> Print A5 Invoice
                        </a>
                        <a href="<?= base_url('billing/printA4/' . $invoice['id']) ?>" class="btn btn-secondary mb-2" target="_blank">
                            <i class="fas fa-file-alt"></i> Print A4 Invoice
                        </a>
                        <a href="<?= base_url('billing/thermalPrint/' . $invoice['id']) ?>" class="btn btn-info" target="_blank">
                            <i class="fas fa-receipt"></i> Print Thermal Receipt
                        </a>
                        <?php if (has_role('admin') && !empty($invoice['customer_phone'])): ?>
                        <?php
                        // Function to format currency for WhatsApp (safe encoding)
                        function format_currency_whatsapp($amount) {
                            $amount = is_numeric($amount) ? (float)$amount : 0;
                            return "Rs. " . number_format($amount, 2);
                        }

                        // Generate WhatsApp message
                        $salon_name = html_entity_decode(get_salon_name(), ENT_QUOTES, 'UTF-8');
                        $invoice_number = str_pad($invoice['id'], 6, '0', STR_PAD_LEFT);
                        $customer_name = html_entity_decode($invoice['customer_name'], ENT_QUOTES, 'UTF-8');
                        $invoice_date = format_date($invoice['invoice_date']);
                        $total_amount = format_currency_whatsapp($invoice['total_amount']);
                        $payment_status = ucfirst($invoice['payment_status']);

                        // Build services list
                        $services_text = "";
                        if (!empty($invoice['services'])) {
                            foreach ($invoice['services'] as $service) {
                                $price = isset($service['total_price']) ? $service['total_price'] : (isset($service['unit_price']) ? $service['unit_price'] : 0);
                                $service_name = html_entity_decode($service['service_name'], ENT_QUOTES, 'UTF-8');
                                $services_text .= "• " . $service_name . " - " . format_currency_whatsapp($price) . "\n";
                            }
                        }

                        // Build products list
                        $products_text = "";
                        if (!empty($invoice['products'])) {
                            foreach ($invoice['products'] as $product) {
                                $total_price = isset($product['total_price']) ? $product['total_price'] : 0;
                                $quantity = isset($product['quantity']) ? $product['quantity'] : 1;
                                $product_name = html_entity_decode($product['product_name'], ENT_QUOTES, 'UTF-8');
                                $products_text .= "• " . $product_name . " (Qty: " . $quantity . ") - " . format_currency_whatsapp($total_price) . "\n";
                            }
                        }

                        // Create the message (clean text format for WhatsApp)
                        $message = "*INVOICE FROM {$salon_name}*\n";
                        $message .= "================================\n\n";
                        $message .= "*Invoice #:* {$invoice_number}\n";
                        $message .= "*Customer:* {$customer_name}\n";
                        $message .= "*Date:* {$invoice_date}\n\n";

                        if (!empty($services_text)) {
                            $message .= "*SERVICES:*\n";
                            $message .= "--------------------------------\n";
                            $message .= "{$services_text}\n";
                        }

                        if (!empty($products_text)) {
                            $message .= "*PRODUCTS:*\n";
                            $message .= "--------------------------------\n";
                            $message .= "{$products_text}\n";
                        }

                        $message .= "================================\n";

                        // Add subtotal, tax, and discount information
                        $subtotal = format_currency_whatsapp($invoice['subtotal']);
                        $message .= "*Subtotal:* {$subtotal}\n";

                        // Add discount if applicable
                        if (!empty($invoice['discount_amount']) && $invoice['discount_amount'] > 0) {
                            $discount = format_currency_whatsapp($invoice['discount_amount']);
                            $message .= "*Discount:* -{$discount}\n";
                        }

                        // Add tax if applicable
                        if (!empty($invoice['tax_amount']) && $invoice['tax_amount'] > 0) {
                            $tax = format_currency_whatsapp($invoice['tax_amount']);
                            $message .= "*Tax:* {$tax}\n";
                        }

                        $message .= "--------------------------------\n";
                        $message .= "*Total Amount:* {$total_amount}\n";

                        // Add wallet payment information if applicable
                        if (isset($invoice['wallet_amount']) && $invoice['wallet_amount'] > 0) {
                            $wallet_payment = format_currency_whatsapp($invoice['wallet_amount']);
                            $message .= "*Wallet Payment:* {$wallet_payment}\n";

                            if ($invoice['payment_method'] == 'mixed') {
                                $other_payment = format_currency_whatsapp($invoice['payment_amount'] - $invoice['wallet_amount']);
                                $message .= "*Other Payment:* {$other_payment}\n";
                            }

                            // Get customer's remaining wallet balance
                            $wallet_model = new WalletModel();
                            $current_wallet = $wallet_model->getByCustomerId($invoice['customer_id']);
                            $remaining_balance = $current_wallet ? $current_wallet['balance'] : 0;
                            $remaining_wallet = format_currency_whatsapp($remaining_balance);
                            $message .= "*Remaining Wallet Balance:* {$remaining_wallet}\n";
                        }

                        $message .= "*Payment Status:* {$payment_status}\n\n";
                        $message .= "Thank you for choosing {$salon_name}!";

                        // Clean and format phone number for WhatsApp
                        $phone = preg_replace('/[^0-9]/', '', $invoice['customer_phone']); // Remove all non-numeric characters

                        // Ensure phone number starts with country code (91 for India)
                        if (strlen($phone) == 10) {
                            // If it's a 10-digit number, add India country code
                            $phone = '91' . $phone;
                        } else if (strlen($phone) == 11 && substr($phone, 0, 1) === '0') {
                            // If it starts with 0, remove it and add country code
                            $phone = '91' . substr($phone, 1);
                        } else if (strlen($phone) == 12 && substr($phone, 0, 2) !== '91') {
                            // If it's 12 digits but doesn't start with 91, assume it's correct
                            // Do nothing
                        } else if (strlen($phone) > 12) {
                            // If longer than 12, try to extract the last 10 digits and add country code
                            $phone = '91' . substr($phone, -10);
                        }

                        // URL encode the message for WhatsApp
                        $encoded_message = urlencode($message);
                        $whatsapp_url = "https://wa.me/{$phone}?text={$encoded_message}";
                        ?>
                        <a href="https://web.whatsapp.com/send?phone=<?= $phone ?>&text=<?= $encoded_message ?>" class="btn btn-success" onclick="openWhatsAppWeb(this.href); return false;">
                            <i class="fab fa-whatsapp"></i> Send via WhatsApp
                        </a>
                        <div class="mt-2 small text-muted">
                            <i class="fas fa-info-circle"></i> This will open WhatsApp Web with a pre-filled message for the customer.
                            <br><strong>Phone:</strong> <?= $phone ?>
                        </div>
                        <?php elseif (has_role('admin')): ?>
                        <button type="button" class="btn btn-outline-success" disabled>
                            <i class="fab fa-whatsapp"></i> Send via WhatsApp
                        </button>
                        <div class="mt-2 small text-muted">
                            <i class="fas fa-info-circle"></i> Customer phone number not available.
                        </div>
                        <?php endif; ?>
                        <?php if (has_role(['admin', 'manager']) && $invoice['payment_status'] != 'cancelled'): ?>
                        <a href="<?= base_url('billing/edit/' . $invoice['id']) ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-edit"></i> Edit Invoice
                        </a>
                        <?php if ($invoice['payment_status'] != 'paid'): ?>
                        <a href="<?= base_url('billing/delete/' . $invoice['id']) ?>" class="btn btn-outline-danger" onclick="return confirm('Are you sure you want to delete this invoice? This action cannot be undone.')">
                            <i class="fas fa-trash"></i> Delete Invoice
                        </a>
                        <?php endif; ?>
                        <?php endif; ?>
                        <a href="<?= base_url('customers/view/' . $invoice['customer_id']) ?>" class="btn btn-outline-primary">
                            <i class="fas fa-user"></i> View Customer Profile
                        </a>
                        <a href="<?= base_url('billing/create?customer_id=' . $invoice['customer_id']) ?>" class="btn btn-outline-primary">
                            <i class="fas fa-plus"></i> Create New Invoice
                        </a>
                    </div>
                </div>
            </div>

            <!-- Payment History -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">Payment History</h5>
                </div>
                <div class="card-body p-0">
                    <?php if ($invoice['payment_amount'] > 0): ?>
                        <div class="list-group list-group-flush">
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="fw-bold"><?= format_date($invoice['invoice_date']) ?></div>
                                        <div class="small text-muted"><?= ucfirst(str_replace('_', ' ', $invoice['payment_method'])) ?></div>
                                        <?php if (isset($invoice['wallet_amount']) && $invoice['wallet_amount'] > 0): ?>
                                            <div class="small text-info mt-1">
                                                <i class="fas fa-wallet"></i> Wallet: <?= format_currency($invoice['wallet_amount']) ?>
                                                <?php if ($invoice['payment_method'] == 'mixed'): ?>
                                                    | Other: <?= format_currency($invoice['payment_amount'] - $invoice['wallet_amount']) ?>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-success fw-bold">
                                        <?= format_currency($invoice['payment_amount']) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="p-3 text-center">
                            <p class="text-muted mb-0">No payments recorded for this invoice.</p>
                        </div>
                    <?php endif; ?>
                </div>
                <?php if ($invoice['payment_status'] == 'unpaid' && is_logged_in() && has_role(['admin', 'manager'])): ?>
                    <div class="card-footer">
                        <button type="button" class="btn btn-success w-100" data-bs-toggle="modal" data-bs-target="#recordPaymentModal">
                            <i class="fas fa-plus"></i> Record Payment
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Record Payment Modal -->
<?php if ($invoice['payment_status'] == 'unpaid' && is_logged_in() && has_role(['admin', 'manager'])): ?>
    <div class="modal fade" id="recordPaymentModal" tabindex="-1" aria-labelledby="recordPaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="recordPaymentModalLabel">Record Payment</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="<?= base_url('billing/record-payment/' . $invoice['id']) ?>" method="post">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="payment_amount" class="form-label">Payment Amount</label>
                            <div class="input-group">
                                <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                                <input type="number" class="form-control" id="payment_amount" name="payment_amount" value="<?= $invoice['total_amount'] - $invoice['payment_amount'] ?>" min="0.01" max="<?= $invoice['total_amount'] - $invoice['payment_amount'] ?>" step="0.01" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="payment_method" class="form-label">Payment Method</label>
                            <select class="form-select" id="payment_method" name="payment_method" required>
                                <option value="cash">Cash</option>
                                <option value="credit_card">Credit Card</option>
                                <option value="debit_card">Debit Card</option>
                                <option value="upi">UPI</option>
                                <option value="bank_transfer">Bank Transfer</option>
                                <option value="cheque">Cheque</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="payment_note" class="form-label">Note (Optional)</label>
                            <textarea class="form-control" id="payment_note" name="payment_note" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Record Payment</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php endif; ?>

<style>
.avatar-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}
</style>

<script>
// Function to open WhatsApp Web in the same tab if already open
function openWhatsAppWeb(url) {
    // Try to find existing WhatsApp Web tab
    let whatsappTab = null;

    // Check if we have a reference to the WhatsApp tab
    if (window.whatsappWebTab && !window.whatsappWebTab.closed) {
        whatsappTab = window.whatsappWebTab;
    }

    if (whatsappTab) {
        // If tab exists and is not closed, use it
        whatsappTab.location.href = url;
        whatsappTab.focus();
    } else {
        // Open new tab and store reference
        window.whatsappWebTab = window.open(url, 'whatsapp_web');
    }
}
</script>

