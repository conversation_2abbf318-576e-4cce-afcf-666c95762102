<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-edit"></i> Edit Product</h1>
        <div>
            <a href="<?= base_url('inventory') ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Products
            </a>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('inventory/update/' . $product['id']) ?>" method="post" enctype="multipart/form-data">
                <input type="hidden" name="id" value="<?= $product['id'] ?>">
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-12 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-info-circle me-2"></i>Basic Information</h5>
                    </div>

                    <!-- Product Name -->
                    <div class="col-md-6 mb-3">
                        <label for="name" class="form-label">Product Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" value="<?= $product['name'] ?>" required>
                    </div>

                    <!-- SKU -->
                    <div class="col-md-6 mb-3">
                        <label for="sku" class="form-label">SKU/Barcode</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="sku" name="sku" value="<?= $product['sku'] ?>">
                            <button class="btn btn-outline-secondary" type="button" id="generate-sku">Generate</button>
                        </div>
                    </div>

                    <!-- Category -->
                    <div class="col-md-6 mb-3">
                        <label for="category_id" class="form-label">Category</label>
                        <div class="input-group">
                            <select class="form-select" id="category_id" name="category_id">
                                <option value="">Select Category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>" <?= $product['category_id'] == $category['id'] ? 'selected' : '' ?>>
                                        <?= $category['name'] ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <button class="btn btn-outline-secondary" type="button" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Brand -->
                    <div class="col-md-6 mb-3">
                        <label for="brand" class="form-label">Brand</label>
                        <input type="text" class="form-control" id="brand" name="brand" value="<?= $product['brand'] ?>">
                    </div>

                    <!-- Description -->
                    <div class="col-md-12 mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?= $product['description'] ?></textarea>
                    </div>

                    <!-- Pricing & Inventory -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-tag me-2"></i>Pricing & Inventory</h5>
                    </div>

                    <!-- Purchase Price -->
                    <div class="col-md-4 mb-3">
                        <label for="purchase_price" class="form-label">Purchase Price <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                            <input type="number" class="form-control" id="purchase_price" name="purchase_price" step="0.01" min="0" value="<?= $product['cost_price'] ?? 0 ?>" required>
                        </div>
                        <div class="form-text">Cost price per unit</div>
                    </div>

                    <!-- Selling Price -->
                    <div class="col-md-4 mb-3">
                        <label for="price" class="form-label">Selling Price <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><?= CURRENCY_SYMBOL ?></span>
                            <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" value="<?= $product['selling_price'] ?>" required>
                        </div>
                        <div class="form-text">Retail price per unit</div>
                    </div>

                    <!-- Quantity -->
                    <div class="col-md-4 mb-3">
                        <label for="quantity" class="form-label">Quantity <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="quantity" name="quantity" min="0" value="<?= $product['quantity'] ?>" required>
                        <div class="form-text">Current stock level</div>
                    </div>

                    <!-- Low Stock Alert -->
                    <div class="col-md-4 mb-3">
                        <label for="low_stock_threshold" class="form-label">Low Stock Alert</label>
                        <input type="number" class="form-control" id="low_stock_threshold" name="low_stock_threshold" min="0" value="<?= $product['low_stock_threshold'] ?>">
                        <div class="form-text">Alert when stock falls below this level</div>
                    </div>

                    <!-- Tax Rate -->
                    <div class="col-md-4 mb-3">
                        <label for="tax_rate" class="form-label">Tax Rate (%)</label>
                        <input type="number" class="form-control" id="tax_rate" name="tax_rate" min="0" step="0.01" value="<?= $product['tax_rate'] ?>">
                        <div class="form-text">Tax percentage applied to this product</div>
                    </div>

                    <!-- Status -->
                    <div class="col-md-4 mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="active" <?= $product['status'] == 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= $product['status'] == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                        <div class="form-text">Inactive products won't appear in sales</div>
                    </div>

                    <!-- Additional Information -->
                    <div class="col-md-12 mt-4 mb-4">
                        <h5 class="border-bottom pb-2 text-primary"><i class="fas fa-clipboard-list me-2"></i>Additional Information</h5>
                    </div>

                    <!-- Supplier -->
                    <div class="col-md-6 mb-3">
                        <label for="supplier" class="form-label">Supplier</label>
                        <input type="text" class="form-control" id="supplier" name="supplier" value="<?= $product['supplier'] ?>">
                    </div>

                    <!-- Expiry Date -->
                    <div class="col-md-6 mb-3">
                        <label for="expiry_date" class="form-label">Expiry Date</label>
                        <input type="date" class="form-control" id="expiry_date" name="expiry_date" value="<?= $product['expiry_date'] ?>">
                    </div>

                    <!-- Product Image -->
                    <div class="col-md-12 mb-3">
                        <label for="image" class="form-label">Product Image</label>
                        <?php if (!empty($product['image'])): ?>
                            <div class="mb-2">
                                <img src="<?= base_url('uploads/products/' . $product['image']) ?>" alt="<?= $product['name'] ?>" class="img-thumbnail" style="max-height: 100px;">
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="checkbox" id="remove_image" name="remove_image">
                                    <label class="form-check-label" for="remove_image">Remove current image</label>
                                </div>
                            </div>
                        <?php endif; ?>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <div class="form-text">Maximum file size: 2MB. Supported formats: JPG, PNG, GIF</div>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-md-12 mt-4 text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-save me-2"></i> Update Product
                        </button>
                        <a href="<?= base_url('inventory') ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCategoryModalLabel">Add New Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addCategoryForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="category_name" class="form-label">Category Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="category_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="category_description" class="form-label">Description</label>
                        <textarea class="form-control" id="category_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Generate SKU
    document.getElementById('generate-sku').addEventListener('click', function() {
        const randomPart = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        const datePart = new Date().getTime().toString().slice(-4);
        document.getElementById('sku').value = 'PRD-' + randomPart + datePart;
    });

    // Calculate profit margin
    const purchasePriceInput = document.getElementById('purchase_price');
    const sellingPriceInput = document.getElementById('price');

    function updateMargin() {
        const purchasePrice = parseFloat(purchasePriceInput.value) || 0;
        const sellingPrice = parseFloat(sellingPriceInput.value) || 0;

        if (purchasePrice > 0 && sellingPrice > 0) {
            const profit = sellingPrice - purchasePrice;
            const margin = (profit / sellingPrice) * 100;

            // Display margin somewhere if needed
            console.log(`Profit: ${profit.toFixed(2)}, Margin: ${margin.toFixed(2)}%`);
        }
    }

    purchasePriceInput.addEventListener('input', updateMargin);
    sellingPriceInput.addEventListener('input', updateMargin);

    // Add Category via AJAX
    document.getElementById('addCategoryForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const name = document.getElementById('category_name').value;
        const description = document.getElementById('category_description').value;

        // Create form data
        const formData = new FormData();
        formData.append('name', name);
        formData.append('description', description);

        // Send AJAX request
        fetch('<?= base_url('inventory/categories/store-ajax') ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add new category to dropdown
                const categorySelect = document.getElementById('category_id');
                const option = document.createElement('option');
                option.value = data.category.id;
                option.textContent = data.category.name;
                option.selected = true;
                categorySelect.appendChild(option);

                // Close modal
                bootstrap.Modal.getInstance(document.getElementById('addCategoryModal')).hide();

                // Reset form
                document.getElementById('addCategoryForm').reset();

                // Show success message
                alert('Category added successfully!');
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while adding the category.');
        });
    });
});
</script>
