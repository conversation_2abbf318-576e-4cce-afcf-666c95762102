/**
 * Mobile Responsive Fixes
 *
 * This file contains CSS fixes for mobile view issues
 */

/* Hero Slider Mobile Fixes */
.hero-slider-container {
    width: 99vw !important;
    position: relative !important;
    left: -12px !important;
    right: 0 !important;
    margin-top: -24px !important;
    margin-bottom: 40px !important;
    overflow: hidden !important;
}

/* Mobile-specific hero slider fixes */
@media (max-width: 768px) {
    .hero-slider-container {
        left: -12px !important;
        right: 0 !important;
        width: 100vw !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}

/* Mobile Button Group Fixes */
@media (max-width: 767.98px) {
    /* Fix for action buttons at the top of pages */
    .content-container .d-flex.justify-content-between,
    .container .d-flex.justify-content-between {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .content-container .d-flex.justify-content-between h1,
    .container .d-flex.justify-content-between h1 {
        margin-bottom: 15px;
    }

    .content-container .d-flex.justify-content-between > div,
    .container .d-flex.justify-content-between > div {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        width: 100%;
        margin-bottom: 15px;
    }

    /* Fix for button groups in general */
    .btn-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        width: 100%;
    }

    .btn-group .btn {
        flex: 1;
        min-width: 40px;
        margin: 0 !important;
        border-radius: 4px !important;
    }

    /* Fix for search form */
    .input-group {
        width: 100%;
    }

    /* Fix for filter buttons */
    .d-flex.flex-wrap.gap-2 {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px !important;
        width: 100%;
    }

    .d-flex.flex-wrap.gap-2 .btn {
        margin: 0 !important;
        width: 100%;
        text-align: center;
    }

    /* Fix for action buttons in tables */
    .table .btn-group {
        display: flex;
        flex-direction: row;
        width: auto;
    }

    .table .btn-group .btn {
        padding: 4px 8px;
        font-size: 0.8rem;
    }

    /* Fix for dropdown buttons */
    .dropdown-toggle {
        width: 100%;
        text-align: center;
    }

    /* Fix for search and filter sections */
    .card-body .row.g-3 {
        margin-left: 0;
        margin-right: 0;
    }

    .card-body .row.g-3 .col-md-8,
    .card-body .row.g-3 .col-md-6,
    .card-body .row.g-3 .col-md-4,
    .card-body .row.g-3 .col-md-3 {
        padding: 0;
        margin-bottom: 10px;
    }

    /* Fix for buttons in general */
    .btn {
        margin-bottom: 5px;
    }

    .me-2 {
        margin-right: 5px !important;
    }
}

/* Additional invoice page fixes */
@media (max-width: 767.98px) {
    /* Fix for invoice form */
    #invoice-form .row {
        margin-left: 0;
        margin-right: 0;
    }

    /* Fix for customer selection */
    #customer_id {
        width: 100% !important;
    }

    /* Fix for service and product rows */
    .service-row, .product-row {
        position: relative;
        padding-bottom: 50px !important;
    }

    /* Position remove button at the bottom */
    .service-row .col-md-1, .product-row .col-md-1 {
        position: absolute;
        bottom: 10px;
        right: 10px;
        width: auto !important;
    }

    /* Fix for add service/product buttons */
    #add-service, #add-product {
        width: 100%;
        margin-top: 10px;
    }

    /* Fix for invoice summary card */
    .card-body .card {
        margin-top: 15px;
    }

    /* Fix for payment section */
    .card-body .row .col-md-4 {
        padding: 0;
    }

    /* Fix for submit buttons */
    .col-md-12.mt-4.mb-3.text-center .btn {
        width: 100%;
        margin-bottom: 10px;
        margin-left: 0 !important;
    }
}

/* Make buttons full width on very small screens */
@media (max-width: 480px) {
    .content-container .d-flex.justify-content-between > div .btn,
    .container .d-flex.justify-content-between > div .btn {
        width: 100%;
        margin-right: 0 !important;
        text-align: center;
        padding: 8px 16px;
    }

    /* Fix for button groups in tables */
    .table .btn-group {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 4px;
        width: 100%;
    }

    .table .btn-group .btn {
        width: 100%;
        margin: 0 !important;
    }

    /* Fix for category buttons */
    .d-flex.flex-wrap.gap-2 {
        grid-template-columns: 1fr;
    }

    /* Fix for search and filter layout */
    .input-group {
        flex-direction: column;
    }

    .input-group > * {
        width: 100%;
        margin-bottom: 5px;
        border-radius: 4px !important;
    }

    .input-group .btn {
        border-radius: 4px !important;
    }

    /* Fix for search button */
    .input-group .btn-primary {
        width: 100%;
        margin-top: 5px;
    }

    /* Fix for search input */
    .input-group input.form-control {
        width: 100%;
        border-radius: 4px !important;
    }

    /* Fix for search icon */
    .input-group-text {
        border-radius: 4px !important;
        width: 100%;
        justify-content: center;
    }

    /* Fix for filter dropdown */
    .col-md-4 .d-flex.justify-content-end {
        width: 100%;
    }

    .col-md-4 .d-flex.justify-content-end .dropdown {
        width: 100%;
    }

    /* Specific fixes for the pages shown in screenshots */
    /* Customers page */
    .content-container > div:first-child > div {
        flex-direction: column;
        width: 100%;
    }

    .content-container > div:first-child > div > a,
    .content-container > div:first-child > div > button {
        width: 100%;
        margin-right: 0 !important;
        margin-bottom: 8px;
        text-align: center;
    }

    /* Services page */
    .container > div:first-child > div {
        flex-direction: column;
        width: 100%;
    }

    .container > div:first-child > div > a,
    .container > div:first-child > div > button {
        width: 100%;
        margin-right: 0 !important;
        margin-bottom: 8px;
        text-align: center;
    }

    /* Inventory page */
    .content-container > div:first-child > div > .btn-group {
        width: 100%;
        margin-bottom: 8px;
    }

    .content-container > div:first-child > div > a {
        width: 100%;
        margin-right: 0 !important;
        margin-bottom: 8px;
        text-align: center;
    }

    /* Invoice/Billing page fixes */
    /* Fix service and product rows */
    .service-row, .product-row {
        margin-bottom: 20px !important;
        padding: 15px !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 8px !important;
    }

    /* Add spacing between form elements */
    .service-row > div, .product-row > div {
        margin-bottom: 10px !important;
    }

    /* Fix the remove button positioning */
    .service-row .col-md-1, .product-row .col-md-1 {
        text-align: right !important;
        width: 100% !important;
    }

    /* Fix select2 dropdowns */
    .select2-container {
        width: 100% !important;
    }

    /* Fix input groups */
    .service-row .input-group, .product-row .input-group {
        flex-direction: row !important;
    }

    /* Fix coupon code input group */
    #coupon_code {
        flex: 1;
    }

    /* Fix invoice summary section */
    .card-body .row .col-md-8,
    .card-body .row .col-md-4 {
        width: 100% !important;
    }

    /* Fix payment details section */
    .card-body .row .col-md-4 {
        margin-bottom: 15px !important;
    }
}
