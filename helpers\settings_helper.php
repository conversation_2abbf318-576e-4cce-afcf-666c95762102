<?php
/**
 * Settings Helper Functions
 */

/**
 * Get a setting value
 * 
 * @param string $key Setting key
 * @param mixed $default Default value if setting not found
 * @return mixed Setting value
 */
function get_setting($key, $default = null) {
    require_once BASE_PATH . '/models/SettingsModel.php';
    $settings_model = new SettingsModel();
    return $settings_model->getValue($key, $default);
}

/**
 * Get the salon name
 * 
 * @return string Salon name
 */
function get_salon_name() {
    return get_setting('salon_name', APP_NAME);
}
?>
