<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-ticket-alt"></i> Coupons</h1>
        <a href="<?= base_url('coupons/create') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Coupon
        </a>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <?php if (empty($coupons)): ?>
                <div class="text-center py-5">
                    <img src="<?= base_url('assets/images/empty-coupon.svg') ?>" alt="No Coupons" style="width: 120px; opacity: 0.5;" class="mb-3">
                    <h5>No Coupons Found</h5>
                    <p class="text-muted">You haven't created any coupons yet.</p>
                    <a href="<?= base_url('coupons/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create First Coupon
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th>Code</th>
                                <th>Discount</th>
                                <th>Validity</th>
                                <th>Usage</th>
                                <th>Status</th>
                                <th width="15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($coupons as $index => $coupon): ?>
                                <tr>
                                    <td><?= $index + 1 ?></td>
                                    <td>
                                        <div class="fw-bold"><?= $coupon['code'] ?></div>
                                        <div class="small text-muted"><?= $coupon['description'] ? substr($coupon['description'], 0, 50) . (strlen($coupon['description']) > 50 ? '...' : '') : 'No description' ?></div>
                                    </td>
                                    <td>
                                        <?php if ($coupon['discount_type'] == 'percentage'): ?>
                                            <?= $coupon['discount_value'] ?>%
                                            <?php if (!empty($coupon['max_discount'])): ?>
                                                <div class="small text-muted">Max: <?= format_currency($coupon['max_discount']) ?></div>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <?= format_currency($coupon['discount_value']) ?>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($coupon['min_purchase'])): ?>
                                            <div class="small text-muted">Min purchase: <?= format_currency($coupon['min_purchase']) ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($coupon['valid_from']) && !empty($coupon['valid_to'])): ?>
                                            <?= format_date($coupon['valid_from']) ?> to <?= format_date($coupon['valid_to']) ?>
                                        <?php elseif (!empty($coupon['valid_from'])): ?>
                                            From <?= format_date($coupon['valid_from']) ?>
                                        <?php elseif (!empty($coupon['valid_to'])): ?>
                                            Until <?= format_date($coupon['valid_to']) ?>
                                        <?php else: ?>
                                            No expiry
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?= $coupon['usage_count'] ?> used
                                        <?php if (!empty($coupon['usage_limit'])): ?>
                                            <div class="small text-muted">Limit: <?= $coupon['usage_limit'] ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($coupon['status'] == 'active'): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= base_url('coupons/edit/' . $coupon['id']) ?>" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?= base_url('coupons/delete/' . $coupon['id']) ?>" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this coupon?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
