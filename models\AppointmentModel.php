<?php
/**
 * Appointment Model
 */
class AppointmentModel extends Model {
    protected $table = 'appointments';



    /**
     * Get today's appointments
     *
     * @return array Appointments
     */
    public function getTodayAppointments() {
        $query = "SELECT a.*,
                 s.name as service_name,
                 u1.name as customer_name,
                 u2.name as staff_name
                 FROM " . $this->table . " a
                 JOIN services s ON a.service_id = s.id
                 JOIN customers c ON a.customer_id = c.id
                 JOIN users u1 ON c.user_id = u1.id
                 JOIN staff st ON a.staff_id = st.id
                 JOIN users u2 ON st.user_id = u2.id
                 WHERE a.appointment_date = CURDATE()
                 ORDER BY a.start_time";
        $stmt = $this->db->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get appointment statistics
     *
     * @return array Statistics
     */
    public function getAppointmentStatistics() {
        $stats = [
            'today' => 0,
            'this_week' => 0,
            'this_month' => 0,
            'pending' => 0,
            'confirmed' => 0,
            'completed' => 0,
            'cancelled' => 0
        ];

        // Today's appointments
        $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE appointment_date = CURDATE()";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        $stats['today'] = $stmt->fetchColumn();

        // This week's appointments
        $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE YEARWEEK(appointment_date) = YEARWEEK(CURDATE())";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        $stats['this_week'] = $stmt->fetchColumn();

        // This month's appointments
        $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE MONTH(appointment_date) = MONTH(CURDATE()) AND YEAR(appointment_date) = YEAR(CURDATE())";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        $stats['this_month'] = $stmt->fetchColumn();

        // Appointment status counts
        $statuses = ['pending', 'confirmed', 'completed', 'cancelled'];
        foreach ($statuses as $status) {
            $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE status = :status";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':status', $status);
            $stmt->execute();
            $stats[$status] = $stmt->fetchColumn();
        }

        return $stats;
    }

    /**
     * Get all appointments with details
     *
     * @param string $status Filter by status (optional)
     * @param string $date Filter by date (optional)
     * @param int $staff_id Filter by staff ID (optional)
     * @return array Appointments
     */
    public function getAllWithDetails($status = null, $date = null, $staff_id = null) {
        $query = "SELECT a.*,
                 s.name as service_name, s.price as service_price, s.duration as service_duration,
                 c.phone as customer_phone,
                 u1.name as customer_name, u1.email as customer_email,
                 u2.name as staff_name
                 FROM " . $this->table . " a
                 JOIN services s ON a.service_id = s.id
                 JOIN customers c ON a.customer_id = c.id
                 JOIN users u1 ON c.user_id = u1.id
                 JOIN staff st ON a.staff_id = st.id
                 JOIN users u2 ON st.user_id = u2.id
                 WHERE 1=1";

        $params = [];

        if ($status) {
            $query .= " AND a.status = :status";
            $params[':status'] = $status;
        }

        if ($date) {
            $query .= " AND a.appointment_date = :date";
            $params[':date'] = $date;
        }

        if ($staff_id) {
            $query .= " AND a.staff_id = :staff_id";
            $params[':staff_id'] = $staff_id;
        }

        $query .= " ORDER BY a.appointment_date DESC, a.start_time ASC";

        $stmt = $this->db->prepare($query);

        foreach ($params as $param => $value) {
            $stmt->bindValue($param, $value);
        }

        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get appointment with details
     *
     * @param int $id Appointment ID
     * @return array|false Appointment data or false if not found
     */
    public function getWithDetails($id) {
        $query = "SELECT a.*,
                 s.name as service_name, s.price as service_price, s.duration as service_duration,
                 c.phone as customer_phone,
                 u1.name as customer_name, u1.email as customer_email,
                 u2.name as staff_name
                 FROM " . $this->table . " a
                 JOIN services s ON a.service_id = s.id
                 JOIN customers c ON a.customer_id = c.id
                 JOIN users u1 ON c.user_id = u1.id
                 JOIN staff st ON a.staff_id = st.id
                 JOIN users u2 ON st.user_id = u2.id
                 WHERE a.id = :id
                 LIMIT 1";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get appointments by customer ID
     *
     * @param int $customer_id Customer ID
     * @return array Appointments
     */
    public function getByCustomerId($customer_id) {
        $query = "SELECT a.*,
                 s.name as service_name, s.price as service_price, s.duration as service_duration,
                 u2.name as staff_name
                 FROM " . $this->table . " a
                 JOIN services s ON a.service_id = s.id
                 JOIN staff st ON a.staff_id = st.id
                 JOIN users u2 ON st.user_id = u2.id
                 WHERE a.customer_id = :customer_id
                 ORDER BY a.appointment_date DESC, a.start_time ASC";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':customer_id', $customer_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get appointments for a specific month
     *
     * @param string $month Month (01-12)
     * @param string $year Year (YYYY)
     * @param int $staff_id Staff ID (optional)
     * @return array Appointments
     */
    public function getMonthAppointments($month, $year, $staff_id = null) {
        $query = "SELECT a.*,
                 s.name as service_name, s.duration as service_duration,
                 u1.name as customer_name,
                 u2.name as staff_name
                 FROM " . $this->table . " a
                 JOIN services s ON a.service_id = s.id
                 JOIN customers c ON a.customer_id = c.id
                 JOIN users u1 ON c.user_id = u1.id
                 JOIN staff st ON a.staff_id = st.id
                 JOIN users u2 ON st.user_id = u2.id
                 WHERE MONTH(a.appointment_date) = :month
                 AND YEAR(a.appointment_date) = :year";

        if ($staff_id) {
            $query .= " AND a.staff_id = :staff_id";
        }

        $query .= " ORDER BY a.appointment_date, a.start_time";

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':month', $month);
        $stmt->bindParam(':year', $year);

        if ($staff_id) {
            $stmt->bindParam(':staff_id', $staff_id);
        }

        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Check if time slot is available
     *
     * @param int $staff_id Staff ID
     * @param string $date Date
     * @param string $start_time Start time
     * @param string $end_time End time
     * @param int $exclude_id Appointment ID to exclude (for updates)
     * @return bool True if available, false if not
     */
    public function isTimeSlotAvailable($staff_id, $date, $start_time, $end_time, $exclude_id = null) {
        $query = "SELECT COUNT(*) FROM " . $this->table . "
                 WHERE staff_id = :staff_id
                 AND appointment_date = :date
                 AND status != 'cancelled'
                 AND (
                     (start_time <= :start_time AND end_time > :start_time) OR
                     (start_time < :end_time AND end_time >= :end_time) OR
                     (start_time >= :start_time AND end_time <= :end_time)
                 )";

        if ($exclude_id) {
            $query .= " AND id != :exclude_id";
        }

        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':staff_id', $staff_id);
        $stmt->bindParam(':date', $date);
        $stmt->bindParam(':start_time', $start_time);
        $stmt->bindParam(':end_time', $end_time);

        if ($exclude_id) {
            $stmt->bindParam(':exclude_id', $exclude_id);
        }

        $stmt->execute();

        return $stmt->fetchColumn() == 0;
    }
}
