<?php
/**
 * Base Model class
 */
class Model {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';

    /**
     * Constructor
     */
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    /**
     * Get database connection
     *
     * @return PDO Database connection
     */
    public function getDb() {
        return $this->db;
    }

    /**
     * Find a record by ID
     *
     * @param int $id Record ID
     * @return array|false Record data or false if not found
     */
    public function find($id) {
        $query = "SELECT * FROM " . $this->table . " WHERE " . $this->primaryKey . " = :id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get all records
     *
     * @return array Records
     */
    public function all() {
        $query = "SELECT * FROM " . $this->table;
        $stmt = $this->db->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Create a new record
     *
     * @param array $data Record data
     * @return int|false Last insert ID or false on failure
     */
    public function create($data) {
        // Build query
        $fields = array_keys($data);
        $placeholders = array_map(function($field) {
            return ':' . $field;
        }, $fields);

        $query = "INSERT INTO " . $this->table . " (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        $stmt = $this->db->prepare($query);

        // Bind parameters
        foreach ($data as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }

        // Execute
        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }

        return false;
    }

    /**
     * Update a record
     *
     * @param int $id Record ID
     * @param array $data Record data
     * @return bool Success or failure
     * @throws PDOException If a database error occurs
     */
    public function update($id, $data) {
        try {
            // Set PDO to throw exceptions on error
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Build query
            $fields = array_keys($data);
            $set = array_map(function($field) {
                return $field . ' = :' . $field;
            }, $fields);

            $query = "UPDATE " . $this->table . " SET " . implode(', ', $set) . " WHERE " . $this->primaryKey . " = :id";
            $stmt = $this->db->prepare($query);

            // Debug the query
            error_log("SQL Query: " . $query);
            error_log("Data: " . json_encode($data));

            // Bind parameters
            $stmt->bindParam(':id', $id);
            foreach ($data as $key => $value) {
                // Handle NULL values properly
                if ($value === null) {
                    $stmt->bindValue(':' . $key, $value, PDO::PARAM_NULL);
                    error_log("Binding NULL for $key");
                } else {
                    $stmt->bindValue(':' . $key, $value);
                    error_log("Binding value for $key: $value");
                }
            }

            // Execute
            $result = $stmt->execute();
            error_log("Update result: " . ($result ? 'Success' : 'Failed'));
            return $result;
        } catch (PDOException $e) {
            error_log("Database error in update: " . $e->getMessage());
            throw $e; // Re-throw the exception for the controller to handle
        }
    }

    /**
     * Delete a record
     *
     * @param int $id Record ID
     * @return bool Success or failure
     */
    public function delete($id) {
        $query = "DELETE FROM " . $this->table . " WHERE " . $this->primaryKey . " = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);

        return $stmt->execute();
    }

    /**
     * Find records by a field value
     *
     * @param string $field Field name
     * @param mixed $value Field value
     * @return array Records
     */
    public function findBy($field, $value) {
        $query = "SELECT * FROM " . $this->table . " WHERE " . $field . " = :value";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':value', $value);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
