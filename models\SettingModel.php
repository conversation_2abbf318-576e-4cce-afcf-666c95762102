<?php
/**
 * Setting Model
 */
class SettingModel extends Model {
    protected $table = 'settings';
    
    /**
     * Get setting value by key
     * 
     * @param string $key Setting key
     * @param mixed $default Default value if setting not found
     * @return mixed Setting value
     */
    public function getValue($key, $default = null) {
        $query = "SELECT setting_value FROM " . $this->table . " WHERE setting_key = :key LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':key', $key);
        $stmt->execute();
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result ? $result['setting_value'] : $default;
    }
    
    /**
     * Set setting value
     * 
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @return bool Success or failure
     */
    public function setValue($key, $value) {
        // Check if setting exists
        $query = "SELECT COUNT(*) FROM " . $this->table . " WHERE setting_key = :key";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':key', $key);
        $stmt->execute();
        
        $exists = $stmt->fetchColumn() > 0;
        
        if ($exists) {
            // Update existing setting
            $query = "UPDATE " . $this->table . " SET setting_value = :value WHERE setting_key = :key";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':key', $key);
            $stmt->bindParam(':value', $value);
            
            return $stmt->execute();
        } else {
            // Insert new setting
            $query = "INSERT INTO " . $this->table . " (setting_key, setting_value) VALUES (:key, :value)";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':key', $key);
            $stmt->bindParam(':value', $value);
            
            return $stmt->execute();
        }
    }
    
    /**
     * Get all settings as key-value pairs
     * 
     * @return array Settings
     */
    public function getAllSettings() {
        $query = "SELECT setting_key, setting_value FROM " . $this->table;
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        $settings = [];
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($results as $result) {
            $settings[$result['setting_key']] = $result['setting_value'];
        }
        
        return $settings;
    }
    
    /**
     * Delete setting
     * 
     * @param string $key Setting key
     * @return bool Success or failure
     */
    public function deleteSetting($key) {
        $query = "DELETE FROM " . $this->table . " WHERE setting_key = :key";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':key', $key);
        
        return $stmt->execute();
    }
}
