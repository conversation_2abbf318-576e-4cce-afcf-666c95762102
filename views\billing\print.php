<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            background-color: #fff;
        }
        .invoice-header {
            padding: 20px 0;
            border-bottom: 1px solid #ddd;
        }
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .invoice-details {
            margin-top: 20px;
            margin-bottom: 20px;
        }
        .invoice-details-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .table-totals {
            margin-top: 20px;
        }
        .table-totals td {
            padding: 5px;
        }
        .table-totals .total-row {
            font-weight: bold;
            font-size: 16px;
            border-top: 1px solid #ddd;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                margin: 0;
                padding: 15px;
            }
            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="no-print mb-3 mt-3">
            <button class="btn btn-primary" onclick="window.print()">Print Invoice</button>
            <button class="btn btn-secondary" onclick="window.close()">Close</button>
        </div>

        <div class="invoice-header">
            <div class="row">
                <div class="col-6">
                    <div class="invoice-title">INVOICE</div>
                    <div>#<?= str_pad($invoice['id'], 6, '0', STR_PAD_LEFT) ?></div>
                </div>
                <div class="col-6 text-end">

                    <div class="mt-2">
                        <strong><?= $settings['salon_name'] ?? 'Vishal\'s makeover' ?></strong><br>
                        <?= $settings['salon_address'] ?? 'Sadar Bazar' ?><br>
                        <?= $settings['salon_phone'] ?? '+918573061818' ?><br>
                        <?= $settings['salon_email'] ?? '<EMAIL>' ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="invoice-details">
            <div class="row">
                <div class="col-6">
                    <div class="invoice-details-title">Bill To:</div>
                    <div><strong><?= $invoice['customer_name'] ?></strong></div>
                    <div><?= $invoice['customer_email'] ?></div>
                    <div><?= $invoice['customer_phone'] ?></div>
                    <?php if (!empty($invoice['customer_address'])): ?>
                        <div><?= $invoice['customer_address'] ?></div>
                    <?php endif; ?>
                </div>
                <div class="col-6 text-end">
                    <div class="invoice-details-title">Invoice Details:</div>
                    <div><strong>Invoice Date:</strong> <?= format_date($invoice['invoice_date']) ?></div>
                    <div>
                        <strong>Status:</strong>
                        <?php if ($invoice['payment_status'] == 'unpaid'): ?>
                            <span class="text-warning">Unpaid</span>
                        <?php elseif ($invoice['payment_status'] == 'partial'): ?>
                            <span class="text-info">Partial</span>
                        <?php elseif ($invoice['payment_status'] == 'paid'): ?>
                            <span class="text-success">Paid</span>
                        <?php elseif ($invoice['payment_status'] == 'cancelled'): ?>
                            <span class="text-danger">Cancelled</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice Summary -->


        <?php if (!empty($invoice['services'])): ?>
            <div class="mb-4">
                <h5>Services</h5>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Service</th>
                            <th>Category</th>
                            <th>Staff</th>
                            <th class="text-end">Price</th>
                            <th class="text-end">Qty</th>
                            <th class="text-end">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($invoice['services'] as $service): ?>
                            <tr>
                                <td>
                                    <div class="fw-bold"><?= $service['service_name'] ?></div>
                                    <?php if (!empty($service['service_description'])): ?>
                                        <div class="small text-muted"><?= substr($service['service_description'], 0, 50) . (strlen($service['service_description']) > 50 ? '...' : '') ?></div>
                                    <?php endif; ?>
                                </td>
                                <td><?= $service['service_category_name'] ?></td>
                                <td><?= $service['staff_name'] ?? 'N/A' ?></td>
                                <td class="text-end"><?= format_currency(isset($service['price']) ? $service['price'] : ($service['unit_price'] ?? 0)) ?></td>
                                <td class="text-end"><?= $service['quantity'] ?></td>
                                <td class="text-end"><?= format_currency(isset($service['price']) ? $service['price'] * $service['quantity'] : ($service['unit_price'] ?? 0) * $service['quantity']) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <?php if (!empty($invoice['products'])): ?>
            <div class="mb-4">
                <h5>Products</h5>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Category</th>
                            <th class="text-end">Price</th>
                            <th class="text-end">Qty</th>
                            <th class="text-end">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($invoice['products'] as $product): ?>
                            <tr>
                                <td>
                                    <div class="fw-bold"><?= $product['product_name'] ?></div>
                                    <?php if (!empty($product['product_description'])): ?>
                                        <div class="small text-muted"><?= substr($product['product_description'], 0, 50) . (strlen($product['product_description']) > 50 ? '...' : '') ?></div>
                                    <?php endif; ?>
                                </td>
                                <td><?= $product['product_category_name'] ?></td>
                                <td class="text-end"><?= format_currency(isset($product['price']) ? $product['price'] : ($product['unit_price'] ?? 0)) ?></td>
                                <td class="text-end"><?= $product['quantity'] ?></td>
                                <td class="text-end"><?= format_currency(isset($product['price']) ? $product['price'] * $product['quantity'] : ($product['unit_price'] ?? 0) * $product['quantity']) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-7">
                <?php if (!empty($invoice['notes'])): ?>
                    <div class="mb-4">
                        <h6>Notes:</h6>
                        <div class="p-3 bg-light">
                            <?= nl2br($invoice['notes']) ?>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="mb-4">
                    <h6>Payment Information:</h6>
                    <div>
                        <strong>Payment Method:</strong> <?= ucfirst(str_replace('_', ' ', $invoice['payment_method'])) ?>
                    </div>
                    <?php if ($invoice['payment_amount'] > 0): ?>
                        <div>
                            <strong>Amount Paid:</strong> <?= format_currency($invoice['payment_amount']) ?>
                        </div>
                        <?php if (isset($invoice['wallet_amount']) && $invoice['wallet_amount'] > 0): ?>
                            <div>
                                <strong>Wallet Payment:</strong> <?= format_currency($invoice['wallet_amount']) ?>
                            </div>
                            <?php if ($invoice['payment_method'] == 'mixed'): ?>
                                <div>
                                    <strong>Other Payment:</strong> <?= format_currency($invoice['payment_amount'] - $invoice['wallet_amount']) ?>
                                </div>
                            <?php endif; ?>
                            <?php
                            // Get customer's remaining wallet balance
                            $wallet_model = new WalletModel();
                            $current_wallet = $wallet_model->getByCustomerId($invoice['customer_id']);
                            $remaining_balance = $current_wallet ? $current_wallet['balance'] : 0;
                            ?>
                            <div>
                                <strong>Remaining Wallet Balance:</strong> <?= format_currency($remaining_balance) ?>
                            </div>
                        <?php endif; ?>
                        <div>
                            <strong>Payment Date:</strong> <?= format_date($invoice['invoice_date']) ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-5">
                <table class="table-totals ms-auto">
                    <tr>
                        <td class="text-end"><strong>Subtotal:</strong></td>
                        <td class="text-end" style="min-width: 120px;"><?= format_currency($invoice['subtotal']) ?></td>
                    </tr>
                    <?php if ($invoice['discount_amount'] > 0): ?>
                        <?php
                            // Calculate discount percentage
                            $discount_percentage = ($invoice['discount_amount'] / $invoice['subtotal']) * 100;
                        ?>
                        <tr>
                            <td class="text-end"><strong>Discount (<?= number_format($discount_percentage, 2) ?>%):</strong></td>
                            <td class="text-end text-danger">-<?= format_currency($invoice['discount_amount']) ?></td>
                        </tr>
                        <?php if (isset($invoice['coupon'])): ?>
                        <tr>
                            <td class="text-end"><small class="text-muted">Coupon Applied:</small></td>
                            <td class="text-end"><small class="text-muted"><?= $invoice['coupon']['coupon_code'] ?>
                                (<?= $invoice['coupon']['discount_type'] === 'percentage' ? $invoice['coupon']['discount_value'] . '%' : format_currency($invoice['coupon']['discount_value']) ?>)</small></td>
                        </tr>
                        <?php elseif (isset($invoice['customer_id'])): ?>
                        <?php
                            // Check if customer has an active membership
                            $membership_model = new MembershipModel();
                            $membership = $membership_model->getActiveMembershipByCustomerId($invoice['customer_id']);
                            if ($membership):
                        ?>
                        <tr>
                            <td class="text-end"><small class="text-success"><?= $membership['membership_name'] ?> Membership:</small></td>
                            <td class="text-end"><small class="text-success"><?= $membership['service_discount'] ?>% off services</small></td>
                        </tr>
                        <?php endif; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php if ($invoice['tax_amount'] > 0): ?>
                        <tr>
                            <td class="text-end"><strong>Tax:</strong></td>
                            <td class="text-end"><?= format_currency($invoice['tax_amount']) ?></td>
                        </tr>
                    <?php endif; ?>
                    <tr class="total-row">
                        <td class="text-end"><strong>Total:</strong></td>
                        <td class="text-end"><?= format_currency($invoice['total_amount']) ?></td>
                    </tr>
                    <tr>
                        <td class="text-end"><strong>Amount Paid:</strong></td>
                        <td class="text-end text-success"><?= format_currency($invoice['payment_amount']) ?></td>
                    </tr>
                    <?php if ($invoice['payment_amount'] < $invoice['total_amount']): ?>
                        <tr>
                            <td class="text-end"><strong>Balance Due:</strong></td>
                            <td class="text-end text-danger"><?= format_currency($invoice['total_amount'] - $invoice['payment_amount']) ?></td>
                        </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>

        <div class="footer text-center">
            <p>Thank you for your business!</p>
            <p>This invoice was generated on <?= format_datetime(date('Y-m-d H:i:s')) ?></p>
        </div>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            // Delay printing to ensure everything is loaded
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>
