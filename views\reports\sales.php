<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-chart-line"></i> Sales Report</h1>
    </div>

    <!-- Date Range Filter -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('reports/sales') ?>" method="get" class="row align-items-end">
                <div class="col-md-3 mb-2">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                </div>
                <div class="col-md-3 mb-2">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                </div>
                <div class="col-md-3 mb-2">
                    <label for="group_by" class="form-label">Group By</label>
                    <select class="form-select" id="group_by" name="group_by">
                        <option value="day" <?= $group_by == 'day' ? 'selected' : '' ?>>Day</option>
                        <option value="month" <?= $group_by == 'month' ? 'selected' : '' ?>>Month</option>
                        <option value="year" <?= $group_by == 'year' ? 'selected' : '' ?>>Year</option>
                    </select>
                </div>
                <div class="col-md-3 mb-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i> Apply Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <!-- Reports Navigation -->
        <div class="col-md-3 mb-4">
            <?php include 'partials/reports_nav.php'; ?>
        </div>
        
        <!-- Reports Content -->
        <div class="col-md-9">
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Sales</h6>
                                    <h4 class="mb-0"><?= format_currency($sales_report['totals']['total_amount']) ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-dollar-sign text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Invoices</h6>
                                    <h4 class="mb-0"><?= $sales_report['totals']['invoice_count'] ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-file-invoice text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Average Sale</h6>
                                    <h4 class="mb-0"><?= $sales_report['totals']['invoice_count'] > 0 ? format_currency($sales_report['totals']['total_amount'] / $sales_report['totals']['invoice_count']) : format_currency(0) ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-chart-line text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sales Chart -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Sales Trend</h5>
                    <a href="<?= base_url('reports/export?type=sales&start_date=' . $start_date . '&end_date=' . $end_date . '&group_by=' . $group_by) ?>" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-download me-1"></i> Export CSV
                    </a>
                </div>
                <div class="card-body">
                    <canvas id="salesChart" height="300"></canvas>
                </div>
            </div>
            
            <!-- Sales Data Table -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Sales Data</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($sales_report['data'])): ?>
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No sales data available for the selected period.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th><?= $sales_report['group_label'] ?></th>
                                        <th class="text-end">Invoices</th>
                                        <th class="text-end">Subtotal</th>
                                        <th class="text-end">Tax</th>
                                        <th class="text-end">Discount</th>
                                        <th class="text-end">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($sales_report['data'] as $row): ?>
                                        <tr>
                                            <td><?= $row['period'] ?></td>
                                            <td class="text-end"><?= $row['invoice_count'] ?></td>
                                            <td class="text-end"><?= format_currency($row['subtotal']) ?></td>
                                            <td class="text-end"><?= format_currency($row['tax_amount']) ?></td>
                                            <td class="text-end"><?= format_currency($row['discount_amount']) ?></td>
                                            <td class="text-end"><?= format_currency($row['total_amount']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th>Total</th>
                                        <th class="text-end"><?= $sales_report['totals']['invoice_count'] ?></th>
                                        <th class="text-end"><?= format_currency($sales_report['totals']['subtotal']) ?></th>
                                        <th class="text-end"><?= format_currency($sales_report['totals']['tax_amount']) ?></th>
                                        <th class="text-end"><?= format_currency($sales_report['totals']['discount_amount']) ?></th>
                                        <th class="text-end"><?= format_currency($sales_report['totals']['total_amount']) ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sales Chart
    const salesData = <?= json_encode($sales_report['data']) ?>;
    const salesLabels = salesData.map(item => item.period);
    const salesValues = salesData.map(item => item.total_amount);
    
    const salesCtx = document.getElementById('salesChart').getContext('2d');
    new Chart(salesCtx, {
        type: 'bar',
        data: {
            labels: salesLabels,
            datasets: [{
                label: 'Sales',
                data: salesValues,
                backgroundColor: 'rgba(52, 152, 219, 0.7)',
                borderColor: 'rgba(52, 152, 219, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '<?= CURRENCY_SYMBOL ?>' + value;
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Sales: <?= CURRENCY_SYMBOL ?>' + context.raw;
                        }
                    }
                }
            }
        }
    });
});
</script>
