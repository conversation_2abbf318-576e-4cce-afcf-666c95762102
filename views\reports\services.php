<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-concierge-bell"></i> Service Sales Report</h1>
    </div>

    <!-- Date Range Filter -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('reports/services') ?>" method="get" class="row align-items-end">
                <div class="col-md-4 mb-2">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                </div>
                <div class="col-md-4 mb-2">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                </div>
                <div class="col-md-4 mb-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i> Apply Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <!-- Reports Navigation -->
        <div class="col-md-3 mb-4">
            <?php include 'partials/reports_nav.php'; ?>
        </div>
        
        <!-- Reports Content -->
        <div class="col-md-9">
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Services</h6>
                                    <h4 class="mb-0"><?= $service_sales_report['totals']['service_count'] ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-concierge-bell text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Revenue</h6>
                                    <h4 class="mb-0"><?= format_currency($service_sales_report['totals']['total_amount']) ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-dollar-sign text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Average Service Value</h6>
                                    <h4 class="mb-0"><?= $service_sales_report['totals']['service_count'] > 0 ? format_currency($service_sales_report['totals']['total_amount'] / $service_sales_report['totals']['service_count']) : format_currency(0) ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-chart-line text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Service Sales Chart -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Top Services by Revenue</h5>
                    <a href="<?= base_url('reports/export?type=services&start_date=' . $start_date . '&end_date=' . $end_date) ?>" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-download me-1"></i> Export CSV
                    </a>
                </div>
                <div class="card-body">
                    <canvas id="serviceChart" height="300"></canvas>
                </div>
            </div>
            
            <!-- Service Sales Data Table -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Service Sales Data</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($service_sales_report['data'])): ?>
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No service sales data available for the selected period.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Service</th>
                                        <th class="text-end">Price</th>
                                        <th class="text-end">Count</th>
                                        <th class="text-end">Total Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($service_sales_report['data'] as $row): ?>
                                        <tr>
                                            <td><?= $row['service_name'] ?></td>
                                            <td class="text-end"><?= format_currency($row['service_price']) ?></td>
                                            <td class="text-end"><?= $row['service_count'] ?></td>
                                            <td class="text-end"><?= format_currency($row['total_amount']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th>Total</th>
                                        <th></th>
                                        <th class="text-end"><?= $service_sales_report['totals']['service_count'] ?></th>
                                        <th class="text-end"><?= format_currency($service_sales_report['totals']['total_amount']) ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Service Sales Chart
    const serviceData = <?= json_encode($service_sales_report['data']) ?>;
    
    if (serviceData.length > 0) {
        // Sort data by total amount in descending order
        serviceData.sort((a, b) => b.total_amount - a.total_amount);
        
        // Take top 10 services
        const topServices = serviceData.slice(0, 10);
        
        const serviceLabels = topServices.map(item => item.service_name);
        const serviceValues = topServices.map(item => item.total_amount);
        const serviceColors = [
            'rgba(52, 152, 219, 0.7)',
            'rgba(46, 204, 113, 0.7)',
            'rgba(155, 89, 182, 0.7)',
            'rgba(52, 73, 94, 0.7)',
            'rgba(241, 196, 15, 0.7)',
            'rgba(230, 126, 34, 0.7)',
            'rgba(231, 76, 60, 0.7)',
            'rgba(26, 188, 156, 0.7)',
            'rgba(41, 128, 185, 0.7)',
            'rgba(243, 156, 18, 0.7)'
        ];
        
        const serviceCtx = document.getElementById('serviceChart').getContext('2d');
        new Chart(serviceCtx, {
            type: 'bar',
            data: {
                labels: serviceLabels,
                datasets: [{
                    label: 'Revenue',
                    data: serviceValues,
                    backgroundColor: serviceColors,
                    borderColor: serviceColors.map(color => color.replace('0.7', '1')),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '<?= CURRENCY_SYMBOL ?>' + value;
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Revenue: <?= CURRENCY_SYMBOL ?>' + context.raw;
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>
