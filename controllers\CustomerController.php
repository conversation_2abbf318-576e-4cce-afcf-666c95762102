<?php
/**
 * Customer Controller
 */
class CustomerController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            flash('error', 'Please login to access this page');
            header('Location: ' . base_url('login'));
            exit;
        }

        // Check if user has admin, manager, or staff role
        if (!has_role(['admin', 'manager', 'staff'])) {
            flash('error', 'You do not have permission to access this page');
            header('Location: ' . base_url());
            exit;
        }
    }

    /**
     * Display all customers
     */
    public function index() {
        // Get search query
        $search = input('search');

        // Get customers
        $customer_model = new CustomerModel();
        if ($search) {
            $customers = $customer_model->search($search);
        } else {
            $customers = $customer_model->getAllWithUsers();
        }

        // Render view
        $this->render('customers/index', [
            'customers' => $customers,
            'search' => $search
        ]);
    }

    /**
     * Display customer creation form
     */
    public function create() {
        // Render view
        $this->render('customers/create');
    }

    /**
     * Store new customer
     */
    public function store() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('customers/create'));
        }

        // Get form data
        $name = input('name');
        $email = input('email');
        $phone = input('phone');
        $address = input('address');
        $gender = input('gender');
        $date_of_birth = input('date_of_birth');
        $password = input('password');
        $confirm_password = input('confirm_password');

        // Validate form data
        if (empty($name) || empty($phone) || empty($password)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('customers/create'));
        }

        if ($password != $confirm_password) {
            flash('error', 'Passwords do not match');
            $this->redirect(base_url('customers/create'));
        }

        // Check if email already exists (only if email is provided)
        $user_model = new UserModel();
        if (!empty($email)) {
            $existing_user = $user_model->findByEmail($email);

            if ($existing_user) {
                flash('error', 'Email already exists');
                $this->redirect(base_url('customers/create'));
            }
        }

        // Create user
        // If email is empty, use a permanent placeholder email
        if (empty($email)) {
            $email = "<EMAIL>";

            // Check if this email already exists
            $existing_user = $user_model->findByEmail($email);

            if ($existing_user) {
                // If the placeholder email is already in use, append a number to make it unique
                $count = 1;
                while ($user_model->findByEmail("walking{$count}@customer.com")) {
                    $count++;
                }
                $email = "walking{$count}@customer.com";
            }
        }

        $user_data = [
            'name' => $name,
            'email' => $email,
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'role' => 'customer',
            'status' => 'active'
        ];

        $user_id = $user_model->create($user_data);

        if (!$user_id) {
            flash('error', 'Failed to create user');
            $this->redirect(base_url('customers/create'));
        }

        // Create customer
        $customer_model = new CustomerModel();
        $customer_data = [
            'user_id' => $user_id,
            'phone' => $phone,
            'address' => $address,
            'gender' => $gender,
            'date_of_birth' => $date_of_birth
        ];

        $customer_id = $customer_model->create($customer_data);

        if (!$customer_id) {
            // Delete user if customer creation fails
            $user_model->delete($user_id);
            flash('error', 'Failed to create customer');
            $this->redirect(base_url('customers/create'));
        }

        flash('success', 'Customer created successfully');
        $this->redirect(base_url('customers'));
    }

    /**
     * Display customer details
     */
    public function view($id) {
        // Get customer
        $customer_model = new CustomerModel();
        $customer = $customer_model->getWithUser($id);

        if (!$customer) {
            flash('error', 'Customer not found');
            $this->redirect(base_url('customers'));
        }

        // Ensure customer has a status field
        if (!isset($customer['status'])) {
            $customer['status'] = 'active'; // Default to active if not set
            error_log("Customer status not found for ID: $id, defaulting to 'active'");
        }

        // Get customer appointments
        $appointment_model = new AppointmentModel();
        $appointments = $appointment_model->getByCustomerId($id);

        // Get customer invoices
        $invoice_model = new InvoiceModel();
        $invoices = $invoice_model->getByCustomerId($id);

        // Get customer membership
        $membership_model = new MembershipModel();
        $membership = $membership_model->getCustomerMembership($id);

        // Debug information
        error_log("Customer data: " . json_encode($customer));

        // Render view
        $this->render('customers/view', [
            'customer' => $customer,
            'appointments' => $appointments,
            'invoices' => $invoices,
            'membership' => $membership
        ]);
    }

    /**
     * Display customer edit form
     */
    public function edit($id) {
        // Get customer
        $customer_model = new CustomerModel();
        $customer = $customer_model->getWithUser($id);

        if (!$customer) {
            flash('error', 'Customer not found');
            $this->redirect(base_url('customers'));
        }

        // Render view
        $this->render('customers/edit', [
            'customer' => $customer
        ]);
    }

    /**
     * Update customer
     */
    public function update($id) {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('customers/edit/' . $id));
        }

        // Get customer
        $customer_model = new CustomerModel();
        $customer = $customer_model->find($id);

        if (!$customer) {
            flash('error', 'Customer not found');
            $this->redirect(base_url('customers'));
        }

        // Get form data
        $name = input('name');
        $email = input('email');
        $phone = input('phone');
        $address = input('address');
        $gender = input('gender');
        $date_of_birth = input('date_of_birth');
        $status = input('status');
        $password = input('password');
        $confirm_password = input('confirm_password');

        // Debug information
        error_log('Customer update - Date of birth: ' . ($date_of_birth ? $date_of_birth : 'empty'));

        // Validate form data
        if (empty($name) || empty($email) || empty($phone)) {
            flash('error', 'Please fill all required fields');
            $this->redirect(base_url('customers/edit/' . $id));
        }

        // Check if email already exists (excluding this user)
        $user_model = new UserModel();
        $existing_user = $user_model->findByEmail($email, $customer['user_id']);

        if ($existing_user) {
            // Log the issue for debugging
            error_log("Email validation failed: $email already exists for user ID: " . $existing_user['id']);
            error_log("Current user ID: " . $customer['user_id']);

            flash('error', 'Email already exists for another user');
            $this->redirect(base_url('customers/edit/' . $id));
        }

        // Debug log
        error_log("Email validation passed for user ID: " . $customer['user_id'] . ", email: $email");

        // Update user
        $user_data = [
            'name' => $name,
            'email' => $email,
            'status' => $status
        ];

        // Update password if provided
        if (!empty($password)) {
            if ($password != $confirm_password) {
                flash('error', 'Passwords do not match');
                $this->redirect(base_url('customers/edit/' . $id));
            }

            $user_data['password'] = password_hash($password, PASSWORD_DEFAULT);
        }

        $user_model = new UserModel();
        $user_result = $user_model->update($customer['user_id'], $user_data);

        if (!$user_result) {
            flash('error', 'Failed to update user');
            $this->redirect(base_url('customers/edit/' . $id));
        }

        // Update customer
        $customer_data = [
            'phone' => $phone,
            'address' => $address,
            'gender' => $gender,
            'status' => $status // Make sure status is included in customer data
        ];

        // Always include date_of_birth field in the update data
        // If it's empty or invalid, set it to NULL
        if (!empty($date_of_birth) && $date_of_birth !== '0000-00-00' && strtotime($date_of_birth)) {
            $customer_data['date_of_birth'] = $date_of_birth;
            error_log('Setting date_of_birth to: ' . $date_of_birth);
        } else {
            // If date is empty or invalid, set it to NULL
            $customer_data['date_of_birth'] = null;
            error_log('Setting date_of_birth to NULL');
        }

        // Debug the data being sent to the database
        error_log('Customer update data: ' . json_encode($customer_data));

        // Ensure we're using a direct SQL query for date_of_birth to avoid any issues
        try {
            // First update other customer fields
            $customer_result = $customer_model->update($id, $customer_data);

            // Then update date_of_birth with a direct query to ensure it's handled correctly
            if ($customer_result) {
                $dob_query = "UPDATE customers SET date_of_birth = :dob WHERE id = :id";
                $dob_stmt = $customer_model->getDb()->prepare($dob_query);

                if (!empty($date_of_birth) && $date_of_birth !== '0000-00-00' && strtotime($date_of_birth)) {
                    $dob_stmt->bindParam(':dob', $date_of_birth);
                    error_log('Direct query: Setting date_of_birth to: ' . $date_of_birth);
                } else {
                    $null_value = null;
                    $dob_stmt->bindParam(':dob', $null_value, PDO::PARAM_NULL);
                    error_log('Direct query: Setting date_of_birth to NULL');
                }

                $dob_stmt->bindParam(':id', $id);
                $dob_result = $dob_stmt->execute();
                error_log('Direct date_of_birth update result: ' . ($dob_result ? 'Success' : 'Failed'));
            }
        } catch (PDOException $e) {
            error_log('Error updating date_of_birth: ' . $e->getMessage());
            // Continue with the rest of the update process even if this fails
        }

        try {
            // We've already updated the customer data above, so we don't need to do it again
            // Just check if the update was successful
            if (!$customer_result) {
                // Use the flash helper function with a specific error message
                flash('error', 'Failed to update customer information. Please try again.');
                error_log('Setting error flash message: Failed to update customer information');
                $this->redirect(base_url('customers/edit/' . $id));
                return; // Stop execution
            }
        } catch (PDOException $e) {
            // Log the error for debugging
            error_log('Customer update error: ' . $e->getMessage());

            // Create a user-friendly error message
            $errorMsg = 'Database error occurred while updating customer.';

            // Add more details for specific errors
            if (strpos($e->getMessage(), 'Incorrect date value') !== false) {
                $errorMsg = 'Invalid date format for Date of Birth. Please use YYYY-MM-DD format.';
            } else if (strpos($e->getMessage(), 'Data too long') !== false) {
                $errorMsg = 'One of the text fields exceeds the maximum allowed length.';
            }

            // Set the flash message
            flash('error', $errorMsg);
            error_log('Setting error flash message: ' . $errorMsg);
            $this->redirect(base_url('customers/edit/' . $id));
            return; // Stop execution
        } catch (Exception $e) {
            // Log the error for debugging
            error_log('General error: ' . $e->getMessage());

            // Set a generic error message
            flash('error', 'An unexpected error occurred. Please try again.');
            error_log('Setting error flash message: An unexpected error occurred');
            $this->redirect(base_url('customers/edit/' . $id));
            return; // Stop execution
        }

        flash('success', 'Customer updated successfully');
        $this->redirect(base_url('customers'));
    }

    /**
     * Delete customer
     */
    public function delete($id) {
        // Get customer
        $customer_model = new CustomerModel();
        $customer = $customer_model->find($id);

        if (!$customer) {
            flash('error', 'Customer not found');
            $this->redirect(base_url('customers'));
        }

        // Check if customer has appointments
        $appointment_model = new AppointmentModel();
        $appointments = $appointment_model->getByCustomerId($id);

        if (count($appointments) > 0) {
            flash('error', 'Cannot delete customer with appointments');
            $this->redirect(base_url('customers'));
        }

        // Check if customer has invoices
        $invoice_model = new InvoiceModel();
        $invoices = $invoice_model->getByCustomerId($id);

        if (count($invoices) > 0) {
            flash('error', 'Cannot delete customer with invoices');
            $this->redirect(base_url('customers'));
        }

        // Delete customer
        $customer_result = $customer_model->delete($id);

        if (!$customer_result) {
            flash('error', 'Failed to delete customer');
            $this->redirect(base_url('customers'));
        }

        // Delete user
        $user_model = new UserModel();
        $user_result = $user_model->delete($customer['user_id']);

        if (!$user_result) {
            flash('error', 'Failed to delete user');
            $this->redirect(base_url('customers'));
        }

        flash('success', 'Customer deleted successfully');
        $this->redirect(base_url('customers'));
    }

    /**
     * Import customers from CSV
     */
    public function import() {
        // Check if form is submitted
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            $this->redirect(base_url('customers'));
        }

        // Check if file is uploaded
        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] != UPLOAD_ERR_OK) {
            flash('error', 'Please select a CSV file');
            $this->redirect(base_url('customers'));
        }

        // Get file
        $file = $_FILES['csv_file']['tmp_name'];

        // Open file
        $handle = fopen($file, 'r');

        if (!$handle) {
            flash('error', 'Failed to open file');
            $this->redirect(base_url('customers'));
        }

        // Skip header row
        fgetcsv($handle);

        // Initialize counters
        $imported = 0;
        $updated = 0;
        $skipped = 0;

        // Get models
        $user_model = new UserModel();
        $customer_model = new CustomerModel();

        // Process rows
        while (($data = fgetcsv($handle)) !== false) {
            // Check if row has enough columns
            if (count($data) < 5) {
                $skipped++;
                continue;
            }

            // Get data
            $name = trim($data[0]);
            $email = trim($data[1]);
            $phone = trim($data[2]);
            $address = trim($data[3]);
            $gender = trim($data[4]);
            $date_of_birth = isset($data[5]) ? trim($data[5]) : null;

            // Validate data
            if (empty($name) || empty($email) || empty($phone)) {
                $skipped++;
                continue;
            }

            // Check if email already exists
            $existing_user = $user_model->findByEmail($email);

            if ($existing_user) {
                // Update existing user and customer
                $user_data = [
                    'name' => $name,
                    'status' => 'active'
                ];

                $user_result = $user_model->update($existing_user['id'], $user_data);

                // Get existing customer
                $existing_customer = $customer_model->findByUserId($existing_user['id']);

                if ($existing_customer) {
                    // Update customer data
                    $customer_data = [
                        'phone' => $phone,
                        'address' => $address,
                        'gender' => $gender,
                        'date_of_birth' => $date_of_birth
                    ];

                    $customer_result = $customer_model->update($existing_customer['id'], $customer_data);

                    if ($customer_result) {
                        $updated++;
                    } else {
                        $skipped++;
                    }
                } else {
                    $skipped++;
                }

                continue;
            }

            // Create new user
            $user_data = [
                'name' => $name,
                'email' => $email,
                'password' => password_hash(random_string(8), PASSWORD_DEFAULT),
                'role' => 'customer',
                'status' => 'active'
            ];

            $user_id = $user_model->create($user_data);

            if (!$user_id) {
                $skipped++;
                continue;
            }

            // Create customer
            $customer_data = [
                'user_id' => $user_id,
                'phone' => $phone,
                'address' => $address,
                'gender' => $gender,
                'date_of_birth' => $date_of_birth
            ];

            $customer_id = $customer_model->create($customer_data);

            if (!$customer_id) {
                // Delete user if customer creation fails
                $user_model->delete($user_id);
                $skipped++;
                continue;
            }

            $imported++;
        }

        // Close file
        fclose($handle);

        // Show result
        flash('success', "Imported $imported customers. Updated $updated customers. Skipped $skipped rows.");
        $this->redirect(base_url('customers'));
    }

    /**
     * Export customers to CSV
     */
    public function export() {
        // Check if user is logged in and has admin or manager role
        if (!is_logged_in()) {
            flash('error', 'Please login to access this page');
            header('Location: ' . base_url('login'));
            exit;
        }

        if (!has_role(['admin', 'manager', 'staff'])) {
            flash('error', 'You do not have permission to access this page');
            header('Location: ' . base_url());
            exit;
        }

        // Get customers
        $customer_model = new CustomerModel();
        $customers = $customer_model->getAllWithUsers();

        // Set headers
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="customers.csv"');

        // Open output stream
        $output = fopen('php://output', 'w');

        // Add header row
        fputcsv($output, ['Name', 'Email', 'Phone', 'Address', 'Gender', 'Date of Birth', 'Status']);

        // Add data rows
        foreach ($customers as $customer) {
            fputcsv($output, [
                $customer['name'],
                $customer['email'],
                $customer['phone'],
                $customer['address'],
                $customer['gender'],
                $customer['date_of_birth'],
                $customer['status']
            ]);
        }

        // Close output stream
        fclose($output);
        exit;
    }
}




