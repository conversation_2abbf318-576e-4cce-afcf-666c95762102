<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-money-check-alt"></i> Payroll Management</h1>
        <a href="<?= base_url('payroll/create') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Payroll
        </a>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <?php if (empty($payroll)): ?>
                <div class="text-center py-5">
                    <img src="<?= base_url('assets/images/empty-payroll.svg') ?>" alt="No Payroll" style="width: 120px; opacity: 0.5;" class="mb-3">
                    <h5>No Payroll Records Found</h5>
                    <p class="text-muted">You haven't created any payroll records yet.</p>
                    <a href="<?= base_url('payroll/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create First Payroll
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%">#</th>
                                <th>Staff</th>
                                <th>Pay Period</th>
                                <th>Basic Salary</th>
                                <th>Commission</th>
                                <th>Net Salary</th>
                                <th>Payment Date</th>
                                <th>Status</th>
                                <th width="15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payroll as $index => $record): ?>
                                <tr>
                                    <td><?= $index + 1 ?></td>
                                    <td>
                                        <div class="fw-bold"><?= $record['staff_name'] ?></div>
                                        <div class="small text-muted">ID: <?= $record['staff_id'] ?></div>
                                    </td>
                                    <td>
                                        <?= format_date($record['pay_period_start']) ?> to <?= format_date($record['pay_period_end']) ?>
                                    </td>
                                    <td><?= format_currency($record['basic_salary']) ?></td>
                                    <td><?= format_currency($record['commission_amount']) ?></td>
                                    <td class="fw-bold"><?= format_currency($record['net_salary']) ?></td>
                                    <td><?= format_date($record['payment_date']) ?></td>
                                    <td>
                                        <?php if ($record['status'] == 'paid'): ?>
                                            <span class="badge bg-success">Paid</span>
                                        <?php elseif ($record['status'] == 'pending'): ?>
                                            <span class="badge bg-warning">Pending</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary"><?= ucfirst($record['status']) ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= base_url('payroll/view/' . $record['id']) ?>" class="btn btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?= base_url('payroll/edit/' . $record['id']) ?>" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?= base_url('payroll/print/' . $record['id']) ?>" class="btn btn-outline-info" title="Print" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <a href="<?= base_url('payroll/delete/' . $record['id']) ?>" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this payroll record? This action cannot be undone.')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
