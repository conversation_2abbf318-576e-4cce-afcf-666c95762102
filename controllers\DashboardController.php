<?php
/**
 * Dashboard Controller
 */
class DashboardController extends Controller {
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!is_logged_in()) {
            flash('error', 'Please login to access the dashboard');
            header('Location: ' . base_url('login'));
            exit;
        }

        // Check if user has admin, manager, or staff role
        if (!has_role(['admin', 'manager', 'staff'])) {
            flash('error', 'You do not have permission to access the dashboard');
            header('Location: ' . base_url());
            exit;
        }
    }

    /**
     * Dashboard index
     */
    public function index() {
        // Get today's appointments
        $appointment_model = new AppointmentModel();
        $today_appointments = $appointment_model->getTodayAppointments();

        // Get today's sales
        $invoice_model = new InvoiceModel();
        $today_sales = $invoice_model->getTodaySales();

        // Get low stock products
        $product_model = new ProductModel();
        $low_stock_products = $product_model->getLowStockProducts();

        // Get recent customers
        $customer_model = new CustomerModel();
        // Fallback if method doesn't exist yet
        $recent_customers = method_exists($customer_model, 'getRecentCustomers') ?
            $customer_model->getRecentCustomers(5) : [];

        // Get sales statistics
        $sales_stats = $invoice_model->getSalesStatistics();

        // Get appointment statistics
        $appointment_stats = $appointment_model->getAppointmentStatistics();

        // Render dashboard view
        $this->render('dashboard/index', [
            'today_appointments' => $today_appointments,
            'today_sales' => $today_sales,
            'low_stock_products' => $low_stock_products,
            'recent_customers' => $recent_customers,
            'sales_stats' => $sales_stats,
            'appointment_stats' => $appointment_stats
        ]);
    }
}
