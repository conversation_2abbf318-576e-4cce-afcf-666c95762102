<div class="content-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><i class="fas fa-chart-line"></i> Reports Dashboard</h1>
    </div>

    <!-- Date Range Filter -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?= base_url('reports') ?>" method="get" class="row align-items-end">
                <div class="col-md-4 mb-2">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $start_date ?>">
                </div>
                <div class="col-md-4 mb-2">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $end_date ?>">
                </div>
                <div class="col-md-4 mb-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i> Apply Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <!-- Reports Navigation -->
        <div class="col-md-3 mb-4">
            <?php include 'partials/reports_nav.php'; ?>
        </div>
        
        <!-- Reports Content -->
        <div class="col-md-9">
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Sales</h6>
                                    <h4 class="mb-0"><?= format_currency($sales_report['totals']['total_amount']) ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-dollar-sign text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Invoices</h6>
                                    <h4 class="mb-0"><?= $sales_report['totals']['invoice_count'] ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-file-invoice text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="text-muted mb-1">Total Appointments</h6>
                                    <h4 class="mb-0"><?= $appointment_report['total'] ?></h4>
                                </div>
                                <div class="bg-light rounded-circle p-3">
                                    <i class="fas fa-calendar-check text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sales Chart -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Sales Overview</h5>
                    <a href="<?= base_url('reports/sales') ?>" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-external-link-alt me-1"></i> Detailed Report
                    </a>
                </div>
                <div class="card-body">
                    <canvas id="salesChart" height="250"></canvas>
                </div>
            </div>
            
            <!-- Top Services & Staff Performance -->
            <div class="row mb-4">
                <div class="col-md-6 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Top Services</h5>
                            <a href="<?= base_url('reports/services') ?>" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-external-link-alt me-1"></i> View All
                            </a>
                        </div>
                        <div class="card-body">
                            <?php if (empty($service_sales_report['data'])): ?>
                                <div class="text-center py-4">
                                    <p class="text-muted mb-0">No service data available for the selected period.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Service</th>
                                                <th class="text-end">Count</th>
                                                <th class="text-end">Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $top_services = array_slice($service_sales_report['data'], 0, 5);
                                            foreach ($top_services as $service): 
                                            ?>
                                                <tr>
                                                    <td><?= $service['service_name'] ?></td>
                                                    <td class="text-end"><?= $service['service_count'] ?></td>
                                                    <td class="text-end"><?= format_currency($service['total_amount']) ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Staff Performance</h5>
                            <a href="<?= base_url('reports/staff') ?>" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-external-link-alt me-1"></i> View All
                            </a>
                        </div>
                        <div class="card-body">
                            <?php if (empty($staff_performance_report['data'])): ?>
                                <div class="text-center py-4">
                                    <p class="text-muted mb-0">No staff performance data available for the selected period.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Staff</th>
                                                <th class="text-end">Services</th>
                                                <th class="text-end">Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $top_staff = array_slice($staff_performance_report['data'], 0, 5);
                                            foreach ($top_staff as $staff): 
                                            ?>
                                                <tr>
                                                    <td><?= $staff['staff_name'] ?></td>
                                                    <td class="text-end"><?= $staff['service_count'] ?></td>
                                                    <td class="text-end"><?= format_currency($staff['service_amount']) ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Appointment Status -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Appointment Status</h5>
                    <a href="<?= base_url('reports/appointments') ?>" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-external-link-alt me-1"></i> Detailed Report
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($appointment_report['by_status'])): ?>
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No appointment data available for the selected period.</p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <div class="col-md-6">
                                <canvas id="appointmentChart" height="200"></canvas>
                            </div>
                            <div class="col-md-6">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Status</th>
                                                <th class="text-end">Count</th>
                                                <th class="text-end">Percentage</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            foreach ($appointment_report['by_status'] as $status): 
                                                $percentage = ($status['appointment_count'] / $appointment_report['total']) * 100;
                                            ?>
                                                <tr>
                                                    <td>
                                                        <?php if ($status['status'] == 'completed'): ?>
                                                            <span class="badge bg-success">Completed</span>
                                                        <?php elseif ($status['status'] == 'confirmed'): ?>
                                                            <span class="badge bg-primary">Confirmed</span>
                                                        <?php elseif ($status['status'] == 'pending'): ?>
                                                            <span class="badge bg-warning">Pending</span>
                                                        <?php elseif ($status['status'] == 'cancelled'): ?>
                                                            <span class="badge bg-danger">Cancelled</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary"><?= ucfirst($status['status']) ?></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="text-end"><?= $status['appointment_count'] ?></td>
                                                    <td class="text-end"><?= number_format($percentage, 1) ?>%</td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sales Chart
    const salesData = <?= json_encode($sales_report['data']) ?>;
    const salesLabels = salesData.map(item => item.period);
    const salesValues = salesData.map(item => item.total_amount);
    
    const salesCtx = document.getElementById('salesChart').getContext('2d');
    new Chart(salesCtx, {
        type: 'line',
        data: {
            labels: salesLabels,
            datasets: [{
                label: 'Sales',
                data: salesValues,
                backgroundColor: 'rgba(52, 152, 219, 0.2)',
                borderColor: 'rgba(52, 152, 219, 1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '<?= CURRENCY_SYMBOL ?>' + value;
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Sales: <?= CURRENCY_SYMBOL ?>' + context.raw;
                        }
                    }
                }
            }
        }
    });
    
    // Appointment Chart
    const appointmentData = <?= json_encode($appointment_report['by_status']) ?>;
    const appointmentLabels = appointmentData.map(item => item.status.charAt(0).toUpperCase() + item.status.slice(1));
    const appointmentValues = appointmentData.map(item => item.appointment_count);
    const appointmentColors = appointmentData.map(item => {
        switch(item.status) {
            case 'completed': return 'rgba(46, 204, 113, 0.8)';
            case 'confirmed': return 'rgba(52, 152, 219, 0.8)';
            case 'pending': return 'rgba(241, 196, 15, 0.8)';
            case 'cancelled': return 'rgba(231, 76, 60, 0.8)';
            default: return 'rgba(149, 165, 166, 0.8)';
        }
    });
    
    if (appointmentData.length > 0) {
        const appointmentCtx = document.getElementById('appointmentChart').getContext('2d');
        new Chart(appointmentCtx, {
            type: 'doughnut',
            data: {
                labels: appointmentLabels,
                datasets: [{
                    data: appointmentValues,
                    backgroundColor: appointmentColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    }
});
</script>
